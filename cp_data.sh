#!/bin/bash

# Root of your project
PROJECT_ROOT="/Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis"
cd "$PROJECT_ROOT" || exit 1

mkdir -p data

# Loop through all `data` folders except the top-level and anything under `.venv`
find . -type d -name data ! -path './data' ! -path './.venv/*' | while read -r subdir; do
    echo "Processing: $subdir"

    # Get parent dir
    parent_dir=$(dirname "$subdir")

    # Compute relative path from root using python (works on macOS)
    relative_path=$(python3 -c "import os.path; print(os.path.relpath('$parent_dir', '.'))")

    # Create target directory inside top-level data
    mkdir -p "data/$relative_path"

    # Copy contents from inner data folder
    cp -R "$subdir/"* "data/$relative_path/"
done

