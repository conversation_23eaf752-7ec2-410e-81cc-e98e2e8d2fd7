{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Automatic Labeling from IFC Metadata for ML Training\n", "\n", "This notebook creates labeled training patches from ground-segmented drone point cloud data using IFC pile metadata. It generates training data ready for ML classification models like PointNet++ and DGCNN.\n", "\n", "**Use Case**: Generate labeled training patches for pile detection and classification using IFC geometry and metadata.\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: July 2025  \n", "**Project**: As-Built Foundation Analysis"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Overview\n", "\n", "### What This Notebook Does:\n", "1. **Load Ground-Segmented Point Cloud**: Import non-ground points from CSF/PMF/RANSAC processing\n", "2. **Load IFC Pile Metadata**: Extract pile locations, types, and pile numbers from IFC files\n", "3. **Create Training Patches**: Extract point cloud patches around pile locations\n", "4. **Generate Labels**: Create pile/non-pile labels with pile numbers for classification\n", "5. **Save Labeled Data**: Export training patches in ML-ready formats (NPZ, PLY)\n", "\n", "### Key Benefits:\n", "- **Automated Patch Creation**: No manual annotation required\n", "- **Ground-Segmented Data**: Uses clean non-ground points for better pile detection\n", "- **Pile Number Labels**: Includes pile identification numbers from IFC metadata\n", "- **ML-Ready Format**: Outputs compatible with PointNet++/DGCNN training pipelines\n", "- **Scalable**: Can process large datasets efficiently"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import numpy as np\n", "import pandas as pd\n", "import open3d as o3d\n", "import matplotlib.pyplot as plt\n", "from pathlib import Path\n", "import json\n", "from scipy.spatial import cKDTree\n", "from scipy.spatial.distance import cdist\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "import logging\n", "from datetime import datetime\n", "import os\n", "import random\n", "from sklearn.model_selection import train_test_split"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["# Papermill parameters cell\n", "site_name = \"trino_enel\"\n", "project_type = \"ENEL\"\n", "ground_segmentation_method = \"csf\"  # Options: \"csf\", \"pmf\", \"ransac\", \"ransac_pmf\"\n", "\n", "# Search radius for XY-only patch extraction\n", "search_radius=10.0\n", "\n", "# Training patch parameters\n", "patch_radius = 2.0  # Radius around pile center for patch extraction (meters)\n", "min_points_per_patch = 50  # Minimum points required for a valid training patch\n", "max_points_per_patch = 2048  # Maximum points per patch (for PointNet++ compatibility)\n", "negative_sample_ratio = 2.0  # Ratio of negative to positive samples\n", "\n", "# Set up paths\n", "base_path = Path('../../..')  # Adjust to your project root\n", "data_path = base_path / 'data'\n", "processed_path = data_path / 'processed' / site_name\n", "output_path = data_path / 'output_runs' / 'labeled_training_data' / f\"{site_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}\"\n", "output_path.mkdir(parents=True, exist_ok=True)\n", "\n", "# Data quality parameters\n", "z_filter_min = 0.5  # Minimum height above ground for pile detection (meters)\n", "z_filter_max = 4.0   # Maximum height above ground for pile detection (meters)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-20 10:55:30,390 - INFO - Auto-labeling from IFC for ML Training - Ready!\n", "2025-07-20 10:55:30,390 - INFO - Site: trino_enel\n", "2025-07-20 10:55:30,391 - INFO - Ground segmentation method: csf\n", "2025-07-20 10:55:30,391 - INFO - Data path: ../../../data\n", "2025-07-20 10:55:30,391 - INFO - Output path: ../../../data/output_runs/labeled_training_data/trino_enel_20250720_105530\n", "2025-07-20 10:55:30,391 - INFO - Patch radius: 2.0m\n", "2025-07-20 10:55:30,392 - INFO - Min points per patch: 50\n", "2025-07-20 10:55:30,392 - INFO - Max points per patch: 2048\n"]}], "source": ["# Configure logging\n", "logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')\n", "logger = logging.getLogger(__name__)\n", "\n", "logger.info(\"Auto-labeling from IFC for ML Training - Ready!\")\n", "logger.info(f\"Site: {site_name}\")\n", "logger.info(f\"Ground segmentation method: {ground_segmentation_method}\")\n", "logger.info(f\"Data path: {data_path}\")\n", "logger.info(f\"Output path: {output_path}\")\n", "logger.info(f\"Patch radius: {patch_radius}m\")\n", "logger.info(f\"Min points per patch: {min_points_per_patch}\")\n", "logger.info(f\"Max points per patch: {max_points_per_patch}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Load Ground-Segmented Point Cloud Data\n", "\n", "Load the non-ground points from the ground segmentation processing."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-20 10:55:30,528 - INFO - Loaded 508,032 points from trino_enel_gcp_aligned.ply\n", "2025-07-20 10:55:30,530 - INFO - Bounds - X:[435222.1,436796.9] Y:[5010809.4,5012545.1] Z:[-2.4,13.3]\n", "2025-07-20 10:55:30,530 - INFO - Point cloud lacks RGB colors\n"]}], "source": ["import open3d as o3d\n", "import numpy as np\n", "import laspy\n", "from pathlib import Path\n", "\n", "# ==== Set your point cloud file ====\n", "# point_file = Path(processed_path) / 'ground_segmentation' / ground_segmentation_method / f\"{site_name}_nonground.ply\"\n", "#point_file = Path(\"../../../data/raw/trino_enel/pointcloud/Trino_Fly_2_Shifted.las\")  # or .ply\n", "#point_file = Path(\"../../../data/processed/trino_enel/denoising/trino_enel_denoised.ply\")\n", "\n", "point_file = Path(\"../../../data/processed/gcp_alignment/ransac_pmf/trino_enel_gcp_aligned.ply\")\n", "# ==== Load and process ====\n", "try:\n", "    if point_file.suffix.lower() == \".ply\":\n", "        pcd = o3d.io.read_point_cloud(str(point_file))\n", "        points = np.asarray(pcd.points)\n", "        colors = np.asarray(pcd.colors) if pcd.has_colors() else None\n", "\n", "    elif point_file.suffix.lower() == \".las\":\n", "        las = laspy.read(str(point_file))\n", "        points = las.xyz  # Efficient and clean\n", "        colors = None  # Skip color for memory efficiency\n", "\n", "        # TODO: Handle color only if needed\n", "        # Convert to Open3D point cloud\n", "        pcd = o3d.geometry.PointCloud()\n", "        pcd.points = o3d.utility.Vector3dVector(points)\n", "\n", "    else:\n", "        raise ValueError(f\"Unsupported file format: {point_file.suffix}\")\n", "\n", "    logger.info(f\"Loaded {len(points):,} points from {point_file.name}\")\n", "    logger.info(f\"Bounds - X:[{points[:, 0].min():.1f},{points[:, 0].max():.1f}] \"\n", "                f\"Y:[{points[:, 1].min():.1f},{points[:, 1].max():.1f}] \"\n", "                f\"Z:[{points[:, 2].min():.1f},{points[:, 2].max():.1f}]\")\n", "    logger.info(f\"Point cloud {'has' if colors is not None else 'lacks'} RGB colors\")\n", "\n", "except Exception as e:\n", "    logger.error(f\"Failed to load point cloud: {e}\")\n", "    raise\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Load IFC Pile Metadata\n", "\n", "Load the extracted IFC metadata containing pile information."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-20 10:55:30,558 - INFO - Loaded IFC pile metadata: 14460 piles\n", "2025-07-20 10:55:30,558 - INFO - Columns: ['GlobalId', 'Name', 'Type', 'Description', 'Tag', 'Site_Latitude', 'Site_Longitude', 'Site_Elevation', 'X', 'Y', 'Z', 'GeometryExtracted', 'Longitude', 'Latitude']\n", "2025-07-20 10:55:30,558 - INFO - Sample IFC pile metadata:\n", "2025-07-20 10:55:30,563 - INFO - Found 14460 unique pile numbers\n", "2025-07-20 10:55:30,564 - INFO - Pile number range: 952577 to 1251068\n", "2025-07-20 10:55:30,564 - INFO - IFC Coordinate Ranges:\n", "2025-07-20 10:55:30,565 - INFO -   - X: [435267.20, 436719.95]\n", "2025-07-20 10:55:30,565 - INFO -   - Y: [5010900.71, 5012462.41]\n", "2025-07-20 10:55:30,566 - INFO -   - Z: [154.99, 159.52]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["                 GlobalId                                        Name  \\\n", "0  1u7AZf3On2lwsljDdawZWm  TRPL_Tracker Pile:TRPL_Tracker Pile:952577   \n", "1  1u7AZf3On2lwsljDdawZWp  TRPL_Tracker Pile:TRPL_Tracker Pile:952578   \n", "2  1u7AZf3On2lwsljDdawZWo  TRPL_Tracker Pile:TRPL_Tracker Pile:952579   \n", "3  1u7AZf3On2lwsljDdawZWr  TRPL_Tracker Pile:TRPL_Tracker Pile:952580   \n", "4  1u7AZf3On2lwsljDdawZWq  TRPL_Tracker Pile:TRPL_Tracker Pile:952581   \n", "\n", "        Type  Description     Tag  Site_Latitude  Site_Longitude  \\\n", "0  IfcColumn          NaN  952577         45.257           8.184   \n", "1  IfcColumn          NaN  952578         45.257           8.184   \n", "2  IfcColumn          NaN  952579         45.257           8.184   \n", "3  IfcColumn          NaN  952580         45.257           8.184   \n", "4  IfcColumn          NaN  952581         45.257           8.184   \n", "\n", "   Site_Elevation           X            Y        Z  GeometryExtracted  \\\n", "0             NaN  435751.684  5012179.151  158.688               True   \n", "1             NaN  435751.684  5012187.444  158.688               True   \n", "2             NaN  435751.684  5012195.736  158.688               True   \n", "3             NaN  435751.684  5012170.859  158.688               True   \n", "4             NaN  435751.684  5012204.029  158.688               True   \n", "\n", "   Longitude  Latitude  \n", "0      8.181     45.26  \n", "1      8.181     45.26  \n", "2      8.181     45.26  \n", "3      8.181     45.26  \n", "4      8.181     45.26  \n"]}], "source": ["# Load IFC pile metadata\n", "ifc_metadata_path = processed_path / 'ifc_metadata' / 'GRE.EEC.S.00.IT.P.14353.00.265_piles.csv'\n", "\n", "try:\n", "    ifc_metadata = pd.read_csv(ifc_metadata_path)\n", "    logger.info(f\"Loaded IFC pile metadata: {len(ifc_metadata)} piles\")\n", "    logger.info(f\"Columns: {list(ifc_metadata.columns)}\")\n", "    \n", "    # Display sample data\n", "    logger.info(\"Sample IFC pile metadata:\")\n", "    print(ifc_metadata.head())\n", "    \n", "    # Extract pile numbers from Tag column\n", "    if 'Tag' in ifc_metadata.columns:\n", "        pile_numbers = ifc_metadata['Tag'].unique()\n", "        logger.info(f\"Found {len(pile_numbers)} unique pile numbers\")\n", "        logger.info(f\"Pile number range: {pile_numbers.min()} to {pile_numbers.max()}\")\n", "    \n", "    # Check coordinate ranges\n", "    coord_cols = ['X', 'Y', 'Z']\n", "    if all(col in ifc_metadata.columns for col in coord_cols):\n", "        logger.info(f\"IFC Coordinate Ranges:\")\n", "        for col in coord_cols:\n", "            min_val, max_val = ifc_metadata[col].min(), ifc_metadata[col].max()\n", "            logger.info(f\"  - {col}: [{min_val:.2f}, {max_val:.2f}]\")\n", "    \n", "except FileNotFoundError:\n", "    logger.error(f\"IFC metadata file not found: {ifc_metadata_path}\")\n", "    logger.error(\"Please run IFC metadata extraction first.\")\n", "    raise"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(14460, 3)\n", "(14460,)\n"]}], "source": ["# Validate IFC metadata\n", "# Extract pile coordinates from IFC metadata\n", "coord_cols = ['X', 'Y', 'Z']\n", "if not all(col in ifc_metadata.columns for col in coord_cols):\n", "    raise ValueError(f\"IFC metadata must contain columns: {coord_cols}\")\n", "    \n", "pile_coords = ifc_metadata[coord_cols].values\n", "pile_numbers = ifc_metadata['Tag'].values if 'Tag' in ifc_metadata.columns else np.arange(len(pile_coords))\n", "print(pile_coords.shape)\n", "print(pile_numbers.shape)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Training Patch Creation Functions\n", "\n", "Define functions to create training patches around pile locations."]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["def detect_coordinate_mismatch(points, pile_coords, threshold=50.0):\n", "    # Analyze Z-coordinates\n", "    drone_z_range = points[:, 2].max() - points[:, 2].min()\n", "    ifc_z_range = pile_coords[:, 2].max() - pile_coords[:, 2].min()\n", "    \n", "    drone_z_mean = points[:, 2].mean()\n", "    ifc_z_mean = pile_coords[:, 2].mean()\n", "    \n", "    z_separation = abs(drone_z_mean - ifc_z_mean)\n", "    \n", "    logger.info(f\"Z-Coordinate Analysis:\")\n", "    logger.info(f\"  Drone Z: {points[:, 2].min():.1f} - {points[:, 2].max():.1f} (range: {drone_z_range:.1f}m)\")\n", "    logger.info(f\"  IFC Z:   {pile_coords[:, 2].min():.1f} - {pile_coords[:, 2].max():.1f} (range: {ifc_z_range:.1f}m)\")\n", "    logger.info(f\"  Mean separation: {z_separation:.1f}m\")\n", "    \n", "    has_mismatch = z_separation > threshold\n", "    z_offset = ifc_z_mean - drone_z_mean\n", "    \n", "    if has_mismatch:\n", "        logger.info(f\"DETECTED: Major Z-coordinate mismatch (likely absolute vs relative)\")\n", "    else:\n", "        logger.info(f\"Z-coordinates appear to be in same reference system\")\n", "    \n", "    return has_mismatch, z_offset"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["def extract_pile_patch_xy_only(points, pile_center, radius, min_points=50, max_points=2048):\n", "    # Calculate distances to pile center (only X, Y for cylindrical search)\n", "    distances_2d = np.sqrt((points[:, 0] - pile_center[0])**2 + \n", "                          (points[:, 1] - pile_center[1])**2)\n", "    \n", "    # Find points within radius\n", "    patch_mask = distances_2d <= radius\n", "    patch_indices = np.where(patch_mask)[0]\n", "    patch_points = points[patch_mask]\n", "    \n", "    # Check if patch has enough points\n", "    is_valid = len(patch_points) >= min_points\n", "    \n", "    if is_valid and len(patch_points) > max_points:\n", "        # Randomly sample to max_points\n", "        sample_indices = np.random.choice(len(patch_points), max_points, replace=False)\n", "        patch_points = patch_points[sample_indices]\n", "        patch_indices = patch_indices[sample_indices]\n", "    \n", "    # Convert to relative coordinates (centered on pile XY, keep original Z)\n", "    if is_valid:\n", "        # Use drone point cloud's median Z as reference instead of IFC Z\n", "        pile_center_corrected = np.array([pile_center[0], pile_center[1], np.median(patch_points[:, 2])])\n", "        patch_points_relative = patch_points - pile_center_corrected\n", "    else:\n", "        patch_points_relative = patch_points\n", "    \n", "    return patch_points_relative, patch_indices, is_valid"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-20 10:55:30,585 - INFO - Z-Coordinate Analysis:\n", "2025-07-20 10:55:30,586 - INFO -   Drone Z: -2.4 - 13.3 (range: 15.8m)\n", "2025-07-20 10:55:30,586 - INFO -   IFC Z:   155.0 - 159.5 (range: 4.5m)\n", "2025-07-20 10:55:30,587 - INFO -   Mean separation: 157.0m\n", "2025-07-20 10:55:30,587 - INFO - DETECTED: Major Z-coordinate mismatch (likely absolute vs relative)\n", "2025-07-20 10:55:30,587 - INFO - Using XY-only labeling to handle Z-coordinate mismatch\n", "2025-07-20 10:55:30,587 - INFO - Using specified search radius: 2.0m\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Z offset: 156.97m\n", "extract_pile_patch_xy_only\n"]}], "source": ["# Detect Z-coordinate mismatch\n", "has_z_mismatch, z_offset = detect_coordinate_mismatch(points, pile_coords)\n", "print(f\"Z offset: {z_offset:.2f}m\")\n", "\n", "if has_z_mismatch:\n", "    logger.info(\"Using XY-only labeling to handle Z-coordinate mismatch\")\n", "    logger.info(f\"Using specified search radius: {patch_radius}m\")\n", "else:\n", "    logger.info(\"Using full XYZ labeling with compatible coordinate systems\")\n", "\n", "extract_function = extract_pile_patch_xy_only  # Still use XY-only for consistency\n", "print (extract_function.__name__)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "from scipy.spatial import cKDTree\n", "import logging\n", "\n", "def create_xy_only_labeling_improved(drone_points, pile_coords, search_radius=8.0, min_points=20, buffer_factor=2.0):\n", "    logging.basicConfig(level=logging.INFO)\n", "    logger = logging.getLogger(__name__)\n", "\n", "    # Input validation\n", "    if not isinstance(drone_points, np.ndarray) or drone_points.shape[1] < 2:\n", "        raise ValueError(\"drone_points must be a numpy array with at least X, Y columns\")\n", "    if not isinstance(pile_coords, np.ndarray) or pile_coords.shape[1] < 2:\n", "        raise ValueError(\"pile_coords must be a numpy array with at least X, Y columns\")\n", "    if search_radius <= 0 or min_points < 0:\n", "        raise ValueError(\"search_radius must be positive and min_points non-negative\")\n", "\n", "    logger.info(f\"Improved XY-only labeling with {search_radius}m search radius...\")\n", "    logger.info(\"Ignoring Z-coordinates due to coordinate system mismatch\")\n", "\n", "    # Get point cloud spatial bounds\n", "    x_min, x_max = drone_points[:, 0].min(), drone_points[:, 0].max()\n", "    y_min, y_max = drone_points[:, 1].min(), drone_points[:, 1].max()\n", "    logger.info(f\"Point cloud bounds: X[{x_min:.1f}, {x_max:.1f}], Y[{y_min:.1f}, {y_max:.1f}]\")\n", "\n", "    # Filter pile coordinates\n", "    buffer = search_radius * buffer_factor\n", "    pile_mask = (\n", "        (pile_coords[:, 0] >= x_min - buffer) & (pile_coords[:, 0] <= x_max + buffer) &\n", "        (pile_coords[:, 1] >= y_min - buffer) & (pile_coords[:, 1] <= y_max + buffer)\n", "    )\n", "    filtered_pile_coords = pile_coords[pile_mask]\n", "    filtered_pile_indices = np.where(pile_mask)[0]\n", "    logger.info(f\"Filtered piles: {len(filtered_pile_coords)}/{len(pile_coords)} within survey area\")\n", "\n", "    # Use only XY coordinates\n", "    drone_xy = drone_points[:, :2]\n", "    pile_xy = filtered_pile_coords[:, :2]\n", "    \n", "    # Build KD-tree\n", "    tree = cKDTree(drone_xy)\n", "    \n", "    # Initialize labels\n", "    labels = np.zeros(len(drone_points), dtype=int)\n", "    pile_assignments = np.full(len(drone_points), -1, dtype=int)\n", "    distances = np.full(len(drone_points), np.inf)  # Track distance to assigned pile\n", "    \n", "    labeled_piles = 0\n", "    total_pile_points = 0\n", "    pile_details = []\n", "    \n", "    logger.info(f\"Searching for points around {len(filtered_pile_coords)} pile XY locations...\")\n", "    \n", "    for i, pile_coord in enumerate(filtered_pile_coords):\n", "        pile_xy_coord = pile_coord[:2]\n", "        original_pile_idx = filtered_pile_indices[i]\n", "        \n", "        # Find points within search radius\n", "        indices = tree.query_ball_point(pile_xy_coord, search_radius)\n", "        \n", "        if len(indices) >= min_points:\n", "            # Compute distances for these points\n", "            xy_distances = np.linalg.norm(drone_xy[indices] - pile_xy_coord, axis=1)\n", "            \n", "            # Assign points to closest pile\n", "            for idx, dist in zip(indices, xy_distances):\n", "                if dist < distances[idx]:  # Closer than previous assignment\n", "                    distances[idx] = dist\n", "                    pile_assignments[idx] = original_pile_idx\n", "                    labels[idx] = 1\n", "            \n", "            labeled_piles += 1\n", "            total_pile_points += len(indices)\n", "            \n", "            mean_xy_distance = np.mean(xy_distances)\n", "            pile_details.append({\n", "                'pile_id': original_pile_idx,\n", "                'xy_coordinates': pile_xy_coord.tolist(),\n", "                'point_count': len(indices),\n", "                'mean_xy_distance': mean_xy_distance,\n", "                'search_radius': search_radius\n", "            })\n", "            \n", "            if labeled_piles <= 5:\n", "                logger.info(f\"Pile {original_pile_idx}: {len(indices)} points, avg XY distance {mean_xy_distance:.1f}m\")\n", "        \n", "        if (i + 1) % max(1, len(filtered_pile_coords) // 10) == 0:\n", "            logger.info(f\"Processed {i + 1}/{len(filtered_pile_coords)} piles\")\n", "    \n", "    detection_rate_filtered = labeled_piles / len(filtered_pile_coords) * 100 if len(filtered_pile_coords) > 0 else 0\n", "    detection_rate_total = labeled_piles / len(pile_coords) * 100 if len(pile_coords) > 0 else 0\n", "    \n", "    logger.info(f\"Improved XY-only labeling complete:\")\n", "    logger.info(f\"  Labeled piles: {labeled_piles}/{len(filtered_pile_coords)} within survey area ({detection_rate_filtered:.1f}%)\")\n", "    logger.info(f\"  Total labeled: {labeled_piles}/{len(pile_coords)} of all piles ({detection_rate_total:.1f}%)\")\n", "    logger.info(f\"  Pile points: {total_pile_points:,}\")\n", "    logger.info(f\"  Background points: {len(drone_points) - total_pile_points:,}\")\n", "    logger.info(f\"  Pile coverage: {total_pile_points/len(drone_points)*100:.1f}% of total points\")\n", "    \n", "    return labels, pile_assignments, {\n", "        'total_piles': len(pile_coords),\n", "        'filtered_piles': len(filtered_pile_coords),\n", "        'labeled_piles': labeled_piles,\n", "        'total_pile_points': total_pile_points,\n", "        'detection_rate_filtered': detection_rate_filtered,\n", "        'detection_rate_total': detection_rate_total,\n", "        'pile_details': pile_details,\n", "        'search_radius_used': search_radius,\n", "        'method': 'xy_only_labeling_improved'\n", "    }"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "from scipy.spatial import cKDTree\n", "import logging\n", "\n", "def create_xy_only_labeling_improved(drone_points, pile_coords, search_radius=12.0, min_points=10, buffer_factor=2.0):\n", "    \"\"\"Improved XY-only labeling with spatial filtering and adaptive parameters.\"\"\"\n", "    logging.basicConfig(level=logging.INFO)\n", "    logger = logging.getLogger(__name__)\n", "\n", "    # Input validation\n", "    if not isinstance(drone_points, np.ndarray) or drone_points.shape[1] < 2:\n", "        raise ValueError(\"drone_points must be a numpy array with at least X, Y columns\")\n", "    if not isinstance(pile_coords, np.ndarray) or pile_coords.shape[1] < 2:\n", "        raise ValueError(\"pile_coords must be a numpy array with at least X, Y columns\")\n", "    if search_radius <= 0 or min_points < 0:\n", "        raise ValueError(\"search_radius must be positive and min_points non-negative\")\n", "\n", "    logger.info(f\"Improved XY-only labeling with {search_radius}m search radius...\")\n", "    logger.info(\"Ignoring Z-coordinates due to coordinate system mismatch\")\n", "\n", "    # Get point cloud spatial bounds\n", "    x_min, x_max = drone_points[:, 0].min(), drone_points[:, 0].max()\n", "    y_min, y_max = drone_points[:, 1].min(), drone_points[:, 1].max()\n", "    logger.info(f\"Point cloud bounds: X[{x_min:.1f}, {x_max:.1f}], Y[{y_min:.1f}, {y_max:.1f}]\")\n", "\n", "    # Filter pile coordinates\n", "    buffer = search_radius * buffer_factor\n", "    pile_mask = (\n", "        (pile_coords[:, 0] >= x_min - buffer) & (pile_coords[:, 0] <= x_max + buffer) &\n", "        (pile_coords[:, 1] >= y_min - buffer) & (pile_coords[:, 1] <= y_max + buffer)\n", "    )\n", "    filtered_pile_coords = pile_coords[pile_mask]\n", "    filtered_pile_indices = np.where(pile_mask)[0]\n", "    logger.info(f\"Filtered piles: {len(filtered_pile_coords)}/{len(pile_coords)} within survey area\")\n", "\n", "    # Use only XY coordinates\n", "    drone_xy = drone_points[:, :2]\n", "    pile_xy = filtered_pile_coords[:, :2]\n", "    \n", "    # Build KD-tree\n", "    tree = cKDTree(drone_xy)\n", "    \n", "    # Initialize labels\n", "    labels = np.zeros(len(drone_points), dtype=int)\n", "    pile_assignments = np.full(len(drone_points), -1, dtype=int)\n", "    distances = np.full(len(drone_points), np.inf)  # Track distance to assigned pile\n", "    \n", "    labeled_piles = 0\n", "    pile_details = []\n", "    \n", "    logger.info(f\"Searching for points around {len(filtered_pile_coords)} pile XY locations...\")\n", "    \n", "    for i, pile_coord in enumerate(filtered_pile_coords):\n", "        pile_xy_coord = pile_coord[:2]\n", "        original_pile_idx = filtered_pile_indices[i]\n", "        \n", "        # Find points within search radius\n", "        indices = tree.query_ball_point(pile_xy_coord, search_radius)\n", "        \n", "        if len(indices) >= min_points:\n", "            # Compute distances for these points\n", "            xy_distances = np.linalg.norm(drone_xy[indices] - pile_xy_coord, axis=1)\n", "            \n", "            # Assign points to closest pile\n", "            for idx, dist in zip(indices, xy_distances):\n", "                if dist < distances[idx]:  # Closer than previous assignment\n", "                    distances[idx] = dist\n", "                    pile_assignments[idx] = original_pile_idx\n", "                    labels[idx] = 1\n", "            \n", "            labeled_piles += 1\n", "            mean_xy_distance = np.mean(xy_distances)\n", "            pile_details.append({\n", "                'pile_id': original_pile_idx,\n", "                'xy_coordinates': pile_xy_coord.tolist(),\n", "                'point_count': len(indices),\n", "                'mean_xy_distance': mean_xy_distance,\n", "                'search_radius': search_radius\n", "            })\n", "            \n", "            if labeled_piles <= 5:\n", "                logger.info(f\"Pile {original_pile_idx}: {len(indices)} points, avg XY distance {mean_xy_distance:.1f}m\")\n", "        \n", "        if (i + 1) % max(1, len(filtered_pile_coords) // 10) == 0:\n", "            logger.info(f\"Processed {i + 1}/{len(filtered_pile_coords)} piles\")\n", "    \n", "    # Count unique pile points\n", "    total_pile_points = np.sum(labels == 1)\n", "    detection_rate_filtered = labeled_piles / len(filtered_pile_coords) * 100 if len(filtered_pile_coords) > 0 else 0\n", "    detection_rate_total = labeled_piles / len(pile_coords) * 100 if len(pile_coords) > 0 else 0\n", "    \n", "    logger.info(f\"Improved XY-only labeling complete:\")\n", "    logger.info(f\"  Labeled piles: {labeled_piles}/{len(filtered_pile_coords)} within survey area ({detection_rate_filtered:.1f}%)\")\n", "    logger.info(f\"  Total labeled: {labeled_piles}/{len(pile_coords)} of all piles ({detection_rate_total:.1f}%)\")\n", "    logger.info(f\"  Pile points: {total_pile_points:,}\")\n", "    logger.info(f\"  Background points: {len(drone_points) - total_pile_points:,}\")\n", "    logger.info(f\"  Pile coverage: {total_pile_points / len(drone_points) * 100:.1f}% of total points\")\n", "    \n", "    return labels, pile_assignments, {\n", "        'total_piles': len(pile_coords),\n", "        'filtered_piles': len(filtered_pile_coords),\n", "        'labeled_piles': labeled_piles,\n", "        'total_pile_points': total_pile_points,\n", "        'detection_rate_filtered': detection_rate_filtered,\n", "        'detection_rate_total': detection_rate_total,\n", "        'pile_details': pile_details,\n", "        'search_radius_used': search_radius,\n", "        'method': 'xy_only_labeling_improved'\n", "    }"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["def generate_negative_samples(points, pile_coords, patch_radius, num_negative_samples, min_distance=5.0):\n", "    # Generate negative training samples (non-pile patches) from the point cloud.\n", "    \n", "    # Get point cloud bounds\n", "    x_min, x_max = points[:, 0].min(), points[:, 0].max()\n", "    y_min, y_max = points[:, 1].min(), points[:, 1].max()\n", "    \n", "    negative_centers = []\n", "    attempts = 0\n", "    max_attempts = num_negative_samples * 10  # Prevent infinite loop\n", "    \n", "    while len(negative_centers) < num_negative_samples and attempts < max_attempts:\n", "        # Random location within point cloud bounds\n", "        candidate_x = np.random.uniform(x_min + patch_radius, x_max - patch_radius)\n", "        candidate_y = np.random.uniform(y_min + patch_radius, y_max - patch_radius)\n", "        candidate_z = np.median(points[:, 2])  # Use median Z as reference\n", "        \n", "        candidate_center = np.array([candidate_x, candidate_y, candidate_z])\n", "        \n", "        # Check distance to all pile centers\n", "        distances_to_piles = np.sqrt(np.sum((pile_coords[:, :2] - candidate_center[:2])**2, axis=1))\n", "        \n", "        if np.min(distances_to_piles) >= min_distance:\n", "            negative_centers.append(candidate_center)\n", "        \n", "        attempts += 1\n", "    \n", "    logger.info(f\"Generated {len(negative_centers)} negative samples in {attempts} attempts\")\n", "    return negative_centers\n"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-20 10:55:30,615 - INFO - Point cloud density: 0.19 points/m²\n", "2025-07-20 10:55:30,616 - INFO - Improved XY-only labeling with 12.0m search radius...\n", "2025-07-20 10:55:30,616 - INFO - Ignoring Z-coordinates due to coordinate system mismatch\n", "2025-07-20 10:55:30,617 - INFO - Point cloud bounds: X[435222.1, 436796.9], Y[5010809.4, 5012545.1]\n", "2025-07-20 10:55:30,617 - INFO - Filtered piles: 14460/14460 within survey area\n", "2025-07-20 10:55:30,748 - INFO - Searching for points around 14460 pile XY locations...\n", "2025-07-20 10:55:30,750 - INFO - <PERSON><PERSON> 4: 34 points, avg XY distance 11.3m\n", "2025-07-20 10:55:30,750 - INFO - <PERSON><PERSON> 7: 19 points, avg XY distance 9.4m\n", "2025-07-20 10:55:30,751 - INFO - <PERSON><PERSON> 10: 21 points, avg XY distance 10.3m\n", "2025-07-20 10:55:30,751 - INFO - <PERSON><PERSON> 11: 29 points, avg XY distance 7.2m\n", "2025-07-20 10:55:30,752 - INFO - <PERSON><PERSON> 12: 41 points, avg XY distance 7.8m\n", "2025-07-20 10:55:30,817 - INFO - Processed 1446/14460 piles\n", "2025-07-20 10:55:30,880 - INFO - Processed 2892/14460 piles\n", "2025-07-20 10:55:30,944 - INFO - Processed 4338/14460 piles\n", "2025-07-20 10:55:31,001 - INFO - Processed 5784/14460 piles\n", "2025-07-20 10:55:31,045 - INFO - Processed 7230/14460 piles\n", "2025-07-20 10:55:31,090 - INFO - Processed 8676/14460 piles\n", "2025-07-20 10:55:31,140 - INFO - Processed 10122/14460 piles\n", "2025-07-20 10:55:31,197 - INFO - Processed 11568/14460 piles\n", "2025-07-20 10:55:31,252 - INFO - Processed 13014/14460 piles\n", "2025-07-20 10:55:31,313 - INFO - Processed 14460/14460 piles\n", "2025-07-20 10:55:31,313 - INFO - Improved XY-only labeling complete:\n", "2025-07-20 10:55:31,314 - INFO -   Labeled piles: 13703/14460 within survey area (94.8%)\n", "2025-07-20 10:55:31,314 - INFO -   Total labeled: 13703/14460 of all piles (94.8%)\n", "2025-07-20 10:55:31,314 - INFO -   <PERSON><PERSON> points: 345,072\n", "2025-07-20 10:55:31,314 - INFO -   Background points: 162,960\n", "2025-07-20 10:55:31,315 - INFO -   Pile coverage: 67.9% of total points\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Validation Metrics:\n", "Detection rate: 94.8%\n", "Points per pile: 25.2\n", "Coverage: 67.9%\n"]}], "source": ["# Compute point cloud density\n", "x_range = points[:, 0].max() - points[:, 0].min()\n", "y_range = points[:, 1].max() - points[:, 1].min()\n", "area = x_range * y_range\n", "density = len(points) / area\n", "logger.info(f\"Point cloud density: {density:.2f} points/m²\")\n", "\n", "\n", "# Use improved XY-only labeling function with better parameters\n", "labels, pile_assignments, pile_info = create_xy_only_labeling_improved(\n", "    drone_points=points,\n", "    pile_coords=pile_coords,\n", "    search_radius=12.0,  # Reduced from 10.0m for better precision\n", "    min_points=15       # Reduced from 15 for better quality\n", ")\n", "\n", "\n", "\n", "# Validation metrics\n", "print(f\"\\nValidation Metrics:\")\n", "print(f\"Detection rate: {pile_info['detection_rate_filtered']:.1f}%\")\n", "print(f\"Points per pile: {pile_info['total_pile_points'] / max(pile_info['labeled_piles'], 1):.1f}\")\n", "print(f\"Coverage: {pile_info['total_pile_points'] / len(points) * 100:.1f}%\")\n"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1500x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Visualization of detected piles\n", "fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))\n", "\n", "# Plot 1: All points with detected piles highlighted\n", "ax1.scatter(points[:, 0], points[:, 1], c='lightgray', s=1, alpha=0.5, label='Background')\n", "pile_points = points[labels == 1]\n", "if len(pile_points) > 0:\n", "    ax1.scatter(pile_points[:, 0], pile_points[:, 1], c='red', s=3, label='Detected Piles')\n", "ax1.set_xlabel('X (m)')\n", "ax1.set_ylabel('Y (m)')\n", "ax1.set_title('Pile Detection Results')\n", "ax1.legend()\n", "ax1.grid(True, alpha=0.3)\n", "\n", "# Plot 2: IFC pile locations vs detected\n", "ax2.scatter(pile_coords[:, 0], pile_coords[:, 1], c='blue', s=2, alpha=0.3, label='IFC Piles')\n", "detected_pile_coords = []\n", "for detail in pile_info['pile_details']:\n", "    detected_pile_coords.append(detail['xy_coordinates'])\n", "if detected_pile_coords:\n", "    detected_coords = np.array(detected_pile_coords)\n", "    ax2.scatter(detected_coords[:, 0], detected_coords[:, 1], c='red', s=10, label='Detected')\n", "ax2.set_xlabel('X (m)')\n", "ax2.set_ylabel('Y (m)')\n", "ax2.set_title('IFC vs Detected Pile Locations')\n", "ax2.legend()\n", "ax2.grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Summary:\n", "Total IFC piles: 14,460\n", "Piles in survey area: 14,460\n", "Successfully detected: 13703\n", "Detection rate: 94.8%\n", "Average points per pile: 25.2\n", "\n", "Results saved to: ../../../data/output_runs/labeled_training_data/trino_enel_20250720_105530/pile_labeling_results.npz\n"]}], "source": ["# Summary\n", "print(f\"\\nSummary:\")\n", "print(f\"Total IFC piles: {len(pile_coords):,}\")\n", "print(f\"Piles in survey area: {pile_info['filtered_piles']:,}\")\n", "print(f\"Successfully detected: {pile_info['labeled_piles']}\")\n", "print(f\"Detection rate: {pile_info['detection_rate_filtered']:.1f}%\")\n", "print(f\"Average points per pile: {pile_info['total_pile_points'] / max(pile_info['labeled_piles'], 1):.1f}\")\n", "\n", "# Save results\n", "results = {\n", "    'labels': labels,\n", "    'pile_assignments': pile_assignments,\n", "    'pile_info': pile_info,\n", "    'points': points,\n", "    'pile_coords': pile_coords\n", "}\n", "np.savez(output_path / 'pile_labeling_results.npz', **results)\n", "print(f\"\\nResults saved to: {output_path / 'pile_labeling_results.npz'}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Create Training Patches with Z-Coordinate Correction\n", "\n", "Create training patches around pile locations using Z-coordinate corrected direct labeling."]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-20 10:55:33,094 - INFO - === Z-COORDINATE CORRECTED LABELING ===\n", "2025-07-20 10:55:33,095 - INFO - Handling absolute vs relative Z-coordinate systems\n", "2025-07-20 10:55:33,095 - INFO - Creating training patches with parameters:\n", "2025-07-20 10:55:33,095 - INFO -   - Patch radius: 2.0m\n", "2025-07-20 10:55:33,096 - INFO -   - Min points per patch: 50\n", "2025-07-20 10:55:33,096 - INFO -   - Max points per patch: 2048\n", "2025-07-20 10:55:33,096 - INFO -   - Negative sample ratio: 2.0\n"]}], "source": ["# Create training patches with Z-coordinate correction\n", "if 'points' in locals() and 'ifc_metadata' in locals():\n", "    \n", "    logger.info(f\"=== Z-COORDINATE CORRECTED LABELING ===\")\n", "    logger.info(f\"Handling absolute vs relative Z-coordinate systems\")\n", "    \n", "    logger.info(f\"Creating training patches with parameters:\")\n", "    logger.info(f\"  - Patch radius: {patch_radius}m\")\n", "    logger.info(f\"  - Min points per patch: {min_points_per_patch}\")\n", "    logger.info(f\"  - Max points per patch: {max_points_per_patch}\")\n", "    logger.info(f\"  - Negative sample ratio: {negative_sample_ratio}\")\n", "\n", "    # TODO\n", "    \n", "    \n", "else:\n", "    logger.error(\"Please ensure both point cloud and IFC metadata are loaded.\")"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["def visualize_training_patches(training_patches, sample_size=10):\n", "    \"\"\"Visualize sample training patches.\"\"\"\n", "    pass"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Save Labeled Training Data\n", "\n", "Export the training patches in ML-ready formats."]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["def save_labeled_data(training_patches, output_path, site_name, ground_seg_method):\n", "    \"\"\"Save training patches in ML-ready formats.\"\"\"\n", "    pass"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["# Save the training data\n", "# TODO"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Quality Assessment\n", "\n", "Assess the quality of the generated training patches."]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["def assess_training_quality(training_patches, ifc_metadata):\n", "    \"\"\"Assess the quality of generated training patches.\"\"\"\n", "    pass    "]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Summary\n", "\n", "This notebook demonstrated how to automatically label point cloud data using IFC pile metadata for pile detection:\n", "\n", "### **Key Benefits:**\n", "- **Automated Labeling**: No manual annotation required\n", "- **Ground-Segmented Input**: Uses clean non-ground points for better pile detection\n", "- **XY-Only Approach**: Focuses on horizontal pile detection, avoiding Z-coordinate issues\n", "- **Validation Metrics**: Built-in assessment of labeling quality\n", "- **Configurable Parameters**: Adjustable search radius and minimum point thresholds\n", "- **Visual Feedback**: Clear plots showing detection results\n", "\n", "### **Output Files:**\n", "- **Labeled results**: `pile_labeling_results.npz` containing:\n", "  - Point-wise labels (pile vs non-pile)\n", "  - Pile assignments (which pile each point belongs to)\n", "  - Detection statistics and metadata\n", "  - Original point cloud and IFC coordinates\n", "\n", "### **Next Steps:**\n", "1. **Improve Detection Parameters**: Adjust search radius and point thresholds for better results\n", "2. **Create Training Patches**: Use labeled data to generate training patches for deep learning\n", "3. **Train Detection Models**: Use PointNet++/DGCNN for automated pile detection\n", "4. **Validate Against Ground Truth**: Compare results with Trino_piles.csv validation data\n", "5. **Apply to New Sites**: Use trained models on sites without IFC data\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}