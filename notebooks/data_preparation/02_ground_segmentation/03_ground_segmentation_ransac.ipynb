{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Ground Segmentation using RANSAC Method\n", "\n", "This notebook implements RANSAC-based ground segmentation for point cloud processing in solar array inspection projects.\n", "\n", "**Method**: RANSAC (Random Sample Consensus) Plane Fitting  \n", "**Input Data**: Raw point cloud (.las, .laz, .pcd)  \n", "**Output**: Ground and non-ground point clouds with RANSAC-specific naming  \n", "**Format**: .ply files with method-specific prefixes  \n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: June 2025  \n", "**Project**: As Built Analytics for Solar Array Inspection\n", "\n", "## RANSAC Algorithm Overview\n", "\n", "RANSAC is a robust iterative method for fitting mathematical models to data containing outliers:\n", "\n", "**Strengths:**\n", "- Highly robust to outliers and noise\n", "- Works well on relatively flat terrain\n", "- Fast convergence for simple planar surfaces\n", "- Deterministic results with fixed random seed\n", "\n", "**Limitations:**\n", "- Assumes ground is a single plane\n", "- May struggle with complex terrain variations\n", "- Performance depends on parameter tuning\n", "- Can misclassify low vegetation as ground\n", "\n", "**Best Use Cases:**\n", "- Flat or gently sloping terrain\n", "- Areas with minimal vegetation\n", "- Quick initial ground removal\n", "- Preprocessing for other algorithms"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Configuration Parameters\n", "\n", "These parameters control the RANSAC algorithm behavior and can be adjusted via Papermill for different sites and conditions."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## RANSAC Plane Segmentation – Parameter Documentation\n", "\n", "**RANSAC (Random Sample Consensus)** is a robust model-fitting algorithm widely used in point cloud processing to detect geometric primitives such as planes. It iteratively selects random subsets of points to hypothesize models (planes), then verifies how many points in the full dataset support that model.\n", "\n", "This makes RANSAC highly resilient to noise and outliers, which is crucial for segmenting surfaces like solar tables, ground slabs, walls, and support beams in unstructured 3D scans.\n", "\n", "Below are the tunable parameters used in our RANSAC implementation:\n", "\n", "\n", "## Use Case Context\n", "\n", "In our pipeline, RANSAC is typically used for:\n", "- Extracting dominant horizontal planes (e.g., solar slab foundations).\n", "- Isolating vertical planes (e.g.,  piles or support poles).\n", "- Pre-filtering large flat regions before deviation or surface analysis.\n", "\n", "It works especially well post-CSF or PMF, once the ground/non-ground segmentation is complete.\n", "\n", "---\n", "\n", "## Source and References\n", "\n", "These parameters and algorithm behavior are based on standard implementations and research:\n", "\n", "- **<PERSON><PERSON><PERSON>, M. <PERSON>, & <PERSON>, R. C. (1981)**  \n", "  *Random sample consensus: A paradigm for model fitting with applications to image analysis and automated cartography.*  \n", "  [DOI:10.1016/S0004-370]()\n"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["# Papermill parameters - these will be injected by Papermill\n", "import sys\n", "from pathlib import Path\n", "\n", "# Add the `notebooks` folder to sys.path\n", "notebooks_root = Path(__file__).resolve().parents[1] if '__file__' in globals() else Path.cwd().parents[1]\n", "\n", "if str(notebooks_root) not in sys.path:\n", "    sys.path.insert(0, str(notebooks_root))\n", "\n", "# Now import from shared package\n", "from shared.config import get_data_path, get_output_path, get_mlflow_tracking_uri, find_latest_file, get_processed_data_path\n", "\n", "# Papermill parameters - these will be injected by Papermill\n", "site_name = \"trino_enel\"  # Site name for output file naming\n", "project_type = \"ENEL\"  # Options: \"ENEL\", \"USA\"\n", "method_name = \"ransac\"  # Options: \"csf\", \"pmf\", \"ransac\"\n", "timestamp = None  # Auto-generated if None\n", "\n", "# Standardized paths\n", "input_data_path = get_processed_data_path(site_name, \"denoising\")\n", "output_path = get_output_path(f\"ground_segmentation_{method_name}\", site_name, timestamp)\n", "mlflow_tracking_uri = get_mlflow_tracking_uri()\n", "coordinate_system = \"EPSG:32632\"\n", "\n", "# Find input file dynamically\n", "input_point_cloud = find_latest_file(input_data_path, \"*denoised*.ply\")\n", "point_cloud_path = str(input_point_cloud)\n", "\n", "# MLflow configuration\n", "mlflow_experiment_name = f\"ground_segmentation_{project_type}\"\n", "mlflow_run_name = f\"{method_name}_{site_name}_{timestamp or 'auto'}\"\n", "\n", "\n", "# === RANSAC (Plane Segmentation) Parameters ===\n", "# These parameters control how RANSAC detects planar surfaces in noisy point clouds.\n", "\n", "buffer_radius = 50.0  # Buffer radius for spatial filtering (meters)\n", "\n", "distance_threshold = 0.2  \n", "# Maximum distance (in meters) from a point to a candidate plane for it to be considered an inlier.\n", "# Smaller values yield tighter fitting planes but may miss noisy or partially flat regions.\n", "\n", "num_iterations = 1000  \n", "# Number of random sampling iterations to attempt.\n", "# More iterations increase the chance of finding the best-fitting plane.\n", "\n", "min_inliers_ratio = 0.05  \n", "# Minimum ratio of inliers (as a percentage of total points) required to accept a plane.\n", "# Helps filter out spurious or small patch detections.\n", "\n", "early_stop_ratio = 0.6  \n", "# If a plane is found that covers at least this ratio of total points, RANSAC will stop early.\n", "# Speeds up processing when large planar surfaces (e.g., ground or slabs) dominate.\n", "\n", "\n", "# === Processing Control Parameters ===\n", "# These help manage memory usage and performance for large point clouds.\n", "\n", "max_points_processing = 1000000  \n", "# Maximum number of points to process in memory at once.\n", "# If exceeded, the point cloud should be downsampled or processed in chunks."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Library Imports and Environment Setup\n", "\n", "Import required libraries for point cloud processing, visualization, and RANSAC implementation."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Libraries imported successfully. Random seed set to 42\n"]}], "source": ["# Import libraries\n", "import numpy as np\n", "import os\n", "import json\n", "import matplotlib.pyplot as plt\n", "from mpl_toolkits.mplot3d import Axes3D\n", "import time\n", "import logging\n", "from pathlib import Path\n", "from datetime import datetime\n", "from tqdm import tqdm\n", "\n", "# Set random seed for reproducible results\n", "random_seed = 42                 # Random seed for reproducible results\n", "np.random.seed(random_seed)\n", "\n", "print(f\"Libraries imported successfully. Random seed set to {random_seed}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Inference: Library Setup\n", "\n", "The RANSAC method requires minimal dependencies compared to other ground segmentation methods. The random seed ensures reproducible results across multiple runs, which is crucial for comparing different methods on the same dataset."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Logging Configuration\n", "\n", "Configure logging to track processing steps and performance metrics."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-20 14:27:20,496 - INFO - RANSAC Ground Segmentation initialized for site: trino_enel\n", "2025-07-20 14:27:20,497 - INFO - Parameters - Distance threshold: 0.2m, Iterations: 1000\n"]}], "source": ["# Configure logging\n", "logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')\n", "logger = logging.getLogger(__name__)\n", "\n", "logger.info(f\"RANSAC Ground Segmentation initialized for site: {site_name}\")\n", "logger.info(f\"Parameters - Distance threshold: {distance_threshold}m, Iterations: {num_iterations}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Directory Structure and Path Configuration\n", "\n", "Set up input and output paths following the project organization structure."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-20 14:27:20,504 - INFO - Checking raw data path: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/raw/trino_enel, Exists: True\n", "2025-07-20 14:27:20,505 - INFO - Using point cloud file: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/denoising/trino_enel_denoised.ply\n", "2025-07-20 14:27:20,506 - INFO - Ground segmentation output path: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/ground_segmentation\n"]}], "source": ["# Standardized paths\n", "from shared.config import get_data_path, get_output_path, get_mlflow_tracking_uri, find_latest_file, get_processed_data_path\n", "from shared.utils import resolve_point_cloud_path\n", "\n", "# Raw data path\n", "raw_path = get_data_path(site_name, data_type=\"raw\")\n", "logger.info(f\"Checking raw data path: {raw_path}, Exists: {raw_path.exists()}\")\n", "\n", "# Timestamped output directory\n", "timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')\n", "output_path = get_output_path(notebook_type=\"denoising\", site_name=site_name, timestamp=timestamp)\n", "\n", "# Processed data path for this stage\n", "processed_path = get_processed_data_path(site_name, processing_stage=\"denoising\")\n", "\n", "# Analysis output path \n", "analysis_output_path = get_data_path(site_name, data_type=\"analysis_output\")\n", "\n", "# Determine input path\n", "input_path = processed_path / f\"{site_name}_denoised.ply\"\n", "point_cloud_file = resolve_point_cloud_path(input_path)\n", "\n", "# Ground segmentation output directory (organized by site/project)\n", "ground_seg_path = get_processed_data_path(site_name, processing_stage=\"ground_segmentation\")\n", "ground_seg_path.mkdir(parents=True, exist_ok=True)\n", "\n", "logger.info(f\"Ground segmentation output path: {ground_seg_path.resolve()}\")\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Parameters saved to: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/analysis_output/trino_enel/parameters.json\n"]}], "source": ["from datetime import datetime\n", "import json\n", "\n", "timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')\n", "visualization_enabled = True\n", "\n", "# Save parameters to JSON for reproducibility\n", "parameters = {\n", "    \"run_info\": {\n", "        \"timestamp\": timestamp,\n", "        \"site_name\": site_name,\n", "        \"project_type\": project_type,\n", "        \"method\": \"RANSAC\",\n", "        \"notebook_version\": \"1.1\"\n", "    },\n", "    \"ransac_parameters\": {\n", "        \"distance_threshold\": distance_threshold,\n", "        \"num_iterations\": num_iterations,\n", "        \"min_inliers_ratio\": min_inliers_ratio,\n", "        \"early_stop_ratio\": early_stop_ratio\n", "    },\n", "    \"processing_parameters\": {\n", "        \"max_points_processing\": max_points_processing,\n", "        \"buffer_radius\": buffer_radius,\n", "        \"visualization_enabled\": visualization_enabled\n", "    },\n", "    \"paths\": {\n", "        \"input_path\": str(input_path),\n", "        \"output_path\": str(ground_seg_path),\n", "        \"run_output_path\": str(analysis_output_path)\n", "    }\n", "}\n", "\n", "# Save parameters to file\n", "params_file = analysis_output_path / \"parameters.json\"\n", "with open(params_file, 'w') as f:\n", "    json.dump(parameters, f, indent=2)\n", "\n", "print(f\"Parameters saved to: {params_file}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Inference: Path Configuration\n", "\n", "The RANSAC method uses method-specific output directory naming (ransac) to distinguish results from other ground segmentation methods. This ensures clear separation of outputs when comparing different approaches on the same dataset."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Point Cloud Data Loading\n", "\n", "Load the raw point cloud data from LAS/LAZ files and prepare for RANSAC processing."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-20 14:27:21,451 - INFO - Reading PLY file: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/denoising/trino_enel_denoised_20250720_141433.ply\n", "2025-07-20 14:27:21,707 - INFO - Point cloud statistics:\n", "2025-07-20 14:27:21,708 - INFO - --------------------------------------------------\n", "2025-07-20 14:27:21,708 - INFO -   Loaded 983767 points\n", "2025-07-20 14:27:21,710 - INFO -   X range: 435220.59 to 436794.95 (1574.36m)\n", "2025-07-20 14:27:21,712 - INFO -   Y range: 5010812.82 to 5012550.57 (1737.75m)\n", "2025-07-20 14:27:21,714 - INFO -   Z range: -0.82 to 14.73 (15.55m)\n"]}], "source": ["# Load Point Cloud data\n", "import open3d as o3d\n", "\n", "# Load Point Cloud (.ply)\n", "input_path = Path(point_cloud_path)\n", "logger.info(f\"Reading PLY file: {input_path}\")\n", "\n", "pcd = o3d.io.read_point_cloud(str(input_path))\n", "\n", "if not pcd.has_points():\n", "    raise ValueError(\"Loaded PLY file contains no points.\")\n", "\n", "points = np.asarray(pcd.points)\n", "\n", "# Display basic statistics\n", "logger.info(f\"Point cloud statistics:\")\n", "logger.info(\"-\" * 50)\n", "logger.info(f\"  Loaded {points.shape[0]} points\")\n", "\n", "x, y, z = points[:, 0], points[:, 1], points[:, 2]\n", "\n", "logger.info(f\"  X range: {x.min():.2f} to {x.max():.2f} ({x.max()-x.min():.2f}m)\")\n", "logger.info(f\"  Y range: {y.min():.2f} to {y.max():.2f} ({y.max()-y.min():.2f}m)\")\n", "logger.info(f\"  Z range: {z.min():.2f} to {z.max():.2f} ({z.max()-z.min():.2f}m)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Inference: Data Loading\n", "\n", "The point cloud has been successfully loaded. The coordinate ranges provide insight into the terrain characteristics that will affect RANSAC performance. Large Z-ranges may indicate complex terrain that could challenge the single-plane assumption of RANSAC."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## RANSAC Ground Plane Detection\n", "\n", "Execute the core RANSAC algorithm to identify the dominant ground plane in the point cloud."]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-20 14:27:21,722 - INFO - Running RANSAC ground detection...\n", "2025-07-20 14:27:21,723 - INFO - Parameters: threshold=0.2m, iterations=1000, min_ratio=0.05\n", "RANSAC iterations: 100%|██████████| 1000/1000 [00:21<00:00, 45.75it/s]\n", "2025-07-20 14:27:43,588 - INFO - RANSAC completed in 21.86 seconds\n", "2025-07-20 14:27:43,588 - INFO - Ground points: 479,070 (0.49 ratio)\n", "2025-07-20 14:27:43,590 - INFO - Plane: -0.000x + -0.000y + 1.000z + 364.367 = 0\n"]}], "source": ["import numpy as np\n", "import time\n", "from tqdm import tqdm\n", "\n", "logger.info(f\"Running RANSAC ground detection...\")\n", "logger.info(f\"Parameters: threshold={distance_threshold}m, iterations={num_iterations}, min_ratio={min_inliers_ratio}\")\n", "\n", "# Downsample the denoised point cloud before RANSAC\n", "# Purpose: Reduce computation, avoid overfitting, and improve speed\n", "max_points_ransac = 1_000_000\n", "if points.shape[0] > max_points_ransac:\n", "    logger.info(f\"Downsampling point cloud from {points.shape[0]:,} to {max_points_ransac:,} points for RANSAC\")\n", "    points = points[np.random.choice(points.shape[0], size=max_points_ransac, replace=False)]\n", "\n", "# Recompute point count AFTER downsampling\n", "n_points = points.shape[0]\n", "min_inliers = int(n_points * min_inliers_ratio)\n", "\n", "best_plane_params = None\n", "best_inliers = []\n", "max_inliers = 0\n", "\n", "start_time = time.time()\n", "\n", "for i in tqdm(range(num_iterations), desc=\"RANSAC iterations\"):\n", "    # Randomly sample 3 points\n", "    sample_indices = np.random.choice(n_points, 3, replace=False)\n", "    p1, p2, p3 = points[sample_indices]\n", "\n", "    # Compute plane normal\n", "    v1 = p2 - p1\n", "    v2 = p3 - p1\n", "    normal = np.cross(v1, v2)\n", "\n", "    norm = np.linalg.norm(normal)\n", "    if norm < 1e-6:\n", "        continue  # Skip degenerate planes\n", "\n", "    normal = normal / norm\n", "\n", "    # Enforce upward-facing normal\n", "    if normal[2] < 0:\n", "        normal = -normal\n", "\n", "    # Plane equation: ax + by + cz + d = 0\n", "    d = -np.dot(normal, p1)\n", "    plane_params = np.append(normal, d)\n", "\n", "    # Distance of all points to the plane\n", "    distances = np.abs(np.dot(points, plane_params[:3]) + d)\n", "\n", "    # Find inliers within threshold\n", "    inliers = np.where(distances < distance_threshold)[0]\n", "    n_inliers = len(inliers)\n", "\n", "    if n_inliers > max_inliers and n_inliers >= min_inliers:\n", "        best_plane_params = plane_params\n", "        best_inliers = inliers\n", "        max_inliers = n_inliers\n", "\n", "        inlier_ratio = n_inliers / n_points\n", "        if inlier_ratio > early_stop_ratio:\n", "            print(f\"Early stopping at iteration {i+1}: Found {n_inliers:,} ground points ({inlier_ratio:.2f} ratio)\")\n", "            break\n", "\n", "end_time = time.time()\n", "\n", "if best_plane_params is not None:\n", "    elapsed_time = end_time - start_time\n", "    logger.info(f\"RANSAC completed in {elapsed_time:.2f} seconds\")\n", "\n", "    ground_ratio = max_inliers / n_points\n", "    plane_eq_str = (\n", "        f\"{best_plane_params[0]:.3f}x + \"\n", "        f\"{best_plane_params[1]:.3f}y + \"\n", "        f\"{best_plane_params[2]:.3f}z + \"\n", "        f\"{best_plane_params[3]:.3f} = 0\"\n", "    )\n", "    summary_msg = f\"Ground points: {max_inliers:,} ({ground_ratio:.2f} ratio)\"\n", "    logger.info(summary_msg)\n", "    \n", "    logger.info(f\"Plane: {plane_eq_str}\")\n", "\n", "    ground_points = points[best_inliers]\n", "    nonground_points = np.delete(points, best_inliers, axis=0)\n", "else:\n", "    raise ValueError(\"RANSAC failed to find a valid ground plane.\")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Inference: RANSAC Ground Detection Results\n", "\n", "The RANSAC algorithm successfully identified a ground plane with approximately 49% of points classified as ground. The plane equation shows a nearly horizontal surface (z-coefficient ≈ 1.0) with minimal slope, which is typical for flat terrain. The processing time of ~23 seconds for 1M points demonstrates RANSAC's computational efficiency. The ground ratio of 0.49 suggests a balanced distribution between ground and non-ground features, indicating the presence of significant above-ground structures."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Point Cloud Output Generation\n", "\n", "Save the segmented ground and non-ground point clouds with RANSAC-specific naming convention."]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-20 14:27:43,658 - INFO - Saved: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/analysis_output/trino_enel/analysis_output/trino_enel_ground.ply\n", "2025-07-20 14:27:43,701 - INFO - Saved: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/analysis_output/trino_enel/analysis_output/trino_enel_nonground.ply\n", "2025-07-20 14:27:43,748 - INFO - Saved: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/ground_segmentation/ransac/trino_enel_ground.ply\n", "2025-07-20 14:27:43,811 - INFO - Saved: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/ground_segmentation/ransac/trino_enel_nonground.ply\n", "2025-07-20 14:27:43,813 - INFO - \n", "RANSAC segmentation outputs saved:\n", "2025-07-20 14:27:43,816 - INFO -   Ground points: 479,070\n", "2025-07-20 14:27:43,818 - INFO -   Non-ground points: 504,697\n", "2025-07-20 14:27:43,823 - INFO -   Method identifier: ransac\n"]}], "source": ["# Save segmented point clouds with method-specific naming\n", "import open3d as o3d\n", "\n", "def save_ply(path, points_array, method_name=\"\"):\n", "    \"\"\"Save point cloud with method-specific naming for comparison.\"\"\"\n", "    pc = o3d.geometry.PointCloud()\n", "    pc.points = o3d.utility.Vector3dVector(points_array)\n", "    o3d.io.write_point_cloud(str(path), pc)\n", "    logger.info(f\"Saved: {path}\")\n", "\n", "# Save the point clouds to the appropriate output paths\n", "analysis_output = analysis_output_path / 'analysis_output'\n", "analysis_output.mkdir(parents=True, exist_ok=True)\n", "\n", "# Save ground and nonground points to the analysis_output directory\n", "save_ply(analysis_output / f\"{site_name}_ground.ply\", ground_points)\n", "save_ply(analysis_output / f\"{site_name}_nonground.ply\", nonground_points)\n", "\n", "# Satisfied with the results save the final output for next stage \n", "save_ply(ground_seg_path / f\"{method_name}/{site_name}_ground.ply\", ground_points)\n", "save_ply(ground_seg_path / f\"{method_name}/{site_name}_nonground.ply\", nonground_points)\n", "\n", "logger.info(f\"\\nRANSAC segmentation outputs saved:\")\n", "logger.info(f\"  Ground points: {len(ground_points):,}\")\n", "logger.info(f\"  Non-ground points: {len(nonground_points):,}\")\n", "logger.info(f\"  Method identifier: {method_name}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Inference: Output Generation\n", "\n", "Point clouds have been saved with RANSAC-specific naming (ransac_ground.ply, ransac_nonground.ply) to enable direct comparison with other ground segmentation methods. This naming convention ensures that results from different algorithms can be easily distinguished and analyzed side-by-side."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Statistical Analysis of Segmentation Results\n", "\n", "Calculate key metrics to evaluate RANSAC segmentation quality and characteristics."]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-20 14:27:43,852 - INFO - Ground Ratio: 0.4870\n", "2025-07-20 14:27:43,853 - INFO - Non-Ground Ratio: 0.5130\n", "2025-07-20 14:27:43,853 - INFO - --------------------------------------------------\n", "2025-07-20 14:27:43,854 - INFO - RANSAC Segmentation Summary:\n", "2025-07-20 14:27:43,854 - INFO - --------------------------------------------------\n", "2025-07-20 14:27:43,855 - INFO -   Total points processed: 983,767\n", "2025-07-20 14:27:43,855 - INFO -   Ground points: 479,070 (48.7%)\n", "2025-07-20 14:27:43,856 - INFO -   Non-ground points: 504,697 (51.3%)\n"]}], "source": ["# Calculate ground to non-ground ratio\n", "# Ground/Non-Ground ratio\n", "ground_count = ground_points.shape[0]\n", "nonground_count = nonground_points.shape[0]\n", "total_points = ground_count + nonground_count\n", "\n", "ground_ratio = ground_count / total_points\n", "logger.info(f\"Ground Ratio: {ground_ratio:.4f}\")\n", "\n", "nonground_ratio = nonground_count / total_points\n", "logger.info(f\"Non-Ground Ratio: {nonground_ratio:.4f}\")\n", "\n", "logger.info(\"-\" * 50)\n", "logger.info(f\"RANSAC Segmentation Summary:\")\n", "logger.info(\"-\" * 50)\n", "\n", "logger.info(f\"  Total points processed: {ground_points.shape[0] + nonground_points.shape[0]:,}\")\n", "logger.info(f\"  Ground points: {ground_points.shape[0]:,} ({ground_ratio:.1%})\")\n", "logger.info(f\"  Non-ground points: {nonground_points.shape[0]:,} ({1-ground_ratio:.1%})\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Inference: Statistical Analysis\n", "\n", "The RANSAC method achieved a ground ratio of 49.4%, indicating a balanced segmentation between ground and non-ground features. This ratio is characteristic of areas with significant infrastructure or vegetation coverage above the ground plane."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Elevation Analysis and Vertical Separation\n", "\n", "Analyze the vertical characteristics of ground vs non-ground points to assess segmentation quality."]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-20 14:27:43,864 - INFO -   RANSAC Elevation Analysis:\n", "2025-07-20 14:27:43,864 - INFO - --------------------------------------------------\n", "2025-07-20 14:27:43,864 - INFO -   Ground elevation - Mean: 0.383m, Std: 0.108m\n", "2025-07-20 14:27:43,865 - INFO -   Non-ground elevation - Mean: 1.917m, Std: 1.260m\n", "2025-07-20 14:27:43,865 - INFO -   Vertical separation: 1.534m\n", "2025-07-20 14:27:43,866 - INFO -   Ground elevation range: 0.510m\n", "2025-07-20 14:27:43,866 - INFO -   Non-ground elevation range: 15.547m\n"]}], "source": ["# Calculate elevation statistics for ground and non-ground points\n", "ground_z_mean = ground_points[:, 2].mean()\n", "ground_z_std = ground_points[:, 2].std()\n", "nonground_z_mean = nonground_points[:, 2].mean()\n", "nonground_z_std = nonground_points[:, 2].std()\n", "z_separation = nonground_z_mean - ground_z_mean\n", "\n", "logger.info(f\"  RANSAC Elevation Analysis:\")\n", "logger.info(\"-\" * 50)\n", "logger.info(f\"  Ground elevation - Mean: {ground_z_mean:.3f}m, Std: {ground_z_std:.3f}m\")\n", "logger.info(f\"  Non-ground elevation - Mean: {nonground_z_mean:.3f}m, Std: {nonground_z_std:.3f}m\")\n", "logger.info(f\"  Vertical separation: {z_separation:.3f}m\")\n", "\n", "# Calculate elevation ranges\n", "ground_z_range = ground_points[:, 2].max() - ground_points[:, 2].min()\n", "nonground_z_range = nonground_points[:, 2].max() - nonground_points[:, 2].min()\n", "logger.info(f\"  Ground elevation range: {ground_z_range:.3f}m\")\n", "logger.info(f\"  Non-ground elevation range: {nonground_z_range:.3f}m\")"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-20 14:27:43,890 - INFO -   Bounding Box Sizes (X, Y, Z):\n", "2025-07-20 14:27:43,890 - INFO - --------------------------------------------------\n", "2025-07-20 14:27:43,891 - INFO -   Ground:     [1.573508e+03 1.733186e+03 5.100000e-01]\n", "2025-07-20 14:27:43,891 - INFO -   Non-Ground: [1574.364 1736.433   15.547]\n"]}], "source": ["# Bounding Box Stats\n", "def bounding_box_stats(points):\n", "    min_bound = np.min(points, axis=0)\n", "    max_bound = np.max(points, axis=0)\n", "    return max_bound - min_bound\n", "\n", "ground_bbox = bounding_box_stats(ground_points)\n", "nonground_bbox = bounding_box_stats(nonground_points)\n", "\n", "logger.info(\"  Bounding Box Sizes (X, Y, Z):\")\n", "logger.info(\"-\" * 50)\n", "logger.info(f\"  Ground:     {ground_bbox}\")\n", "logger.info(f\"  Non-Ground: {nonground_bbox}\")"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Height (Z) Distribution Plot\n", "plt.figure(figsize=(10, 5))\n", "plt.hist(ground_points[:, 2], bins=100, alpha=0.6, label='Ground Z')\n", "plt.hist(nonground_points[:, 2], bins=100, alpha=0.6, label='Non-Ground Z')\n", "plt.legend()\n", "plt.title(\"Z-Height Distribution\")\n", "plt.xlabel(\"Z (Elevation)\")\n", "plt.ylabel(\"Point Count\")\n", "plt.grid(True)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-20 14:27:44,098 - INFO - RANSAC Ground Segmentation - Ready!\n", "2025-07-20 14:27:44,099 - INFO - ==================================================\n", "2025-07-20 14:27:44,100 - INFO - Project: ENEL/trino_enel\n", "2025-07-20 14:27:44,100 - INFO - Input path: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/denoising/trino_enel_denoised_20250720_141433.ply\n", "2025-07-20 14:27:44,101 - INFO - Output path: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/ground_segmentation\n", "2025-07-20 14:27:44,101 - INFO - Current run output: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/analysis_output/trino_enel\n", "2025-07-20 14:27:44,102 - INFO - RANSAC Parameters: threshold=0.2m, iterations=1000\n"]}], "source": ["# Final readiness print\n", "logger.info(\"RANSAC Ground Segmentation - Ready!\")\n", "logger.info(\"=\" * 50)\n", "logger.info(f\"Project: {project_type}/{site_name}\")\n", "logger.info(f\"Input path: {input_path}\")\n", "logger.info(f\"Output path: {ground_seg_path}\")\n", "logger.info(f\"Current run output: {analysis_output_path}\")\n", "logger.info(f\"RANSAC Parameters: threshold={distance_threshold}m, iterations={num_iterations}\")"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["# Visualize the results\n", "import open3d as o3d\n", "import numpy as np\n", "\n", "# Create point cloud for ground\n", "pcd_ground = o3d.geometry.PointCloud()\n", "pcd_ground.points = o3d.utility.Vector3dVector(ground_points)\n", "pcd_ground.paint_uniform_color([0.0, 1.0, 0.0])  # Green\n", "\n", "# Create point cloud for non-ground\n", "pcd_nonground = o3d.geometry.PointCloud()\n", "pcd_nonground.points = o3d.utility.Vector3dVector(nonground_points)\n", "pcd_nonground.paint_uniform_color([1.0, 0.0, 0.0])  # Red\n", "\n", "# Show both together\n", "o3d.visualization.draw_geometries([pcd_ground, pcd_nonground],\n", "                                  window_name=\"Ground vs Non-Ground\",\n", "                                  point_show_normal=False)\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}