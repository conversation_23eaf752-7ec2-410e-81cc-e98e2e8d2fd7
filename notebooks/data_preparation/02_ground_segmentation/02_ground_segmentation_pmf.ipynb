{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Ground Segmentation - Progressive Morphological Filter (PMF)\n", "\n", "This notebook implements PMF-based ground segmentation for point cloud processing.\n", "\n", "**Method**: Progressive Morphological Filter  \n", "**Input Data**: Raw point cloud (.las, .laz, .pcd)  \n", "**Output**: Ground-removed / non-ground point cloud  \n", "**Format**: .ply (recommended for compatibility with Open3D + visualization), .pcd (preferred for PCL + ML pipelines)  \n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: June 2025\n", "**Project**: As Built Analytics for Solar Array Inspection\n", "\n", "## PMF Algorithm:\n", "- Based on <PERSON> et al. (2003) algorithm\n", "- Uses morphological operations on rasterized point cloud\n", "- Progressive window size increase\n", "- Best for complex terrain with vegetation"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup and Imports"]}, {"cell_type": "code", "execution_count": 81, "metadata": {}, "outputs": [], "source": ["# Import libraries\n", "import numpy as np\n", "import os\n", "import json\n", "import matplotlib.pyplot as plt\n", "from mpl_toolkits.mplot3d import Axes3D\n", "import time\n", "import logging\n", "from pathlib import Path\n", "from datetime import datetime\n", "from scipy import ndimage"]}, {"cell_type": "code", "execution_count": 82, "metadata": {}, "outputs": [], "source": ["# Configure logging\n", "logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')\n", "logger = logging.getLogger(__name__)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Parameters"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Progressive Morphological Filter (PMF) – Parameter Documentation\n", "\n", "The **Progressive Morphological Filter (PMF)** is a commonly used ground segmentation algorithm, particularly in LiDAR and photogrammetric point clouds. It works by progressively increasing the size of a structuring element to remove non-ground points based on local elevation changes and terrain slope.\n", "\n", "Below are the tunable parameters used in our implementation:\n", "\n", "---\n", "## Source and References\n", "\n", "These parameters and the algorithm are based on research and implementations including:\n", "\n", "- **<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, & <PERSON>, <PERSON> (2003)**  \n", "  *A progressive morphological filter for removing nonground measurements from airborne LIDAR data.*  \n", "  [DOI:10.1109/TGRS.2003.810682](https://doi.org/10.1109/TGRS.2003.810682)\n", "\n", "- **PDAL PMF Filter Docs**  \n", "  https://pdal.io/stages/filters.pmf.html\n", "\n", "- **LAStools PMF Implementation**  \n", "  [https://rapidlasso.com/2013/11/07/morphological-ground-classification](https://rapidlasso.com/2013/11/07/morphological-ground-classification)\n"]}, {"cell_type": "code", "execution_count": 83, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["import sys\n", "from pathlib import Path\n", "\n", "# Add the `notebooks` folder to sys.path\n", "notebooks_root = Path(__file__).resolve().parents[1] if '__file__' in globals() else Path.cwd().parents[1]\n", "\n", "if str(notebooks_root) not in sys.path:\n", "    sys.path.insert(0, str(notebooks_root))\n", "\n", "# Now import from shared package\n", "from shared.config import get_data_path, get_output_path, get_mlflow_tracking_uri, find_latest_file, get_processed_data_path\n", "\n", "# Papermill parameters - these will be injected by Papermill\n", "site_name = \"trino_enel\"  # Site name for output file naming\n", "project_type = \"ENEL\"  # Options: \"ENEL\", \"USA\"\n", "method_name = \"pmf\"  # Options: \"csf\", \"pmf\", \"ransac\"\n", "timestamp = None  # Auto-generated if None\n", "\n", "# Standardized paths\n", "input_data_path = get_processed_data_path(site_name, \"denoising\")\n", "output_path = get_output_path(f\"ground_segmentation_{method_name}\", site_name, timestamp)\n", "mlflow_tracking_uri = get_mlflow_tracking_uri()\n", "coordinate_system = \"EPSG:32632\"\n", "\n", "# Find input file dynamically\n", "input_point_cloud = find_latest_file(input_data_path, \"*denoised*.ply\")\n", "point_cloud_path = str(input_point_cloud)\n", "\n", "# MLflow configuration\n", "mlflow_experiment_name = f\"ground_segmentation_{project_type}\"\n", "mlflow_run_name = f\"{method_name}_{site_name}_{timestamp or 'auto'}\"\n", "\n", "\n", "# Papermill parameters - these will be injected by Papermill\n", "# === PMF (Progressive Morphological Filter) Parameters ===\n", "# These parameters control how the morphological filter is applied to identify ground points \n", "# by simulating terrain smoothing across increasing window sizes.\n", "\n", "buffer_radius = 50.0  # Buffer radius for spatial filtering (meters)\n", "\n", "cell_size = 1.0  \n", "# Size of each grid cell when rasterizing the point cloud (in meters).\n", "# Smaller values retain finer surface detail but increase computation.\n", "# Recommended: 0.5 – 2.0 based on point density and terrain complexity.\n", "\n", "max_window_size = 20  \n", "# Maximum size (in raster units) of the morphological structuring element.\n", "# Determines the scale of features that can be removed (e.g., buildings, vegetation).\n", "# Larger values capture broader terrain variation but may oversmooth.\n", "\n", "slope = 0.15  \n", "# Maximum local slope (in radians) allowed during filtering.\n", "# Points exceeding this elevation change across a window are treated as non-ground.\n", "# Typical values: 0.1 – 0.3 for natural terrain.\n", "\n", "max_distance = 2.5  \n", "# Maximum elevation difference (in meters) between a point and the estimated ground surface \n", "# to still be classified as ground.\n", "# Helps in removing high outliers like trees and rooftops.\n", "\n", "initial_distance = 0.5  \n", "# Initial threshold (in meters) for elevation difference during early filtering iterations.\n", "# A tighter threshold avoids early misclassifications and stabilizes the progressive process.\n", "\n", "height_threshold_ratio = 0.1  \n", "# Proportion of the lowest height range used to seed initial ground estimation (0–1).\n", "# Typically set between 0.05 and 0.15 to capture the base terrain while ignoring outliers."]}, {"cell_type": "code", "execution_count": 84, "metadata": {}, "outputs": [], "source": ["# Configure logging\n", "import logging\n", "from datetime import datetime\n", "\n", "logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')\n", "logger = logging.getLogger(__name__)"]}, {"cell_type": "code", "execution_count": 85, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-20 14:15:58,685 - INFO - Checking raw data path: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/raw/trino_enel, Exists: True\n", "2025-07-20 14:15:58,688 - INFO - Using point cloud file: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/denoising/trino_enel_denoised.ply\n", "2025-07-20 14:15:58,690 - INFO - Ground segmentation output path: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/ground_segmentation\n"]}], "source": ["# Standardized paths\n", "from shared.utils import resolve_point_cloud_path\n", "# Raw data path\n", "raw_path = get_data_path(site_name, data_type=\"raw\")\n", "logger.info(f\"Checking raw data path: {raw_path}, Exists: {raw_path.exists()}\")\n", "\n", "# Timestamped output directory\n", "timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')\n", "output_path = get_output_path(notebook_type=\"denoising\", site_name=site_name, timestamp=timestamp)\n", "\n", "# Processed data path for this stage\n", "processed_path = get_processed_data_path(site_name, processing_stage=\"denoising\")\n", "\n", "# Analysis output path \n", "analysis_output_path = get_data_path(site_name, data_type=\"analysis_output\")\n", "\n", "# Determine input path\n", "input_path = processed_path / f\"{site_name}_denoised.ply\"\n", "point_cloud_file = resolve_point_cloud_path(input_path)\n", "\n", "# Ground segmentation output directory (organized by site/project)\n", "ground_seg_path = get_processed_data_path(site_name, processing_stage=\"ground_segmentation\")\n", "ground_seg_path.mkdir(parents=True, exist_ok=True)\n", "\n", "logger.info(f\"Ground segmentation output path: {ground_seg_path.resolve()}\")"]}, {"cell_type": "code", "execution_count": 86, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-20 14:15:58,696 - INFO - Reading PLY file: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/denoising/trino_enel_denoised_20250720_141433.ply\n", "2025-07-20 14:15:58,736 - INFO - Point cloud statistics:\n", "2025-07-20 14:15:58,737 - INFO - --------------------------------------------------\n", "2025-07-20 14:15:58,737 - INFO -   Loaded 983767 points\n", "2025-07-20 14:15:58,741 - INFO -   X range: 435220.59 to 436794.95 (1574.36m)\n", "2025-07-20 14:15:58,743 - INFO -   Y range: 5010812.82 to 5012550.57 (1737.75m)\n", "2025-07-20 14:15:58,745 - INFO -   Z range: -0.82 to 14.73 (15.55m)\n"]}], "source": ["# Load Point Cloud data\n", "import open3d as o3d\n", "import numpy as np\n", "\n", "# Load Point Cloud (.ply)\n", "input_path = Path(point_cloud_path)\n", "logger.info(f\"Reading PLY file: {input_path}\")\n", "\n", "pcd = o3d.io.read_point_cloud(str(input_path))\n", "\n", "if not pcd.has_points():\n", "    raise ValueError(\"Loaded PLY file contains no points.\")\n", "\n", "points = np.asarray(pcd.points)\n", "\n", "# Display basic statistics\n", "logger.info(f\"Point cloud statistics:\")\n", "logger.info(\"-\" * 50)\n", "logger.info(f\"  Loaded {points.shape[0]} points\")\n", "\n", "x, y, z = points[:, 0], points[:, 1], points[:, 2]\n", "\n", "logger.info(f\"  X range: {x.min():.2f} to {x.max():.2f} ({x.max()-x.min():.2f}m)\")\n", "logger.info(f\"  Y range: {y.min():.2f} to {y.max():.2f} ({y.max()-y.min():.2f}m)\")\n", "logger.info(f\"  Z range: {z.min():.2f} to {z.max():.2f} ({z.max()-z.min():.2f}m)\")"]}, {"cell_type": "code", "execution_count": 87, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-20 14:16:01,595 - INFO - Ground points: 290174\n", "2025-07-20 14:16:01,596 - INFO - Non-ground points: 693593\n"]}], "source": ["from scipy.ndimage import grey_erosion, grey_dilation\n", "from scipy import ndimage\n", "\n", "# Grid the point cloud (2D raster)\n", "min_xy = np.min(points[:, :2], axis=0)\n", "max_xy = np.max(points[:, :2], axis=0)\n", "dims = np.ceil((max_xy - min_xy) / cell_size).astype(int)\n", "grid = np.full(dims, np.nan)\n", "\n", "# Populate raster with lowest Z value per cell\n", "for x, y, z in points:\n", "    xi = int((x - min_xy[0]) / cell_size)\n", "    yi = int((y - min_xy[1]) / cell_size)\n", "    if 0 <= xi < dims[0] and 0 <= yi < dims[1]:\n", "        if np.isnan(grid[xi, yi]) or z < grid[xi, yi]:\n", "            grid[xi, yi] = z\n", "\n", "# Fill holes\n", "filled_grid = ndimage.grey_closing(np.nan_to_num(grid, nan=np.nanmin(grid)), size=3)\n", "\n", "# Morphological opening (erosion then dilation)\n", "opened = grey_dilation(grey_erosion(filled_grid, size=max_window_size), size=max_window_size)\n", "\n", "# Ground mask based on slope threshold\n", "z_diff = filled_grid - opened\n", "ground_mask_2d = z_diff < slope\n", "\n", "# Reconstruct full ground point mask\n", "ground_mask = []\n", "for x, y, z in points:\n", "    xi = int((x - min_xy[0]) / cell_size)\n", "    yi = int((y - min_xy[1]) / cell_size)\n", "    if 0 <= xi < dims[0] and 0 <= yi < dims[1]:\n", "        if ground_mask_2d[xi, yi]:\n", "            ground_mask.append(True)\n", "        else:\n", "            ground_mask.append(False)\n", "ground_mask = np.array(ground_mask)\n", "\n", "ground_points = points[ground_mask]\n", "nonground_points = points[~ground_mask]\n", "\n", "logger.info(f\"Ground points: {ground_points.shape[0]}\")\n", "logger.info(f\"Non-ground points: {nonground_points.shape[0]}\")"]}, {"cell_type": "code", "execution_count": 88, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-20 14:16:01,647 - INFO - Saved: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/output_runs/denoising/trino_enel_20250720_141558/analysis_output/trino_enel_ground.ply\n", "2025-07-20 14:16:01,752 - INFO - Saved: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/output_runs/denoising/trino_enel_20250720_141558/analysis_output/trino_enel_nonground.ply\n"]}], "source": ["# ## Save Output .PLY\n", "def save_ply(path, points_array):\n", "    pc = o3d.geometry.PointCloud()\n", "    pc.points = o3d.utility.Vector3dVector(points_array)\n", "    o3d.io.write_point_cloud(str(path), pc)\n", "    logger.info(f\"Saved: {path}\")\n", "\n", "# Save the point clouds to the appropriate output paths\n", "# Save the point clouds to the appropriate output paths\n", "analysis_output = output_path / 'analysis_output'\n", "analysis_output.mkdir(parents=True, exist_ok=True)\n", "\n", "# Save ground and nonground points to the analysis_output directory\n", "save_ply(analysis_output / f\"{site_name}_ground.ply\", ground_points)\n", "save_ply(analysis_output / f\"{site_name}_nonground.ply\", nonground_points)\n"]}, {"cell_type": "code", "execution_count": 89, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-20 14:16:01,757 - INFO - Ground Ratio: 0.2950\n", "2025-07-20 14:16:01,758 - INFO - Non-Ground Ratio: 0.7050\n"]}], "source": ["# Ground/Non-Ground ratio\n", "ground_count = ground_points.shape[0]\n", "nonground_count = nonground_points.shape[0]\n", "total_points = ground_count + nonground_count\n", "\n", "ground_ratio = ground_count / total_points\n", "logger.info(f\"Ground Ratio: {ground_ratio:.4f}\")\n", "\n", "nonground_ratio = nonground_count / total_points\n", "logger.info(f\"Non-Ground Ratio: {nonground_ratio:.4f}\")"]}, {"cell_type": "code", "execution_count": 90, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-20 14:16:01,764 - INFO - ground_z_mean:{ground_z_mean}\n", "2025-07-20 14:16:01,765 - INFO - nonground_z_mean:1.4505089930261694\n", "2025-07-20 14:16:01,765 - INFO - z_separation: 0.9524228136992827\n"]}], "source": ["ground_z_mean = ground_points[:, 2].mean()\n", "nonground_z_mean = nonground_points[:, 2].mean()\n", "z_separation = nonground_z_mean - ground_z_mean\n", "\n", "logger.info(\"ground_z_mean:{ground_z_mean}\")\n", "logger.info(f\"nonground_z_mean:{nonground_z_mean}\")\n", "logger.info(f\"z_separation: {z_separation}\")"]}, {"cell_type": "code", "execution_count": 91, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-20 14:16:01,789 - INFO -   Bounding Box Sizes (X, Y, Z):\n", "2025-07-20 14:16:01,790 - INFO - --------------------------------------------------\n", "2025-07-20 14:16:01,792 - INFO -   Ground:     [1569.781 1704.087    8.387]\n", "2025-07-20 14:16:01,792 - INFO -   Non-Ground: [1574.364 1737.751   15.447]\n"]}], "source": ["# Bounding Box Stats\n", "def bounding_box_stats(points):\n", "    min_bound = np.min(points, axis=0)\n", "    max_bound = np.max(points, axis=0)\n", "    return max_bound - min_bound\n", "\n", "ground_bbox = bounding_box_stats(ground_points)\n", "nonground_bbox = bounding_box_stats(nonground_points)\n", "\n", "logger.info(\"  Bounding Box Sizes (X, Y, Z):\")\n", "logger.info(\"-\" * 50)\n", "logger.info(f\"  Ground:     {ground_bbox}\")\n", "logger.info(f\"  Non-Ground: {nonground_bbox}\")"]}, {"cell_type": "code", "execution_count": 92, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Height (Z) Distribution Plot\n", "from matplotlib import pyplot as plt\n", "\n", "plt.figure(figsize=(10, 5))\n", "plt.hist(ground_points[:, 2], bins=100, alpha=0.6, label='Ground Z')\n", "plt.hist(nonground_points[:, 2], bins=100, alpha=0.6, label='Non-Ground Z')\n", "plt.legend()\n", "plt.title(\"Z-Height Distribution\")\n", "plt.xlabel(\"Z (Elevation)\")\n", "plt.ylabel(\"Point Count\")\n", "plt.grid(True)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 93, "metadata": {}, "outputs": [], "source": ["# Save parameters to JSON for reproducibility\n", "import json\n", "\n", "parameters = {\n", "    \"run_info\": {\n", "        \"timestamp\": timestamp,\n", "        \"site_name\": site_name,\n", "        \"project_type\": project_type,\n", "        \"method\": \"PMF\",\n", "        \"notebook_version\": \"1.1\"\n", "    },\n", "    \"pmf_parameters\": {\n", "        \"cell_size\": cell_size,\n", "        \"max_window_size\": max_window_size,\n", "        \"slope\": slope,\n", "        \"max_distance\": max_distance,\n", "        \"initial_distance\": initial_distance,\n", "        \"height_threshold_ratio\": height_threshold_ratio\n", "    },\n", "    \"processing_parameters\": {\n", "        \"buffer_radius\": buffer_radius\n", "    },\n", "    \"paths\": {\n", "        \"input_path\": str(input_path),\n", "        \"output_path\": str(ground_seg_path),\n", "        \"run_output_path\": str(analysis_output_path)\n", "    }\n", "}\n", "\n", "# Save parameters to file\n", "params_file = analysis_output_path / \"parameters.json\"\n", "with open(params_file, 'w') as f:\n", "    json.dump(parameters, f, indent=2)\n"]}, {"cell_type": "code", "execution_count": 94, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-20 14:16:02,220 - INFO - PMF Ground Segmentation - Ready!\n", "2025-07-20 14:16:02,220 - INFO - ==================================================\n", "2025-07-20 14:16:02,221 - INFO - Project: ENEL/trino_enel\n", "2025-07-20 14:16:02,221 - INFO - --------------------------------------------------\n", "2025-07-20 14:16:02,222 - INFO - Input path: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/denoising/trino_enel_denoised_20250720_141433.ply\n", "2025-07-20 14:16:02,222 - INFO - Output path: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/ground_segmentation\n", "2025-07-20 14:16:02,222 - INFO - Current run output: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/analysis_output/trino_enel\n", "2025-07-20 14:16:02,223 - INFO - PMF Parameters: cell_size=1.0m, max_window=20, slope=0.15\n"]}], "source": ["# Final readiness logger.info\n", "logger.info(\"PMF Ground Segmentation - Ready!\")\n", "logger.info(\"=\" * 50)\n", "logger.info(f\"Project: {project_type}/{site_name}\")\n", "logger.info(\"-\" * 50)\n", "logger.info(f\"Input path: {input_path}\")\n", "logger.info(f\"Output path: {ground_seg_path}\")\n", "logger.info(f\"Current run output: {analysis_output_path}\")\n", "logger.info(f\"PMF Parameters: cell_size={cell_size}m, max_window={max_window_size}, slope={slope}\")"]}, {"cell_type": "code", "execution_count": 95, "metadata": {}, "outputs": [], "source": ["# Visualize the results\n", "import open3d as o3d\n", "import numpy as np\n", "\n", "# Create point cloud for ground\n", "pcd_ground = o3d.geometry.PointCloud()\n", "pcd_ground.points = o3d.utility.Vector3dVector(ground_points)\n", "pcd_ground.paint_uniform_color([0.0, 1.0, 0.0])  # Green\n", "\n", "# Create point cloud for non-ground\n", "pcd_nonground = o3d.geometry.PointCloud()\n", "pcd_nonground.points = o3d.utility.Vector3dVector(nonground_points)\n", "pcd_nonground.paint_uniform_color([1.0, 0.0, 0.0])  # Red\n", "\n", "# Show both together\n", "o3d.visualization.draw_geometries([pcd_ground, pcd_nonground],\n", "                                  window_name=\"Ground vs Non-Ground\",\n", "                                  point_show_normal=False)\n"]}, {"cell_type": "code", "execution_count": 96, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-20 14:16:05,154 - INFO - Saved: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/ground_segmentation/pmf/trino_enel_ground.ply\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-07-20 14:16:05,183 - INFO - Saved: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/ground_segmentation/pmf/trino_enel_nonground.ply\n"]}], "source": ["# Satisfied with the results save the final output for next stage \n", "save_ply(ground_seg_path / f\"{method_name}/{site_name}_ground.ply\", ground_points)\n", "save_ply(ground_seg_path / f\"{method_name}/{site_name}_nonground.ply\", nonground_points)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}