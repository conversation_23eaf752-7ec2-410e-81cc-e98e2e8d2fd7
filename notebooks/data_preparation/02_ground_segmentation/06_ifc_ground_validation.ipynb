{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# IFC-Based Ground Segmentation Validation\n", "\n", "This notebook validates ground segmentation results against IFC geometric models to ensure accuracy and reliability for downstream alignment processes.\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: July 2025  \n", "**Project**: As-Built Foundation Analysis"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup and Dependencies"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from pathlib import Path\n", "import logging\n", "import ifcopenshell\n", "import ifcopenshell.geom\n", "import open3d as o3d\n", "from scipy.spatial import cKDTree\n", "from sklearn.metrics import confusion_matrix, classification_report\n", "import mlflow\n", "import mlflow.sklearn\n", "from datetime import datetime\n", "\n", "# Setup logging\n", "logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')\n", "logger = logging.getLogger(__name__)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Parameters"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["# Site configuration\n", "site_name = \"trino_enel\"\n", "project_type = \"solar\"\n", "\n", "# Validation parameters\n", "spatial_tolerance = 0.5  # meters for spatial matching\n", "elevation_tolerance = 0.2  # meters for elevation validation\n", "min_overlap_threshold = 0.1  # minimum overlap for valid comparison\n", "\n", "# Ground segmentation methods to validate\n", "ground_methods = ['csf', 'pmf', 'ransac', 'ransac_pmf']\n", "\n", "# Output configuration\n", "generate_validation_plots = True\n", "save_validation_results = True"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Data Loading"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-19 17:34:44,810 - INFO - IFC processed path: ../../../data/processed/trino_enel/ifc_metadata\n", "2025-07-19 17:34:44,811 - INFO - Ground results path: ../../../data/processed/trino_enel/ground_segmentation\n", "2025-07-19 17:34:44,811 - INFO - Output path: ../../../data/processed/trino_enel/validation\n"]}], "source": ["# Define paths\n", "base_path = Path('../../../data')\n", "ifc_processed_path = base_path / 'processed' / site_name / 'ifc_metadata'\n", "ground_results_path = base_path / 'processed' / site_name / 'ground_segmentation'\n", "output_path = base_path / 'processed' / site_name / 'validation'\n", "\n", "logger.info(f\"IFC processed path: {ifc_processed_path}\")\n", "logger.info(f\"Ground results path: {ground_results_path}\")\n", "logger.info(f\"Output path: {output_path}\")\n", "\n", "# Ensure output directory exists\n", "output_path.mkdir(parents=True, exist_ok=True)\n", "\n", "# Check if IFC preprocessing has been completed\n", "if not ifc_processed_path.exists():\n", "    logger.warning(f\"IFC processed directory not found: {ifc_processed_path}\")\n", "    logger.info(\"Please run IFC metadata extraction first: notebooks/data_preparation/04_ifc_processing/1_ifc_metadata_extraction.ipynb\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## IFC Ground Reference Loading"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["def load_ifc_terrain_reference():\n", "    \"\"\"Load pre-extracted IFC terrain points from IFC preprocessing\"\"\"\n", "    \n", "    # Look for terrain points from IFC preprocessing\n", "    ifc_processed_path = base_path / 'processed' / site_name / 'ifc_metadata'\n", "    \n", "    terrain_files = list(ifc_processed_path.glob('*terrain_points.ply'))\n", "    \n", "    if not terrain_files:\n", "        logger.warning(f\"No IFC terrain points found in {ifc_processed_path}\")\n", "        logger.info(\"Please run IFC metadata extraction (04_ifc_processing/1_ifc_metadata_extraction.ipynb) first\")\n", "        return None, None\n", "    \n", "    terrain_file = terrain_files[0]\n", "    logger.info(f\"Loading IFC terrain reference from: {terrain_file.name}\")\n", "    \n", "    try:\n", "        # Load terrain points\n", "        terrain_pcd = o3d.io.read_point_cloud(str(terrain_file))\n", "        terrain_points = np.asarray(terrain_pcd.points)\n", "        \n", "        # Load terrain metadata if available\n", "        metadata_file = terrain_file.parent / terrain_file.name.replace('_terrain_points.ply', '_terrain_metadata.csv')\n", "        terrain_metadata = None\n", "        \n", "        if metadata_file.exists():\n", "            terrain_metadata = pd.read_csv(metadata_file)\n", "            logger.info(f\"Loaded terrain metadata: {len(terrain_metadata)} elements\")\n", "        \n", "        logger.info(f\"Loaded {len(terrain_points)} IFC terrain points\")\n", "        logger.info(f\"Terrain extent: X[{terrain_points[:, 0].min():.2f}, {terrain_points[:, 0].max():.2f}]\")\n", "        logger.info(f\"Terrain extent: Y[{terrain_points[:, 1].min():.2f}, {terrain_points[:, 1].max():.2f}]\")\n", "        logger.info(f\"Terrain extent: Z[{terrain_points[:, 2].min():.2f}, {terrain_points[:, 2].max():.2f}]\")\n", "        \n", "        return terrain_points, terrain_metadata\n", "        \n", "    except Exception as e:\n", "        logger.error(f\"Failed to load IFC terrain reference: {e}\")\n", "        return None, None"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-19 17:34:44,821 - WARNING - No IFC terrain points found in ../../../data/processed/trino_enel/ifc_metadata\n", "2025-07-19 17:34:44,821 - INFO - Please run IFC metadata extraction (04_ifc_processing/1_ifc_metadata_extraction.ipynb) first\n", "2025-07-19 17:34:44,822 - ERROR - Failed to load IFC ground reference\n", "2025-07-19 17:34:44,822 - INFO - Validation cannot proceed without IFC terrain data\n"]}], "source": ["# Load IFC terrain reference\n", "ifc_ground_points, ifc_terrain_metadata = load_ifc_terrain_reference()\n", "\n", "if ifc_ground_points is not None:\n", "    logger.info(f\"Successfully loaded IFC ground reference: {len(ifc_ground_points)} points\")\n", "    \n", "    if ifc_terrain_metadata is not None:\n", "        logger.info(\"IFC Terrain Metadata Summary:\")\n", "        logger.info(f\"  Terrain elements: {len(ifc_terrain_metadata)}\")\n", "        logger.info(f\"  Element types: {ifc_terrain_metadata['Type'].unique()}\")\n", "        logger.info(f\"  Total points: {ifc_terrain_metadata['PointCount'].sum()}\")\n", "        logger.info(f\"  Z range: {ifc_terrain_metadata['MinZ'].min():.2f} to {ifc_terrain_metadata['MaxZ'].max():.2f} m\")\n", "else:\n", "    logger.error(\"Failed to load IFC ground reference\")\n", "    logger.info(\"Validation cannot proceed without IFC terrain data\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Ground Segmentation Results Loading"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["def load_ground_segmentation_results(method_name):\n", "    \"\"\"Load ground segmentation results for a specific method\"\"\"\n", "    \n", "    method_path = ground_results_path / method_name\n", "    \n", "    # Look for ground points file\n", "    ground_files = list(method_path.glob('*ground_points*.las')) + list(method_path.glob('*ground_points*.ply'))\n", "    \n", "    if not ground_files:\n", "        logger.warning(f\"No ground points file found for {method_name}\")\n", "        return None\n", "    \n", "    ground_file = ground_files[0]\n", "    logger.info(f\"Loading {method_name} ground points from: {ground_file.name}\")\n", "    \n", "    try:\n", "        if ground_file.suffix == '.las':\n", "            import laspy\n", "            las_file = laspy.read(str(ground_file))\n", "            points = np.column_stack([las_file.x, las_file.y, las_file.z])\n", "        elif ground_file.suffix == '.ply':\n", "            pcd = o3d.io.read_point_cloud(str(ground_file))\n", "            points = np.asarray(pcd.points)\n", "        else:\n", "            logger.error(f\"Unsupported file format: {ground_file.suffix}\")\n", "            return None\n", "        \n", "        logger.info(f\"Loaded {len(points)} {method_name} ground points\")\n", "        return points\n", "        \n", "    except Exception as e:\n", "        logger.error(f\"Failed to load {method_name} ground points: {e}\")\n", "        return None"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-19 17:34:44,831 - WARNING - No ground points file found for csf\n", "2025-07-19 17:34:44,831 - WARNING - Skipping csf - no valid ground points found\n", "2025-07-19 17:34:44,832 - WARNING - No ground points file found for pmf\n", "2025-07-19 17:34:44,832 - WARNING - Skipping pmf - no valid ground points found\n", "2025-07-19 17:34:44,833 - WARNING - No ground points file found for ransac\n", "2025-07-19 17:34:44,833 - WARNING - Skipping ransac - no valid ground points found\n", "2025-07-19 17:34:44,834 - WARNING - No ground points file found for ransac_pmf\n", "2025-07-19 17:34:44,834 - WARNING - Skipping ransac_pmf - no valid ground points found\n", "2025-07-19 17:34:44,834 - INFO - Loaded ground results for 0 methods\n"]}], "source": ["# Load all ground segmentation results\n", "ground_segmentation_results = {}\n", "\n", "for method in ground_methods:\n", "    points = load_ground_segmentation_results(method)\n", "    if points is not None:\n", "        ground_segmentation_results[method] = points\n", "    else:\n", "        logger.warning(f\"Skipping {method} - no valid ground points found\")\n", "\n", "logger.info(f\"Loaded ground results for {len(ground_segmentation_results)} methods\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Validation Functions"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["def calculate_spatial_overlap(segmented_ground, ifc_ground, tolerance=0.5):\n", "    \"\"\"Calculate spatial overlap between segmented and IFC ground points\"\"\"\n", "    \n", "    if len(segmented_ground) == 0 or len(ifc_ground) == 0:\n", "        return 0.0\n", "    \n", "    # Build KDTree for IFC ground points\n", "    tree = cKDTree(ifc_ground[:, :2])  # Use only X,Y coordinates\n", "    \n", "    # Find segmented points within tolerance of IFC ground\n", "    distances, _ = tree.query(segmented_ground[:, :2], distance_upper_bound=tolerance)\n", "    \n", "    # Count points within tolerance\n", "    valid_matches = np.sum(distances < tolerance)\n", "    \n", "    # Calculate overlap percentage\n", "    overlap_percentage = (valid_matches / len(segmented_ground)) * 100\n", "    \n", "    return overlap_percentage\n", "\n", "def calculate_elevation_rmse(segmented_ground, ifc_ground, spatial_tolerance=0.5):\n", "    \"\"\"Calculate elevation RMSE between spatially matched points\"\"\"\n", "    \n", "    if len(segmented_ground) == 0 or len(ifc_ground) == 0:\n", "        return float('inf')\n", "    \n", "    # Build KDTree for IFC ground points\n", "    tree = cKDTree(ifc_ground[:, :2])\n", "    \n", "    # Find nearest IFC points for each segmented point\n", "    distances, indices = tree.query(segmented_ground[:, :2])\n", "    \n", "    # Filter to points within spatial tolerance\n", "    valid_mask = distances < spatial_tolerance\n", "    \n", "    if np.sum(valid_mask) == 0:\n", "        return float('inf')\n", "    \n", "    # Calculate elevation differences for valid matches\n", "    segmented_z = segmented_ground[valid_mask, 2]\n", "    ifc_z = ifc_ground[indices[valid_mask], 2]\n", "    \n", "    elevation_diff = segmented_z - ifc_z\n", "    rmse = np.sqrt(np.mean(elevation_diff**2))\n", "    \n", "    return rmse\n", "\n", "def calculate_coverage_completeness(segmented_ground, ifc_ground, tolerance=0.5):\n", "    \"\"\"Calculate what percentage of IFC ground area is covered by segmented ground\"\"\"\n", "    \n", "    if len(segmented_ground) == 0 or len(ifc_ground) == 0:\n", "        return 0.0\n", "    \n", "    # Build KDTree for segmented ground points\n", "    tree = cKDTree(segmented_ground[:, :2])\n", "    \n", "    # Find IFC points covered by segmented ground\n", "    distances, _ = tree.query(ifc_ground[:, :2], distance_upper_bound=tolerance)\n", "    \n", "    # Count IFC points within tolerance of segmented ground\n", "    covered_points = np.sum(distances < tolerance)\n", "    \n", "    # Calculate coverage percentage\n", "    coverage_percentage = (covered_points / len(ifc_ground)) * 100\n", "    \n", "    return coverage_percentage"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Validation Execution"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-19 17:34:44,845 - ERROR - Cannot perform validation - missing IFC ground reference or segmentation results\n"]}], "source": ["# Perform validation for all methods\n", "validation_results = []\n", "\n", "if ifc_ground_points is not None and len(ground_segmentation_results) > 0:\n", "    logger.info(\"Starting IFC ground validation...\")\n", "    \n", "    for method_name, segmented_points in ground_segmentation_results.items():\n", "        logger.info(f\"Validating {method_name}...\")\n", "        \n", "        # Calculate validation metrics\n", "        spatial_overlap = calculate_spatial_overlap(\n", "            segmented_points, ifc_ground_points, spatial_tolerance\n", "        )\n", "        \n", "        elevation_rmse = calculate_elevation_rmse(\n", "            segmented_points, ifc_ground_points, spatial_tolerance\n", "        )\n", "        \n", "        coverage_completeness = calculate_coverage_completeness(\n", "            segmented_points, ifc_ground_points, spatial_tolerance\n", "        )\n", "        \n", "        # Store results\n", "        result = {\n", "            'method': method_name,\n", "            'segmented_points': len(segmented_points),\n", "            'ifc_reference_points': len(ifc_ground_points),\n", "            'spatial_overlap_percent': spatial_overlap,\n", "            'elevation_rmse_m': elevation_rmse,\n", "            'coverage_completeness_percent': coverage_completeness,\n", "            'spatial_tolerance_m': spatial_tolerance,\n", "            'validation_timestamp': datetime.now().isoformat()\n", "        }\n", "        \n", "        validation_results.append(result)\n", "        \n", "        logger.info(f\"{method_name} validation complete:\")\n", "        logger.info(f\"  Spatial overlap: {spatial_overlap:.1f}%\")\n", "        logger.info(f\"  Elevation RMSE: {elevation_rmse:.3f}m\")\n", "        logger.info(f\"  Coverage completeness: {coverage_completeness:.1f}%\")\n", "    \n", "    # Create results DataFrame\n", "    validation_df = pd.DataFrame(validation_results)\n", "    \n", "    logger.info(\"\\n=== IFC GROUND VALIDATION SUMMARY ===\")\n", "    print(validation_df[['method', 'spatial_overlap_percent', 'elevation_rmse_m', 'coverage_completeness_percent']].round(3))\n", "    \n", "else:\n", "    logger.error(\"Cannot perform validation - missing IFC ground reference or segmentation results\")\n", "    validation_df = pd.DataFrame()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Validation Visualization"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["# Create validation visualization\n", "if generate_validation_plots and not validation_df.empty:\n", "    \n", "    fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n", "    fig.suptitle(f'IFC Ground Validation Results - {site_name.replace(\"_\", \" \").title()}', \n", "                 fontsize=16, fontweight='bold')\n", "    \n", "    # 1. Spatial Overlap Comparison\n", "    ax1 = axes[0, 0]\n", "    bars1 = ax1.bar(validation_df['method'], validation_df['spatial_overlap_percent'], \n", "                    color=['#2E8B57', '#4169E1', '#9932CC', '#008B8B'], alpha=0.8)\n", "    ax1.set_title('Spatial Overlap with IFC Ground')\n", "    ax1.set_ylabel('Overlap Percentage (%)')\n", "    ax1.set_ylim(0, 100)\n", "    ax1.grid(True, alpha=0.3)\n", "    \n", "    # Add value labels\n", "    for bar, value in zip(bars1, validation_df['spatial_overlap_percent']):\n", "        height = bar.get_height()\n", "        ax1.text(bar.get_x() + bar.get_width()/2., height + 1,\n", "                f'{value:.1f}%', ha='center', va='bottom', fontweight='bold')\n", "    \n", "    # 2. Elevation RMSE Comparison\n", "    ax2 = axes[0, 1]\n", "    bars2 = ax2.bar(validation_df['method'], validation_df['elevation_rmse_m'], \n", "                    color=['#2E8B57', '#4169E1', '#9932CC', '#008B8B'], alpha=0.8)\n", "    ax2.set_title('Elevation RMSE vs IFC Ground')\n", "    ax2.set_ylabel('RMSE (meters)')\n", "    ax2.grid(True, alpha=0.3)\n", "    \n", "    # Add value labels\n", "    for bar, value in zip(bars2, validation_df['elevation_rmse_m']):\n", "        height = bar.get_height()\n", "        if np.isfinite(value):\n", "            ax2.text(bar.get_x() + bar.get_width()/2., height + height*0.05,\n", "                    f'{value:.3f}m', ha='center', va='bottom', fontweight='bold')\n", "    \n", "    # 3. Coverage Completeness\n", "    ax3 = axes[1, 0]\n", "    bars3 = ax3.bar(validation_df['method'], validation_df['coverage_completeness_percent'], \n", "                    color=['#2E8B57', '#4169E1', '#9932CC', '#008B8B'], alpha=0.8)\n", "    ax3.set_title('IFC Ground Coverage Completeness')\n", "    ax3.set_ylabel('Coverage Percentage (%)')\n", "    ax3.set_ylim(0, 100)\n", "    ax3.grid(True, alpha=0.3)\n", "    \n", "    # Add value labels\n", "    for bar, value in zip(bars3, validation_df['coverage_completeness_percent']):\n", "        height = bar.get_height()\n", "        ax3.text(bar.get_x() + bar.get_width()/2., height + 1,\n", "                f'{value:.1f}%', ha='center', va='bottom', fontweight='bold')\n", "    \n", "    # 4. Combined Performance Radar Chart\n", "    ax4 = axes[1, 1]\n", "    \n", "    # Normalize metrics for radar chart (0-1 scale)\n", "    metrics_normalized = validation_df.copy()\n", "    metrics_normalized['spatial_overlap_norm'] = metrics_normalized['spatial_overlap_percent'] / 100\n", "    metrics_normalized['elevation_accuracy_norm'] = 1 / (1 + metrics_normalized['elevation_rmse_m'])  # Inverse for accuracy\n", "    metrics_normalized['coverage_norm'] = metrics_normalized['coverage_completeness_percent'] / 100\n", "    \n", "    # Create summary score\n", "    metrics_normalized['overall_score'] = (\n", "        metrics_normalized['spatial_overlap_norm'] * 0.4 +\n", "        metrics_normalized['elevation_accuracy_norm'] * 0.3 +\n", "        metrics_normalized['coverage_norm'] * 0.3\n", "    ) * 100\n", "    \n", "    bars4 = ax4.bar(metrics_normalized['method'], metrics_normalized['overall_score'], \n", "                    color=['#2E8B57', '#4169E1', '#9932CC', '#008B8B'], alpha=0.8)\n", "    ax4.set_title('Overall Validation Score')\n", "    ax4.set_ylabel('Composite Score (0-100)')\n", "    ax4.set_ylim(0, 100)\n", "    ax4.grid(True, alpha=0.3)\n", "    \n", "    # Add value labels\n", "    for bar, value in zip(bars4, metrics_normalized['overall_score']):\n", "        height = bar.get_height()\n", "        ax4.text(bar.get_x() + bar.get_width()/2., height + 1,\n", "                f'{value:.1f}', ha='center', va='bottom', fontweight='bold')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    # Save validation plot\n", "    if save_validation_results:\n", "        validation_plot_path = output_path / f\"ifc_ground_validation_{site_name}.png\"\n", "        fig.savefig(validation_plot_path, dpi=300, bbox_inches='tight')\n", "        logger.info(f\"Saved validation plot: {validation_plot_path}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## MLflow Integration"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["# Log validation results to MLflow\n", "if not validation_df.empty:\n", "    \n", "    experiment_name = f\"ground_segmentation_ifc_validation_{site_name}\"\n", "    mlflow.set_experiment(experiment_name)\n", "    \n", "    with mlflow.start_run(run_name=f\"ifc_validation_{datetime.now().strftime('%Y%m%d_%H%M%S')}\"):\n", "        \n", "        # Log parameters\n", "        mlflow.log_param(\"site_name\", site_name)\n", "        mlflow.log_param(\"spatial_tolerance\", spatial_tolerance)\n", "        mlflow.log_param(\"elevation_tolerance\", elevation_tolerance)\n", "        mlflow.log_param(\"ifc_reference_points\", len(ifc_ground_points))\n", "        \n", "        # Log metrics for each method\n", "        for _, row in validation_df.iterrows():\n", "            method = row['method']\n", "            mlflow.log_metric(f\"{method}_spatial_overlap_percent\", row['spatial_overlap_percent'])\n", "            mlflow.log_metric(f\"{method}_elevation_rmse_m\", row['elevation_rmse_m'])\n", "            mlflow.log_metric(f\"{method}_coverage_completeness_percent\", row['coverage_completeness_percent'])\n", "            mlflow.log_metric(f\"{method}_segmented_points\", row['segmented_points'])\n", "        \n", "        # Log validation results as artifact\n", "        if save_validation_results:\n", "            validation_csv_path = output_path / f\"ifc_validation_results_{site_name}.csv\"\n", "            validation_df.to_csv(validation_csv_path, index=False)\n", "            mlflow.log_artifact(str(validation_csv_path))\n", "            \n", "            if generate_validation_plots:\n", "                mlflow.log_artifact(str(validation_plot_path))\n", "        \n", "        logger.info(f\"Logged validation results to MLflow experiment: {experiment_name}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Summary and Recommendations"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-19 17:34:44,864 - ERROR - No validation results available for analysis\n"]}], "source": ["# Generate validation summary and recommendations\n", "if not validation_df.empty:\n", "    \n", "    logger.info(\"\\n\" + \"=\"*60)\n", "    logger.info(\"IFC GROUND VALIDATION SUMMARY\")\n", "    logger.info(\"=\"*60)\n", "    \n", "    # Find best performing method for each metric\n", "    best_spatial = validation_df.loc[validation_df['spatial_overlap_percent'].idxmax()]\n", "    best_elevation = validation_df.loc[validation_df['elevation_rmse_m'].idxmin()]\n", "    best_coverage = validation_df.loc[validation_df['coverage_completeness_percent'].idxmax()]\n", "    \n", "    logger.info(f\"Best Spatial Overlap: {best_spatial['method'].upper()} ({best_spatial['spatial_overlap_percent']:.1f}%)\")\n", "    logger.info(f\"Best Elevation Accuracy: {best_elevation['method'].upper()} ({best_elevation['elevation_rmse_m']:.3f}m RMSE)\")\n", "    logger.info(f\"Best Coverage: {best_coverage['method'].upper()} ({best_coverage['coverage_completeness_percent']:.1f}%)\")\n", "    \n", "    # Method-specific analysis\n", "    logger.info(\"\\nMethod-Specific Analysis:\")\n", "    for _, row in validation_df.iterrows():\n", "        method = row['method'].upper()\n", "        spatial = row['spatial_overlap_percent']\n", "        rmse = row['elevation_rmse_m']\n", "        coverage = row['coverage_completeness_percent']\n", "        \n", "        # Generate recommendations\n", "        if spatial > 70 and rmse < 0.3 and coverage > 70:\n", "            recommendation = \"EXCELLENT - Suitable for all alignment methods\"\n", "        elif spatial > 50 and rmse < 0.5:\n", "            recommendation = \"GOOD - Suitable for most alignment applications\"\n", "        elif spatial > 30:\n", "            recommendation = \"MODERATE - May require parameter tuning\"\n", "        else:\n", "            recommendation = \"POOR - Not recommended for ICP alignment\"\n", "        \n", "        logger.info(f\"  {method}: {recommendation}\")\n", "        logger.info(f\"    Spatial: {spatial:.1f}%, Elevation: {rmse:.3f}m, Coverage: {coverage:.1f}%\")\n", "    \n", "    logger.info(\"\\nNext Steps:\")\n", "    logger.info(\"1. Use validation results to select optimal ground segmentation method\")\n", "    logger.info(\"2. Integrate IFC validation into alignment preprocessing workflow\")\n", "    logger.info(\"3. Consider method-specific parameter tuning based on validation feedback\")\n", "    logger.info(\"4. Document validation criteria for future site processing\")\n", "    \n", "else:\n", "    logger.error(\"No validation results available for analysis\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}