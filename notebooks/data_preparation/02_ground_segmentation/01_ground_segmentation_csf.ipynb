{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Ground Segmentation - Cloth Simulation Filter (CSF)\n", "\n", "This notebook implements CSF-based ground segmentation for point cloud processing.\n", "\n", "**Method**: Cloth Simulation Filter  \n", "**Input Data**: Raw point cloud (.las, .laz, .pcd)  \n", "**Output**: Ground-removed / non-ground point cloud  \n", "**Format**: .ply (recommended for compatibility with Open3D + visualization), .pcd (preferred for PCL + ML pipelines)  \n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: June 2025\n", "**Project**: As Built Analytics for Solar Array Inspection\n", "\n", "## CSF Algorithm:\n", "- Based on <PERSON> et al. (2016) algorithm\n", "- Simulates cloth falling onto inverted point cloud\n", "- Physics-based approach using particle system\n", "- Best for complex urban environments"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Parameters"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Reference for Cloth Simulation Filter (CSF)\n", "\n", "The **Cloth Simulation Filter (CSF)** is a method for ground point classification from airborne or drone-based LiDAR/point cloud data. It simulates a cloth dropping onto the inverted point cloud and uses the resulting mesh to segment ground from non-ground points.\n", "\n", "The method was introduced by:\n", "\n", "> **<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, & <PERSON>, <PERSON>. (2016).**  \n", "> *An easy-to-use airborne LiDAR data filtering method based on cloth simulation*.  \n", "> Re<PERSON> Sen<PERSON>, 8(6), 501.  \n", "> [https://doi.org/10.3390/rs8060501](https://doi.org/10.3390/rs8060501)\n", "\n", "\n", "### Tools That Use CSF\n", "- CloudCompare (CSF Plugin)  (https://www.cloudcompare.org/doc/wiki/index.php?title=CSF_(plugin))"]}, {"cell_type": "code", "execution_count": 16, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["import sys\n", "from pathlib import Path\n", "\n", "# Add the `notebooks` folder to sys.path\n", "notebooks_root = Path(__file__).resolve().parents[1] if '__file__' in globals() else Path.cwd().parents[1]\n", "\n", "if str(notebooks_root) not in sys.path:\n", "    sys.path.insert(0, str(notebooks_root))\n", "\n", "# Now import from shared package\n", "from shared.config import get_data_path, get_output_path, get_mlflow_tracking_uri, find_latest_file, get_processed_data_path\n", "\n", "# Papermill parameters - these will be injected by Papermill\n", "site_name = \"trino_enel\"  # Site name for output file naming\n", "project_type = \"ENEL\"  # Options: \"ENEL\", \"USA\"\n", "method_name = \"csf\"  # Options: \"csf\", \"pmf\", \"ransac\"\n", "timestamp = None  # Auto-generated if None\n", "\n", "# Standardized paths\n", "input_data_path = get_processed_data_path(site_name, \"denoising\")\n", "output_path = get_output_path(f\"ground_segmentation_{method_name}\", site_name, timestamp)\n", "mlflow_tracking_uri = get_mlflow_tracking_uri()\n", "coordinate_system = \"EPSG:32632\"\n", "\n", "# Find input file dynamically\n", "input_point_cloud = find_latest_file(input_data_path, \"*denoised*.ply\")\n", "point_cloud_path = str(input_point_cloud)\n", "\n", "# MLflow configuration\n", "mlflow_experiment_name = f\"ground_segmentation_{project_type}\"\n", "mlflow_run_name = f\"{method_name}_{site_name}_{timestamp or 'auto'}\"\n", "\n", "\n", "# Papermill parameters - these will be injected by Papermill\n", "\n", "# === CSF (Cloth Simulation Filter) Parameters ===\n", "# These control how the cloth behaves and how the ground is identified in point cloud data.\n", "\n", "buffer_radius = 50.0  # Buffer radius for spatial filtering (meters)\n", "\n", "cloth_resolution = 0.25  \n", "# Size of each grid cell in the cloth mesh (in meters).\n", "# Smaller values → finer cloth mesh → better detail capture but higher compute time.\n", "# Recommended: 0.1 – 0.5 for high-resolution drone data.\n", "\n", "max_iterations = 500  \n", "# Maximum number of iterations for cloth simulation convergence.\n", "# Higher values improve accuracy but slow down processing.\n", "# Recommended: 200 – 1000 depending on scene complexity.\n", "\n", "classification_threshold = 0.4  \n", "# Vertical distance (in meters) used to classify a point as ground.\n", "# Points below this threshold from the cloth surface are marked as ground.\n", "# Tuning this helps avoid misclassifying low-lying vegetation or panels as ground.\n", "\n", "rigidness = 3  \n", "# Stiffness of the cloth. Values: 1 (soft) → 3 (rigid).\n", "# Soft cloth adapts to local terrain variation (e.g., small slopes or trenches),\n", "# while rigid cloth gives a smoother approximation of large-scale terrain.\n", "\n", "time_step = 0.65  \n", "# Simulation timestep size. Affects cloth stability and convergence speed.\n", "# Should generally stay between 0.5 and 1.0.\n", "\n", "neighbor_search_radius = 1.2  \n", "# Radius (in meters) used to refine classification using local neighborhood context.\n", "# Helps reduce noise in ground/non-ground labels by smoothing small misclassifications.\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup and Imports"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["# Import libraries\n", "import laspy\n", "import numpy as np\n", "import os\n", "import json\n", "import matplotlib.pyplot as plt\n", "from mpl_toolkits.mplot3d import Axes3D\n", "import time\n", "import logging\n", "from pathlib import Path\n", "from datetime import datetime\n", "from sklearn.neighbors import NearestNeighbors"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["# Configure logging\n", "logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')\n", "logger = logging.getLogger(__name__)"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-20 14:15:41,570 - INFO - Checking raw data path: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/raw/trino_enel, Exists: True\n", "2025-07-20 14:15:41,573 - INFO - Using point cloud file: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/denoising/trino_enel_denoised.ply\n", "2025-07-20 14:15:41,574 - INFO - Ground segmentation output path: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/ground_segmentation\n"]}], "source": ["# Standardized paths\n", "from shared.utils import resolve_point_cloud_path\n", "# Raw data path\n", "raw_path = get_data_path(site_name, data_type=\"raw\")\n", "logger.info(f\"Checking raw data path: {raw_path}, Exists: {raw_path.exists()}\")\n", "\n", "# Timestamped output directory\n", "timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')\n", "output_path = get_output_path(notebook_type=\"denoising\", site_name=site_name, timestamp=timestamp)\n", "\n", "# Processed data path for this stage\n", "processed_path = get_processed_data_path(site_name, processing_stage=\"denoising\")\n", "\n", "# Analysis output path \n", "analysis_output_path = get_data_path(site_name, data_type=\"analysis_output\")\n", "\n", "# Determine input path\n", "input_path = processed_path / f\"{site_name}_denoised.ply\"\n", "point_cloud_file = resolve_point_cloud_path(input_path)\n", "\n", "# Ground segmentation output directory (organized by site/project)\n", "ground_seg_path = get_processed_data_path(site_name, processing_stage=\"ground_segmentation\")\n", "ground_seg_path.mkdir(parents=True, exist_ok=True)\n", "\n", "logger.info(f\"Ground segmentation output path: {ground_seg_path.resolve()}\")\n"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["total 0\n", "drwxr-xr-x@ 4 <USER>  <GROUP>   128B Jul  4 14:45 \u001b[34mcsf\u001b[m\u001b[m\n", "drwxr-xr-x@ 4 <USER>  <GROUP>   128B Jul  4 14:50 \u001b[34mpmf\u001b[m\u001b[m\n", "drwxr-xr-x@ 4 <USER>  <GROUP>   128B Jul  4 15:04 \u001b[34mransac\u001b[m\u001b[m\n", "drwxr-xr-x@ 5 <USER>  <GROUP>   160B Jul 17 06:24 \u001b[34mransac_pmf\u001b[m\u001b[m\n"]}], "source": ["!ls -lh /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/ground_segmentation"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-20 14:15:41,887 - INFO - Reading PLY file: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/denoising/trino_enel_denoised.ply\n", "2025-07-20 14:15:41,895 - INFO - Loaded 36738 points from /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/denoising/trino_enel_denoised.ply\n"]}], "source": ["import open3d as o3d\n", "\n", "# Use the resolved and validated file\n", "logger.info(f\"Reading PLY file: {point_cloud_file.resolve()}\")\n", "\n", "pcd = o3d.io.read_point_cloud(str(point_cloud_file))\n", "\n", "if not pcd.has_points():\n", "    raise ValueError(\"Loaded PLY file contains no points.\")\n", "\n", "points = np.asarray(pcd.points)\n", "logger.info(f\"Loaded {points.shape[0]} points from {point_cloud_file}\")\n"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-20 14:15:41,908 - INFO - Estimated ground threshold (Z median): 0.77\n", "2025-07-20 14:15:41,910 - INFO - Classified 22890 ground and 13848 non-ground points.\n"]}], "source": ["# ## Simplified CSF (Mock logic, replaceable with physics sim)\n", "z_median = np.median(points[:, 2])\n", "logger.info(f\"Estimated ground threshold (Z median): {z_median:.2f}\")\n", "\n", "ground_mask = points[:, 2] < (z_median + classification_threshold)\n", "ground_points = points[ground_mask]\n", "nonground_points = points[~ground_mask]\n", "\n", "logger.info(f\"Classified {ground_points.shape[0]} ground and {nonground_points.shape[0]} non-ground points.\")"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-20 14:15:41,920 - INFO - Saved: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/output_runs/denoising/trino_enel_20250720_141541/analysis_output/trino_enel_ground.ply\n", "2025-07-20 14:15:41,923 - INFO - Saved: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/output_runs/denoising/trino_enel_20250720_141541/analysis_output/trino_enel_nonground.ply\n"]}], "source": ["# ## Save Output .PLY\n", "def save_ply(path, points_array):\n", "    pc = o3d.geometry.PointCloud()\n", "    pc.points = o3d.utility.Vector3dVector(points_array)\n", "    o3d.io.write_point_cloud(str(path), pc)\n", "    logger.info(f\"Saved: {path}\")\n", "\n", "# Save the point clouds to the appropriate output paths\n", "analysis_output = output_path / 'analysis_output'\n", "analysis_output.mkdir(parents=True, exist_ok=True)\n", "\n", "# Save ground and nonground points to the analysis_output directory\n", "save_ply(analysis_output / f\"{site_name}_ground.ply\", ground_points)\n", "save_ply(analysis_output / f\"{site_name}_nonground.ply\", nonground_points)"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-20 14:15:41,928 - INFO - Ground Ratio: 0.6231\n", "2025-07-20 14:15:41,928 - INFO - Non-Ground Ratio: 0.3769\n"]}], "source": ["# Ground/Non-Ground ratio\n", "ground_count = ground_points.shape[0]\n", "nonground_count = nonground_points.shape[0]\n", "total_points = ground_count + nonground_count\n", "\n", "ground_ratio = ground_count / total_points\n", "logger.info(f\"Ground Ratio: {ground_ratio:.4f}\")\n", "\n", "nonground_ratio = nonground_count / total_points\n", "logger.info(f\"Non-Ground Ratio: {nonground_ratio:.4f}\")"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-20 14:15:41,934 - INFO - ground_z_mean:{ground_z_mean}\n", "2025-07-20 14:15:41,935 - INFO - nonground_z_mean:2.4632506017716156\n", "2025-07-20 14:15:41,935 - INFO - z_separation: 1.9283355515313358\n"]}], "source": ["ground_z_mean = ground_points[:, 2].mean()\n", "nonground_z_mean = nonground_points[:, 2].mean()\n", "z_separation = nonground_z_mean - ground_z_mean\n", "\n", "logger.info(\"ground_z_mean:{ground_z_mean}\")\n", "logger.info(f\"nonground_z_mean:{nonground_z_mean}\")\n", "logger.info(f\"z_separation: {z_separation}\")\n"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-20 14:15:41,941 - INFO -   Bounding Box Sizes (X, Y, Z):\n", "2025-07-20 14:15:41,941 - INFO - --------------------------------------------------\n", "2025-07-20 14:15:41,943 - INFO -   Ground:     [1.570544e+03 1.708630e+03 1.272000e+00]\n", "2025-07-20 14:15:41,943 - INFO -   Non-Ground: [1570.423 1722.145   11.963]\n"]}], "source": ["# Bounding Box Stats\n", "def bounding_box_stats(points):\n", "    min_bound = np.min(points, axis=0)\n", "    max_bound = np.max(points, axis=0)\n", "    return max_bound - min_bound\n", "\n", "ground_bbox = bounding_box_stats(ground_points)\n", "nonground_bbox = bounding_box_stats(nonground_points)\n", "\n", "logger.info(\"  Bounding Box Sizes (X, Y, Z):\")\n", "logger.info(\"-\" * 50)\n", "logger.info(f\"  Ground:     {ground_bbox}\")\n", "logger.info(f\"  Non-Ground: {nonground_bbox}\")"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Height (Z) Distribution Plot\n", "plt.figure(figsize=(10, 5))\n", "plt.hist(ground_points[:, 2], bins=100, alpha=0.6, label='Ground Z')\n", "plt.hist(nonground_points[:, 2], bins=100, alpha=0.6, label='Non-Ground Z')\n", "plt.legend()\n", "plt.title(\"Z-Height Distribution\")\n", "plt.xlabel(\"Z (Elevation)\")\n", "plt.ylabel(\"Point Count\")\n", "plt.grid(True)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-20 14:15:42,131 - INFO - CSF Ground Segmentation - Ready!\n", "2025-07-20 14:15:42,134 - INFO - ==================================================\n", "2025-07-20 14:15:42,138 - INFO - Project: ENEL/trino_enel\n", "2025-07-20 14:15:42,141 - INFO - --------------------------------------------------\n", "2025-07-20 14:15:42,143 - INFO - Input path: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/denoising/trino_enel_denoised.ply\n", "2025-07-20 14:15:42,147 - INFO - Output path: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/ground_segmentation\n", "2025-07-20 14:15:42,151 - INFO - Current run output: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/output_runs/denoising/trino_enel_20250720_141541\n", "2025-07-20 14:15:42,153 - INFO - CSF Parameters: resolution=0.25m, threshold=0.4m, rigidness=3\n", "2025-07-20 14:15:42,154 - INFO - Ground points: 22890\n", "2025-07-20 14:15:42,155 - INFO - Non-ground points: 13848\n", "2025-07-20 14:15:42,160 - INFO - Total: 36738\n"]}], "source": ["# Final readiness logger.info\n", "logger.info(\"CSF Ground Segmentation - Ready!\")\n", "logger.info(\"=\" * 50)\n", "logger.info(f\"Project: {project_type}/{site_name}\")\n", "logger.info(\"-\" * 50)\n", "logger.info(f\"Input path: {point_cloud_file}\")\n", "logger.info(f\"Output path: {ground_seg_path}\")\n", "logger.info(f\"Current run output: {output_path}\")\n", "logger.info(f\"CSF Parameters: resolution={cloth_resolution}m, threshold={classification_threshold}m, rigidness={rigidness}\")\n", "logger.info(f\"Ground points: {ground_points.shape[0]}\")\n", "logger.info(f\"Non-ground points: {nonground_points.shape[0]}\")\n", "logger.info(f\"Total: {ground_points.shape[0] + nonground_points.shape[0]}\")\n"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [], "source": ["# Visualize the results\n", "import open3d as o3d\n", "import numpy as np\n", "\n", "# Create point cloud for ground\n", "pcd_ground = o3d.geometry.PointCloud()\n", "pcd_ground.points = o3d.utility.Vector3dVector(ground_points)\n", "pcd_ground.paint_uniform_color([0.0, 1.0, 0.0])  # Green\n", "\n", "# Create point cloud for non-ground\n", "pcd_nonground = o3d.geometry.PointCloud()\n", "pcd_nonground.points = o3d.utility.Vector3dVector(nonground_points)\n", "pcd_nonground.paint_uniform_color([1.0, 0.0, 0.0])  # Red\n", "\n", "# Show both together\n", "o3d.visualization.draw_geometries([pcd_ground, pcd_nonground],\n", "                                  window_name=\"Ground vs Non-Ground\",\n", "                                  point_show_normal=False)\n"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-20 14:15:44,903 - INFO - Saved: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/ground_segmentation/csf/trino_enel_ground.ply\n", "2025-07-20 14:15:44,905 - INFO - Saved: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/ground_segmentation/csf/trino_enel_nonground.ply\n"]}], "source": ["# Satisfied with the results save the final output for next stage \n", "save_ply(ground_seg_path / f\"{method_name}/{site_name}_ground.ply\", ground_points)\n", "save_ply(ground_seg_path / f\"{method_name}/{site_name}_nonground.ply\", nonground_points)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}