{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# AI/ML-Based Local Non-Uniform Correction\n", "\n", "This notebook implements machine learning approaches for local point cloud corrections, going beyond traditional spatial interpolation methods.\n", "\n", "**AI/ML Methods Implemented:**\n", "1. **Neural Network Regression** for spatial correction prediction\n", "2. **Gaussian Process Regression** for uncertainty quantification\n", "3. **Random Forest** for feature-based correction modeling\n", "4. **Deep Learning** for pattern recognition in systematic errors\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: July 2025  \n", "**Project**: As-Built Foundation Analysis"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# Parameters (Papermill)\n", "site_name = \"trino_enel\"\n", "ground_method = \"ransac_pmf\"\n", "ml_method = \"neural_network\"  # Options: neural_network, gaussian_process, random_forest, ensemble\n", "feature_engineering = True\n", "uncertainty_quantification = True\n", "save_results = True"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"ename": "ModuleNotFoundError", "evalue": "No module named 'src.config'", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mModuleNotFoundError\u001b[39m                       <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[2]\u001b[39m\u001b[32m, line 31\u001b[39m\n\u001b[32m     28\u001b[39m project_root = Path().resolve().parents[\u001b[32m2\u001b[39m]\n\u001b[32m     29\u001b[39m sys.path.append(\u001b[38;5;28mstr\u001b[39m(project_root))\n\u001b[32m---> \u001b[39m\u001b[32m31\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01msrc\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mconfig\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mpaths\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m get_processed_data_path\n\u001b[32m     33\u001b[39m \u001b[38;5;66;03m# Setup paths\u001b[39;00m\n\u001b[32m     34\u001b[39m output_dir = get_processed_data_path(site_name, \u001b[33m\"\u001b[39m\u001b[33mml_local_alignment\u001b[39m\u001b[33m\"\u001b[39m)\n", "\u001b[31mModuleNotFoundError\u001b[39m: No module named 'src.config'"]}], "source": ["# Imports\n", "import sys\n", "from pathlib import Path\n", "import numpy as np\n", "import pandas as pd\n", "import open3d as o3d\n", "import matplotlib.pyplot as plt\n", "from scipy.spatial import cKDTree\n", "import json\n", "from datetime import datetime\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# ML/AI Libraries\n", "import torch\n", "import torch.nn as nn\n", "import torch.optim as optim\n", "from torch.utils.data import DataLoader, TensorDataset\n", "from sklearn.ensemble import RandomForestRegressor\n", "from sklearn.gaussian_process import GaussianProcessRegressor\n", "from sklearn.gaussian_process.kernels import RBF, WhiteKernel, Matern\n", "from sklearn.model_selection import train_test_split, cross_val_score\n", "from sklearn.preprocessing import StandardScaler, PolynomialFeatures\n", "from sklearn.metrics import mean_squared_error, r2_score\n", "import joblib\n", "\n", "# Add project root to path\n", "project_root = Path().resolve().parents[2]\n", "sys.path.append(str(project_root))\n", "\n", "import sys\n", "from pathlib import Path\n", "\n", "# Add the `notebooks` folder to sys.path\n", "notebooks_root = Path(__file__).resolve().parents[1] if '__file__' in globals() else Path.cwd().parents[1]\n", "\n", "if str(notebooks_root) not in sys.path:\n", "    sys.path.insert(0, str(notebooks_root))\n", "\n", "# Now import from shared package\n", "from shared.config import get_data_path, get_output_path, get_mlflow_tracking_uri, find_latest_file, get_processed_data_path\n", "\n", "# Setup paths\n", "output_dir = get_processed_data_path(site_name, \"ml_local_alignment\")\n", "output_dir.mkdir(parents=True, exist_ok=True)\n", "\n", "print(\"=== AI/ML-BASED LOCAL NON-UNIFORM CORRECTION ===\")\n", "print(f\"Site: {site_name}\")\n", "print(f\"ML Method: {ml_method}\")\n", "print(f\"Feature Engineering: {feature_engineering}\")\n", "print(f\"Output directory: {output_dir}\")\n", "print(f\"Device: {'CUDA' if torch.cuda.is_available() else 'CPU'}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Data Loading and Preparation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load drone point cloud\n", "drone_file = f\"../../../data/processed/{site_name}/ground_segmentation/{ground_method}/{site_name}_nonground.ply\"\n", "drone_pcd = o3d.io.read_point_cloud(drone_file)\n", "drone_points = np.asarray(drone_pcd.points)\n", "\n", "# Load IFC metadata for ground truth\n", "ifc_metadata_file = f\"../../../data/processed/{site_name}/ifc_metadata/GRE.EEC.S.00.IT.P.14353.00.265_enhanced_metadata.csv\"\n", "ifc_df = pd.read_csv(ifc_metadata_file)\n", "\n", "# Extract pile coordinates as ground truth\n", "pile_mask = ifc_df['Name'].str.contains('Pile', case=False, na=False)\n", "# Check available columns first\n", "print(f\"IFC metadata columns: {list(ifc_df.columns)}\")\n", "print(f\"IFC metadata shape: {ifc_df.shape}\")\n", "\n", "# Check for different possible coordinate column names\n", "coord_columns = []\n", "for col in ifc_df.columns:\n", "    if any(coord in col.lower() for coord in ['x', 'easting', 'longitude']):\n", "        coord_columns.append(col)\n", "        break\n", "for col in ifc_df.columns:\n", "    if any(coord in col.lower() for coord in ['y', 'northing', 'latitude']):\n", "        coord_columns.append(col)\n", "        break\n", "for col in ifc_df.columns:\n", "    if any(coord in col.lower() for coord in ['z', 'elevation', 'height']):\n", "        coord_columns.append(col)\n", "        break\n", "\n", "print(f\"Detected coordinate columns: {coord_columns}\")\n", "\n", "# Extract pile coordinates using detected column names\n", "if len(coord_columns) == 3:\n", "    pile_coords = ifc_df[pile_mask][coord_columns].dropna().values\n", "else:\n", "    print(f\"Error: Could not find all coordinate columns. Available columns: {list(ifc_df.columns)}\")\n", "    # Fallback: try common column names\n", "    try:\n", "        pile_coords = ifc_df[pile_mask][['X', 'Y', 'Z']].dropna().values\n", "    except KeyError:\n", "        try:\n", "            pile_coords = ifc_df[pile_mask][['x', 'y', 'z']].dropna().values\n", "        except KeyError:\n", "            print(\"Please check the coordinate column names in your IFC metadata file.\")\n", "            print(f\"Available columns: {list(ifc_df.columns)}\")\n", "            raise\n", "\n", "print(f\"Loaded drone points: {len(drone_points):,}\")\n", "print(f\"Loaded pile coordinates: {len(pile_coords):,}\")\n", "\n", "# Create training data by finding correspondences\n", "def create_training_data(drone_pts, pile_pts, max_distance=5.0):\n", "    \"\"\"Create training dataset from drone-pile correspondences\"\"\"\n", "    \n", "    drone_tree = cKDTree(drone_pts[:, :2])\n", "    \n", "    training_features = []\n", "    training_targets = []\n", "    \n", "    for pile_pt in pile_pts:\n", "        distances, indices = drone_tree.query(pile_pt[:2], k=1, distance_upper_bound=max_distance)\n", "        \n", "        if distances < np.inf:\n", "            drone_pt = drone_pts[indices]\n", "            \n", "            # Features: drone coordinates\n", "            features = drone_pt\n", "            \n", "            # Target: Z correction needed\n", "            z_correction = pile_pt[2] - drone_pt[2]\n", "            \n", "            training_features.append(features)\n", "            training_targets.append(z_correction)\n", "    \n", "    return np.array(training_features), np.array(training_targets)\n", "\n", "# Create training dataset\n", "X_train_raw, y_train = create_training_data(drone_points, pile_coords)\n", "print(f\"Training samples: {len(X_train_raw)}\")\n", "print(f\"Z correction range: [{y_train.min():.2f}, {y_train.max():.2f}]m\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Feature Engineering"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def engineer_features(points, include_spatial=True, include_geometric=True, include_contextual=True):\n", "    \"\"\"Engineer features for ML-based correction prediction\"\"\"\n", "    \n", "    features = []\n", "    feature_names = []\n", "    \n", "    # Basic coordinates\n", "    features.extend([points[:, 0], points[:, 1], points[:, 2]])\n", "    feature_names.extend(['x', 'y', 'z'])\n", "    \n", "    if include_spatial:\n", "        # Spatial features\n", "        centroid = np.mean(points, axis=0)\n", "        \n", "        # Distance from centroid\n", "        dist_from_center = np.linalg.norm(points - centroid, axis=1)\n", "        features.append(dist_from_center)\n", "        feature_names.append('dist_from_center')\n", "        \n", "        # Normalized coordinates\n", "        points_norm = (points - centroid) / np.std(points, axis=0)\n", "        features.extend([points_norm[:, 0], points_norm[:, 1]])\n", "        feature_names.extend(['x_norm', 'y_norm'])\n", "        \n", "        # Polar coordinates\n", "        angles = np.arctan2(points[:, 1] - centroid[1], points[:, 0] - centroid[0])\n", "        features.extend([np.cos(angles), np.sin(angles)])\n", "        feature_names.extend(['cos_angle', 'sin_angle'])\n", "    \n", "    if include_geometric:\n", "        # Geometric features\n", "        # Polynomial features\n", "        features.extend([points[:, 0]**2, points[:, 1]**2, points[:, 0]*points[:, 1]])\n", "        feature_names.extend(['x_squared', 'y_squared', 'xy_product'])\n", "        \n", "        # Radial basis functions\n", "        rbf_centers = np.percentile(points[:, :2], [25, 50, 75], axis=0)\n", "        for i, center in enumerate(rbf_centers):\n", "            rbf_dist = np.linalg.norm(points[:, :2] - center, axis=1)\n", "            features.append(np.exp(-rbf_dist**2 / (2 * 100**2)))  # RBF with 100m bandwidth\n", "            feature_names.append(f'rbf_{i}')\n", "    \n", "    if include_contextual:\n", "        # Contextual features (local neighborhood)\n", "        tree = cKDTree(points[:, :2])\n", "        \n", "        # Local density\n", "        neighbor_counts = tree.query_ball_point(points[:, :2], r=50.0, return_length=True)\n", "        features.append(neighbor_counts)\n", "        feature_names.append('local_density')\n", "        \n", "        # Local Z statistics\n", "        local_z_std = []\n", "        for i, pt in enumerate(points):\n", "            neighbors = tree.query_ball_point(pt[:2], r=25.0)\n", "            if len(neighbors) > 1:\n", "                local_z_std.append(np.std(points[neighbors, 2]))\n", "            else:\n", "                local_z_std.append(0.0)\n", "        features.append(local_z_std)\n", "        feature_names.append('local_z_std')\n", "    \n", "    return np.column_stack(features), feature_names\n", "\n", "if feature_engineering:\n", "    # Engineer features for training data\n", "    X_train_engineered, feature_names = engineer_features(X_train_raw)\n", "    \n", "    # Engineer features for all drone points\n", "    X_all_engineered, _ = engineer_features(drone_points)\n", "    \n", "    print(f\"Engineered features: {len(feature_names)}\")\n", "    print(f\"Feature names: {feature_names}\")\n", "    \n", "    X_train = X_train_engineered\n", "    X_all = X_all_engineered\n", "else:\n", "    X_train = X_train_raw\n", "    X_all = drone_points\n", "    feature_names = ['x', 'y', 'z']\n", "\n", "# Normalize features\n", "scaler = StandardScaler()\n", "X_train_scaled = scaler.fit_transform(X_train)\n", "X_all_scaled = scaler.transform(X_all)\n", "\n", "print(f\"Training data shape: {X_train_scaled.shape}\")\n", "print(f\"All data shape: {X_all_scaled.shape}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Neural Network Model"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class CorrectionNet(nn.Module):\n", "    \"\"\"Neural network for predicting Z corrections\"\"\"\n", "    \n", "    def __init__(self, input_dim, hidden_dims=[128, 64, 32], dropout=0.2):\n", "        super(CorrectionNet, self).__init__()\n", "        \n", "        layers = []\n", "        prev_dim = input_dim\n", "        \n", "        for hidden_dim in hidden_dims:\n", "            layers.extend([\n", "                nn.Linear(prev_dim, hidden_dim),\n", "                nn.ReLU(),\n", "                nn.BatchNorm1d(hidden_dim),\n", "                nn.Dropout(dropout)\n", "            ])\n", "            prev_dim = hidden_dim\n", "        \n", "        # Output layer\n", "        layers.append(nn.Linear(prev_dim, 1))\n", "        \n", "        self.network = nn.Sequential(*layers)\n", "    \n", "    def forward(self, x):\n", "        return self.network(x).squeeze()\n", "\n", "def train_neural_network(X_train, y_train, epochs=200, batch_size=64, learning_rate=0.001):\n", "    \"\"\"Train neural network for correction prediction\"\"\"\n", "    \n", "    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "    \n", "    # Split data\n", "    X_train_split, X_val, y_train_split, y_val = train_test_split(\n", "        X_train, y_train, test_size=0.2, random_state=42\n", "    )\n", "    \n", "    # Convert to tensors\n", "    X_train_tensor = torch.FloatTensor(X_train_split).to(device)\n", "    y_train_tensor = torch.FloatTensor(y_train_split).to(device)\n", "    X_val_tensor = torch.FloatTensor(X_val).to(device)\n", "    y_val_tensor = torch.FloatTensor(y_val).to(device)\n", "    \n", "    # Create data loaders\n", "    train_dataset = TensorDataset(X_train_tensor, y_train_tensor)\n", "    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)\n", "    \n", "    # Initialize model\n", "    model = CorrectionNet(X_train.shape[1]).to(device)\n", "    criterion = nn.MS<PERSON><PERSON>()\n", "    optimizer = optim.<PERSON>(model.parameters(), lr=learning_rate, weight_decay=1e-5)\n", "    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=20, factor=0.5)\n", "    \n", "    # Training loop\n", "    train_losses = []\n", "    val_losses = []\n", "    \n", "    for epoch in range(epochs):\n", "        model.train()\n", "        epoch_loss = 0.0\n", "        \n", "        for batch_X, batch_y in train_loader:\n", "            optimizer.zero_grad()\n", "            outputs = model(batch_X)\n", "            loss = criterion(outputs, batch_y)\n", "            loss.backward()\n", "            optimizer.step()\n", "            epoch_loss += loss.item()\n", "        \n", "        # Validation\n", "        model.eval()\n", "        with torch.no_grad():\n", "            val_outputs = model(X_val_tensor)\n", "            val_loss = criterion(val_outputs, y_val_tensor)\n", "        \n", "        train_losses.append(epoch_loss / len(train_loader))\n", "        val_losses.append(val_loss.item())\n", "        \n", "        scheduler.step(val_loss)\n", "        \n", "        if epoch % 50 == 0:\n", "            print(f\"Epoch {epoch}: Train Loss = {train_losses[-1]:.4f}, Val Loss = {val_losses[-1]:.4f}\")\n", "    \n", "    return model, train_losses, val_losses\n", "\n", "if ml_method == \"neural_network\":\n", "    print(\"Training Neural Network...\")\n", "    nn_model, train_losses, val_losses = train_neural_network(X_train_scaled, y_train)\n", "    \n", "    # Plot training curves\n", "    plt.figure(figsize=(10, 4))\n", "    plt.subplot(1, 2, 1)\n", "    plt.plot(train_losses, label='Training Loss')\n", "    plt.plot(val_losses, label='Validation Loss')\n", "    plt.xlabel('Epoch')\n", "    plt.ylabel('MSE Loss')\n", "    plt.title('Neural Network Training')\n", "    plt.legend()\n", "    plt.grid(True)\n", "    \n", "    # Make predictions\n", "    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "    nn_model.eval()\n", "    with torch.no_grad():\n", "        X_all_tensor = torch.FloatTensor(X_all_scaled).to(device)\n", "        nn_predictions = nn_model(X_all_tensor).cpu().numpy()\n", "    \n", "    print(f\"Neural Network predictions range: [{nn_predictions.min():.2f}, {nn_predictions.max():.2f}]m\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Gaussian Process Regression"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def train_gaussian_process(X_train, y_train):\n", "    \"\"\"Train Gaussian Process for correction prediction with uncertainty\"\"\"\n", "    \n", "    # Define kernel\n", "    kernel = RBF(length_scale=100.0, length_scale_bounds=(10.0, 1000.0)) + \\\n", "             WhiteKernel(noise_level=1.0, noise_level_bounds=(0.1, 10.0))\n", "    \n", "    # Initialize GP\n", "    gp = GaussianProcessRegressor(\n", "        kernel=kernel,\n", "        alpha=1e-6,\n", "        normalize_y=True,\n", "        n_restarts_optimizer=5,\n", "        random_state=42\n", "    )\n", "    \n", "    # Subsample for computational efficiency\n", "    if len(X_train) > 1000:\n", "        indices = np.random.choice(len(X_train), 1000, replace=False)\n", "        X_train_sub = X_train[indices]\n", "        y_train_sub = y_train[indices]\n", "    else:\n", "        X_train_sub = X_train\n", "        y_train_sub = y_train\n", "    \n", "    print(f\"Training GP with {len(X_train_sub)} samples...\")\n", "    gp.fit(X_train_sub, y_train_sub)\n", "    \n", "    print(f\"GP kernel: {gp.kernel_}\")\n", "    print(f\"GP log-likelihood: {gp.log_marginal_likelihood():.2f}\")\n", "    \n", "    return gp\n", "\n", "if ml_method == \"gaussian_process\" or ml_method == \"ensemble\":\n", "    print(\"Training Gaussian Process...\")\n", "    gp_model = train_gaussian_process(X_train_scaled, y_train)\n", "    \n", "    # Make predictions with uncertainty\n", "    print(\"Making GP predictions...\")\n", "    gp_predictions, gp_std = gp_model.predict(X_all_scaled, return_std=True)\n", "    \n", "    print(f\"GP predictions range: [{gp_predictions.min():.2f}, {gp_predictions.max():.2f}]m\")\n", "    print(f\"GP uncertainty range: [{gp_std.min():.2f}, {gp_std.max():.2f}]m\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Random Forest Regression"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def train_random_forest(X_train, y_train):\n", "    \"\"\"Train Random Forest for correction prediction\"\"\"\n", "    \n", "    rf = RandomForestRegressor(\n", "        n_estimators=200,\n", "        max_depth=15,\n", "        min_samples_split=5,\n", "        min_samples_leaf=2,\n", "        max_features='sqrt',\n", "        random_state=42,\n", "        n_jobs=-1\n", "    )\n", "    \n", "    print(f\"Training Random Forest with {len(X_train)} samples...\")\n", "    rf.fit(X_train, y_train)\n", "    \n", "    # Feature importance\n", "    if feature_engineering:\n", "        feature_importance = pd.DataFrame({\n", "            'feature': feature_names,\n", "            'importance': rf.feature_importances_\n", "        }).sort_values('importance', ascending=False)\n", "        \n", "        print(\"\\nTop 10 Most Important Features:\")\n", "        print(feature_importance.head(10))\n", "    \n", "    return rf\n", "\n", "if ml_method == \"random_forest\" or ml_method == \"ensemble\":\n", "    print(\"Training Random Forest...\")\n", "    rf_model = train_random_forest(X_train_scaled, y_train)\n", "    \n", "    # Make predictions\n", "    rf_predictions = rf_model.predict(X_all_scaled)\n", "    \n", "    print(f\"RF predictions range: [{rf_predictions.min():.2f}, {rf_predictions.max():.2f}]m\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. <PERSON> Method"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if ml_method == \"ensemble\":\n", "    print(\"\\n=== ENSEMBLE PREDICTIONS ===\")\n", "    \n", "    # Combine predictions from all models\n", "    predictions = []\n", "    weights = []\n", "    \n", "    if 'nn_predictions' in locals():\n", "        predictions.append(nn_predictions)\n", "        weights.append(0.4)  # Neural network weight\n", "        print(\"Added Neural Network to ensemble\")\n", "    \n", "    if 'gp_predictions' in locals():\n", "        predictions.append(gp_predictions)\n", "        weights.append(0.3)  # Gaussian process weight\n", "        print(\"Added Gaussian Process to ensemble\")\n", "    \n", "    if 'rf_predictions' in locals():\n", "        predictions.append(rf_predictions)\n", "        weights.append(0.3)  # Random forest weight\n", "        print(\"Added Random Forest to ensemble\")\n", "    \n", "    # Weighted ensemble\n", "    if len(predictions) > 1:\n", "        weights = np.array(weights) / np.sum(weights)  # Normalize weights\n", "        ensemble_predictions = np.average(predictions, axis=0, weights=weights)\n", "        \n", "        # Uncertainty from ensemble variance\n", "        ensemble_uncertainty = np.std(predictions, axis=0)\n", "        \n", "        print(f\"Ensemble predictions range: [{ensemble_predictions.min():.2f}, {ensemble_predictions.max():.2f}]m\")\n", "        print(f\"Ensemble uncertainty range: [{ensemble_uncertainty.min():.2f}, {ensemble_uncertainty.max():.2f}]m\")\n", "        \n", "        final_predictions = ensemble_predictions\n", "        final_uncertainty = ensemble_uncertainty\n", "    else:\n", "        print(\"Not enough models for ensemble, using single model\")\n", "        final_predictions = predictions[0]\n", "        final_uncertainty = np.zeros_like(final_predictions)\n", "else:\n", "    # Use single model predictions\n", "    if ml_method == \"neural_network\":\n", "        final_predictions = nn_predictions\n", "        final_uncertainty = np.zeros_like(nn_predictions)\n", "    elif ml_method == \"gaussian_process\":\n", "        final_predictions = gp_predictions\n", "        final_uncertainty = gp_std\n", "    elif ml_method == \"random_forest\":\n", "        final_predictions = rf_predictions\n", "        final_uncertainty = np.zeros_like(rf_predictions)\n", "\n", "print(f\"\\nFinal ML predictions range: [{final_predictions.min():.2f}, {final_predictions.max():.2f}]m\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Apply ML Corrections and Validation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Apply ML corrections\n", "drone_ml_corrected = drone_points.copy()\n", "drone_ml_corrected[:, 2] += final_predictions\n", "\n", "print(f\"Applied ML corrections to {len(drone_points):,} points\")\n", "print(f\"Original Z range: [{drone_points[:, 2].min():.2f}, {drone_points[:, 2].max():.2f}]m\")\n", "print(f\"ML corrected Z range: [{drone_ml_corrected[:, 2].min():.2f}, {drone_ml_corrected[:, 2].max():.2f}]m\")\n", "\n", "# Validation function\n", "def validate_ml_corrections(drone_corrected, pile_coords, max_distance=2.0):\n", "    \"\"\"Validate ML correction quality\"\"\"\n", "    \n", "    drone_tree = cKDTree(drone_corrected[:, :2])\n", "    validation_errors = []\n", "    \n", "    for pile_pt in pile_coords:\n", "        distances, indices = drone_tree.query(pile_pt[:2], k=1, distance_upper_bound=max_distance)\n", "        \n", "        if distances < np.inf:\n", "            drone_z = drone_corrected[indices, 2]\n", "            pile_z = pile_pt[2]\n", "            error = abs(drone_z - pile_z)\n", "            validation_errors.append(error)\n", "    \n", "    if len(validation_errors) > 0:\n", "        errors = np.array(validation_errors)\n", "        return {\n", "            'rmse': np.sqrt(np.mean(errors**2)),\n", "            'mean_error': np.mean(errors),\n", "            'median_error': np.median(errors),\n", "            'max_error': np.max(errors),\n", "            'std_error': np.std(errors),\n", "            'num_validated': len(errors),\n", "            'sub_meter_pct': np.sum(errors < 1.0) / len(errors) * 100,\n", "            'sub_half_meter_pct': np.sum(errors < 0.5) / len(errors) * 100\n", "        }\n", "    return None\n", "\n", "# Validate ML corrections\n", "ml_validation = validate_ml_corrections(drone_ml_corrected, pile_coords)\n", "\n", "if ml_validation:\n", "    print(\"\\n=== ML CORRECTION VALIDATION ===\")\n", "    print(f\"Method: {ml_method.upper()}\")\n", "    print(f\"Validated points: {ml_validation['num_validated']}\")\n", "    print(f\"RMSE: {ml_validation['rmse']:.3f}m\")\n", "    print(f\"Mean error: {ml_validation['mean_error']:.3f}m\")\n", "    print(f\"Median error: {ml_validation['median_error']:.3f}m\")\n", "    print(f\"Max error: {ml_validation['max_error']:.3f}m\")\n", "    print(f\"Sub-meter accuracy: {ml_validation['sub_meter_pct']:.1f}%\")\n", "    print(f\"Sub-half-meter accuracy: {ml_validation['sub_half_meter_pct']:.1f}%\")\n", "    \n", "    # Compare with global correction\n", "    global_z_offset = 154.69\n", "    drone_global = drone_points.copy()\n", "    drone_global[:, 2] += global_z_offset\n", "    global_validation = validate_ml_corrections(drone_global, pile_coords)\n", "    \n", "    if global_validation:\n", "        print(\"\\n=== COMPARISON WITH GLOBAL CORRECTION ===\")\n", "        print(f\"Global RMSE: {global_validation['rmse']:.3f}m\")\n", "        print(f\"ML RMSE: {ml_validation['rmse']:.3f}m\")\n", "        improvement = (global_validation['rmse'] - ml_validation['rmse']) / global_validation['rmse'] * 100\n", "        print(f\"Improvement: {improvement:.1f}% reduction in RMSE\")\n", "        \n", "        print(f\"\\nSub-meter accuracy improvement:\")\n", "        print(f\"  Global: {global_validation['sub_meter_pct']:.1f}%\")\n", "        print(f\"  ML: {ml_validation['sub_meter_pct']:.1f}%\")\n", "        print(f\"  Gain: +{ml_validation['sub_meter_pct'] - global_validation['sub_meter_pct']:.1f}%\")\n", "else:\n", "    print(\"No validation points found\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}