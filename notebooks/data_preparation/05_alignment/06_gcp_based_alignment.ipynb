{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Ground Control Point (GCP) Based Alignment\n", "\n", "This notebook implements GCP-based alignment using manually identified ground control points to achieve more accurate alignment between drone and IFC point clouds.\n", "\n", "**Key Features:**\n", "- Manual GCP identification and matching\n", "- Similarity transformation (rotation, translation, scale)\n", "- Quality assessment with residual analysis\n", "- Comparison with coordinate-only alignment\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: July 2025  \n", "**Project**: As-Built Foundation Analysis"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import sys\n", "from pathlib import Path\n", "\n", "# Add the `notebooks` folder to sys.path\n", "notebooks_root = Path(__file__).resolve().parents[1] if '__file__' in globals() else Path.cwd().parents[1]\n", "\n", "if str(notebooks_root) not in sys.path:\n", "    sys.path.insert(0, str(notebooks_root))\n", "\n", "# Now import from shared package\n", "from shared.config import get_data_path, get_output_path, get_mlflow_tracking_uri, find_latest_file, get_processed_data_path\n", "\n", "# Parameters (Papermill)\n", "ground_method = \"csf\"  # Ground segmentation method\n", "site_name = \"trino_enel\"\n", "save_results = True\n", "quality_sample_size = 5000  # For quality assessment sampling\n", "\n", "# Option to use detected coordinates instead of manual ones\n", "use_detected_gcp = False  # Set to True to use automated detection results"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== GROUND CONTROL POINT BASED ALIGNMENT ===\n", "Ground method: csf\n", "Site: trino_enel\n", "Output directory: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/gcp_alignment\n", "Using GCP-based similarity transformation\n"]}], "source": ["# Imports\n", "import numpy as np\n", "import pandas as pd\n", "import open3d as o3d\n", "import laspy\n", "from pathlib import Path\n", "import json\n", "from scipy.spatial import cKDTree\n", "from scipy.spatial.transform import Rotation\n", "from scipy.optimize import least_squares\n", "import matplotlib.pyplot as plt\n", "from datetime import datetime\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "\n", "# Setup paths using shared config\n", "output_dir = get_processed_data_path(site_name, \"gcp_alignment\")\n", "\n", "print(\"=== GROUND CONTROL POINT BASED ALIGNMENT ===\")\n", "print(f\"Ground method: {ground_method}\")\n", "print(f\"Site: {site_name}\")\n", "print(f\"Output directory: {output_dir}\")\n", "print(f\"Using GCP-based similarity transformation\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Data Loading"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading data for GCP-based alignment...\n", "Drone file: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/ground_segmentation/csf/trino_enel_nonground.ply\n", "IFC metadata file: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/ifc_metadata/GRE.EEC.S.00.IT.P.14353.00.265_enhanced_metadata.csv\n", "IFC point cloud file: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply\n"]}], "source": ["# Define file paths using shared config\n", "ground_seg_path = get_processed_data_path(site_name, \"ground_segmentation\") / ground_method\n", "ifc_metadata_path = get_processed_data_path(site_name, \"ifc_metadata\")\n", "ifc_pointcloud_path = get_processed_data_path(site_name, \"ifc_pointclouds\")\n", "\n", "# Find the actual files\n", "drone_file = find_latest_file(ground_seg_path, f\"{site_name}_nonground.ply\")\n", "ifc_metadata_file = find_latest_file(ifc_metadata_path, \"*enhanced_metadata.csv\")\n", "ifc_pointcloud_file = find_latest_file(ifc_pointcloud_path, \"*data_driven.ply\")\n", "\n", "print(\"Loading data for GCP-based alignment...\")\n", "print(f\"Drone file: {drone_file}\")\n", "print(f\"IFC metadata file: {ifc_metadata_file}\")\n", "print(f\"IFC point cloud file: {ifc_pointcloud_file}\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded drone scan: 13,848 points\n"]}], "source": ["def load_drone_points(drone_path):\n", "    \"\"\"Load drone point cloud\"\"\"\n", "    drone_file = Path(drone_path)\n", "    \n", "    if not drone_file.exists():\n", "        raise FileNotFoundError(f\"Drone file not found: {drone_path}\")\n", "    \n", "    if drone_file.suffix.lower() == \".las\":\n", "        drone_las = laspy.read(drone_file)\n", "        drone_points = drone_las.xyz\n", "    elif drone_file.suffix.lower() == \".ply\":\n", "        drone_pcd = o3d.io.read_point_cloud(str(drone_file))\n", "        drone_points = np.asarray(drone_pcd.points)\n", "    else:\n", "        raise ValueError(\"Unsupported drone file format. Use .las or .ply\")\n", "    \n", "    print(f\"Loaded drone scan: {drone_points.shape[0]:,} points\")\n", "    return drone_points\n", "\n", "# Load data\n", "drone_points = load_drone_points(drone_file)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading IFC metadata coordinates...\n", "Loaded IFC metadata: 14,460 records\n", "Valid IFC coordinates: 14,460 points\n", "Coordinate ranges:\n", "  X: 435267.20 to 436719.95\n", "  Y: 5010900.71 to 5012462.41\n", "  Z: 154.99 to 159.52\n"]}], "source": ["def load_ifc_points_from_metadata(metadata_csv_path):\n", "    \"\"\"Load IFC coordinates from metadata CSV\"\"\"\n", "    metadata_file = Path(metadata_csv_path)\n", "    \n", "    if not metadata_file.exists():\n", "        raise FileNotFoundError(f\"IFC metadata file not found: {metadata_csv_path}\")\n", "    \n", "    # Load metadata\n", "    df = pd.read_csv(metadata_file)\n", "    print(f\"Loaded IFC metadata: {len(df):,} records\")\n", "    \n", "    # Extract coordinates\n", "    coord_cols = ['X', 'Y', 'Z']\n", "    if not all(col in df.columns for col in coord_cols):\n", "        raise ValueError(f\"Missing coordinate columns. Found: {list(df.columns)}\")\n", "    \n", "    # Get valid coordinates\n", "    valid_coords = df[coord_cols].dropna()\n", "    ifc_points = valid_coords.values\n", "    \n", "    print(f\"Valid IFC coordinates: {len(ifc_points):,} points\")\n", "    print(f\"Coordinate ranges:\")\n", "    for i, col in enumerate(coord_cols):\n", "        print(f\"  {col}: {ifc_points[:, i].min():.2f} to {ifc_points[:, i].max():.2f}\")\n", "    \n", "    return ifc_points\n", "\n", "print(\"Loading IFC metadata coordinates...\")\n", "ifc_points = load_ifc_points_from_metadata(ifc_metadata_file)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Ground Control Point Definition"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 12 ground control points\n", "   Point_ID     Easting     Northing Location\n", "0         1  435267.277  5011787.189       NE\n", "1         2  435280.920  5011792.074       NE\n", "2         3  435267.220  5011921.754       NE\n", "3         4  435280.438  5011925.654       NE\n", "4         5  436224.793  5012459.896       NW\n", "5         6  436226.921  5012459.942       NW\n", "6      4_sw  436719.919  5011825.557       SW\n", "7     4_sw2  436718.628  5011831.735       SW\n", "8      5_SM  436302.667  5011417.757       SM\n", "9     5_SM2  436305.828  5011410.901       SM\n", "10     6_SE  436112.667  5010904.990       SE\n", "11    6_SE2  436118.344  5010917.949       SE\n"]}], "source": ["def load_ground_control_points():\n", "    \"\"\"Load the ground control points from project data\"\"\"\n", "    gcp_data = {\n", "        'Point_ID': ['1', '2', '3', '4', '5', '6'],\n", "        'Easting': [435267.277, 435280.92, 435267.22, 435280.438, 436224.793, 436226.921],\n", "        'Northing': [5011787.189, 5011792.074, 5011921.754, 5011925.654, 5012459.896, 5012459.942],\n", "        'Location': ['NE', 'NE', 'NE', 'NE', 'NW', 'NW']\n", "    }\n", "    \n", "    # Additional points from project data\n", "    additional_points = {\n", "        'Point_ID': ['4_sw', '4_sw2', '5_SM', '5_SM2', '6_SE', '6_SE2'],\n", "        'Easting': [436719.919, 436718.628, 436302.667, 436305.828, 436112.667, 436118.344],\n", "        'Northing': [5011825.557, 5011831.735, 5011417.757, 5011410.901, 5010904.99, 5010917.949],\n", "        'Location': ['SW', 'SW', 'SM', 'SM', 'SE', 'SE']\n", "    }\n", "    \n", "    # <PERSON><PERSON><PERSON> all points\n", "    all_data = {}\n", "    for key in gcp_data:\n", "        all_data[key] = gcp_data[key] + additional_points[key]\n", "    \n", "    df = pd.DataFrame(all_data)\n", "    \n", "    # Convert to 3D coordinates (assuming Z=0 for ground level)\n", "    ifc_gcp_coords = np.column_stack([\n", "        df['Easting'].values,\n", "        df['Northing'].values,\n", "        np.zeros(len(df))  # Assume ground level Z=0\n", "    ])\n", "    \n", "    return df, ifc_gcp_coords\n", "\n", "gcp_df, ifc_gcp_coords = load_ground_control_points()\n", "print(f\"Loaded {len(ifc_gcp_coords)} ground control points\")\n", "print(gcp_df)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Using manual GCP coordinates\n"]}], "source": ["def load_detected_gcp_coordinates(json_file_path):\n", "    \"\"\"Load GCP coordinates from automated detection results\"\"\"\n", "    import json\n", "    from pathlib import Path\n", "    \n", "    json_file = Path(json_file_path)\n", "    \n", "    if not json_file.exists():\n", "        print(f\"Detected GCP file not found: {json_file_path}\")\n", "        print(\"Run 03_automated_gcp_detection.ipynb first to generate coordinates\")\n", "        return None, None\n", "    \n", "    with open(json_file, 'r') as f:\n", "        gcp_data = json.load(f)\n", "    \n", "    print(f\"Loading detected GCP coordinates from: {json_file.name}\")\n", "    print(f\"Detection metadata: {gcp_data['metadata']}\")\n", "    \n", "    drone_coords = []\n", "    ifc_coords = []\n", "    \n", "    for pair in gcp_data['gcp_pairs']:\n", "        drone_coords.append([\n", "            pair['drone_coordinates']['x'],\n", "            pair['drone_coordinates']['y'], \n", "            pair['drone_coordinates']['z']\n", "        ])\n", "        ifc_coords.append([\n", "            pair['ifc_coordinates']['x'],\n", "            pair['ifc_coordinates']['y'],\n", "            pair['ifc_coordinates']['z']\n", "        ])\n", "    \n", "    drone_gcp_coords = np.array(drone_coords)\n", "    ifc_gcp_coords = np.array(ifc_coords)\n", "    \n", "    print(f\"Loaded {len(drone_gcp_coords)} detected GCP pairs\")\n", "    \n", "    return drone_gcp_coords, ifc_gcp_coords\n", "\n", "\n", "if use_detected_gcp:\n", "    detected_file = f\"../../../data/processed/automated_gcp/{ground_method}/{site_name}_detected_gcp_coordinates.json\"\n", "    detected_drone_gcp, detected_ifc_gcp = load_detected_gcp_coordinates(detected_file)\n", "    \n", "    if detected_drone_gcp is not None:\n", "        print(\"\\nUsing detected GCP coordinates for alignment\")\n", "        # Override manual coordinates with detected ones\n", "        drone_gcp_coords = detected_drone_gcp\n", "        ifc_gcp_coords = detected_ifc_gcp\n", "    else:\n", "        print(\"\\nFalling back to manual GCP coordinates\")\n", "else:\n", "    print(\"\\nUsing manual GCP coordinates\")\n", "    # Use the manual coordinates loaded above\n", "    drone_gcp_coords = None  # Will be found from drone data\n", "    # ifc_gcp_coords already loaded from manual function"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## GCP Matching and Transformation"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["def find_corresponding_drone_points(drone_points, ifc_gcp_coords, search_radii=[2.0, 5.0, 10.0]):\n", "    \"\"\"\n", "    Find corresponding points in drone data for each GCP using multi-radius (XY) then 3D search.\n", "    \"\"\"\n", "    tree_xy = cKDTree(drone_points[:, :2])\n", "    drone_gcp_coords = []\n", "    valid_pairs = []\n", "\n", "    for i, gcp in enumerate(ifc_gcp_coords):\n", "        matched = False\n", "        for radius in search_radii:\n", "            idx = tree_xy.query_ball_point(gcp[:2], radius)\n", "            if idx:\n", "                # Stage 2: pick best 3D match among candidates\n", "                candidates = drone_points[idx]\n", "                best_idx = np.argmin(np.linalg.norm(candidates - gcp, axis=1))\n", "                best_match = candidates[best_idx]\n", "\n", "                drone_gcp_coords.append(best_match)\n", "                valid_pairs.append(i)\n", "                print(f\"GCP {i}: IFC {gcp[:2]} -> Drone {best_match[:2]} (Z: {best_match[2]:.2f}) using radius {radius}m\")\n", "                matched = True\n", "                break  # Stop at first successful radius\n", "        if not matched:\n", "            print(f\"GCP {i}: No drone match found in any of the radii {search_radii}\")\n", "\n", "    return np.array(drone_gcp_coords), valid_pairs\n"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["def compute_similarity_transform(source_points, target_points):\n", "    \"\"\"Compute optimal similarity transformation using Procrustes analysis\"\"\"\n", "    # Center the points\n", "    source_centroid = np.mean(source_points, axis=0)\n", "    target_centroid = np.mean(target_points, axis=0)\n", "    \n", "    source_centered = source_points - source_centroid\n", "    target_centered = target_points - target_centroid\n", "    \n", "    # Compute scale\n", "    source_scale = np.sqrt(np.sum(source_centered**2))\n", "    target_scale = np.sqrt(np.sum(target_centered**2))\n", "    scale = target_scale / source_scale if source_scale > 0 else 1.0\n", "    \n", "    # Normalize for rotation calculation\n", "    source_norm = source_centered / source_scale if source_scale > 0 else source_centered\n", "    target_norm = target_centered / target_scale if target_scale > 0 else target_centered\n", "    \n", "    # Compute rotation using SVD\n", "    H = source_norm.T @ target_norm\n", "    U, _, Vt = np.linalg.svd(H)\n", "    R = Vt.T @ U.T\n", "    \n", "    # Ensure proper rotation (det(R) = 1)\n", "    if np.linalg.det(R) < 0:\n", "        Vt[-1, :] *= -1\n", "        R = Vt.T @ U.T\n", "    \n", "    # Compute translation\n", "    translation = target_centroid - scale * (R @ source_centroid)\n", "    \n", "    return R, translation, scale"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["def apply_similarity_transform(points, R, translation, scale):\n", "    \"\"\"Apply similarity transformation to point cloud\"\"\"\n", "    return scale * (points @ R.T) + translation"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Execute GCP Alignment"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Searching for corresponding drone points...\n", "GCP 0: IFC [ 435267.277 5011787.189] -> <PERSON><PERSON> [ 435260.692 5011779.803] (Z: 1.39) using radius 10.0m\n", "GCP 1: No drone match found in any of the radii [2.0, 5.0, 10.0]\n", "GCP 2: No drone match found in any of the radii [2.0, 5.0, 10.0]\n", "GCP 3: No drone match found in any of the radii [2.0, 5.0, 10.0]\n", "GCP 4: IFC [ 436224.793 5012459.896] -> <PERSON><PERSON> [ 436226.82  5012456.923] (Z: 3.45) using radius 5.0m\n", "GCP 5: IFC [ 436226.921 5012459.942] -> <PERSON><PERSON> [ 436226.82  5012456.923] (Z: 3.45) using radius 5.0m\n", "GCP 6: No drone match found in any of the radii [2.0, 5.0, 10.0]\n", "GCP 7: No drone match found in any of the radii [2.0, 5.0, 10.0]\n", "GCP 8: No drone match found in any of the radii [2.0, 5.0, 10.0]\n", "GCP 9: No drone match found in any of the radii [2.0, 5.0, 10.0]\n", "GCP 10: IFC [ 436112.667 5010904.99 ] -> <PERSON><PERSON> [ 436119.567 5010904.127] (Z: 1.20) using radius 10.0m\n", "GCP 11: IFC [ 436118.344 5010917.949] -> Dr<PERSON> [ 436119.909 5010918.707] (Z: 3.12) using radius 2.0m\n", "Found 5 valid GCP pairs\n", "Computing similarity transformation...\n", "Scale factor: 0.999576\n", "Translation: [-15720.52995562   3539.72674692   4326.6861596 ]\n", "Rotation matrix:\n", "[[ 9.99993615e-01  3.17541882e-03  1.63937785e-03]\n", " [-3.17659699e-03  9.99994698e-01  7.16564454e-04]\n", " [-1.63709376e-03 -7.21767522e-04  9.99998399e-01]]\n", "\n", "GCP Alignment Quality:\n", "Mean residual: 4.200m\n", "Max residual: 7.204m\n", "RMS residual: 4.524m\n"]}], "source": ["# Find corresponding points in drone data with multi-radius search\n", "print(\"Searching for corresponding drone points...\")\n", "search_radii = [2.0, 5.0, 10.0]  # Try tighter match first, fallback if needed\n", "drone_gcp_coords, valid_pairs = find_corresponding_drone_points(\n", "    drone_points, ifc_gcp_coords, search_radii\n", ")\n", "\n", "if len(valid_pairs) < 3:\n", "    print(f\"ERROR: Need at least 3 GCP pairs, found {len(valid_pairs)}\")\n", "else:\n", "    print(f\"Found {len(valid_pairs)} valid GCP pairs\")\n", "\n", "    ifc_gcp_valid = ifc_gcp_coords[valid_pairs]\n", "\n", "    print(\"Computing similarity transformation...\")\n", "    R, translation, scale = compute_similarity_transform(drone_gcp_coords, ifc_gcp_valid)\n", "\n", "    print(f\"Scale factor: {scale:.6f}\")\n", "    print(f\"Translation: {translation}\")\n", "    print(f\"Rotation matrix:\\n{R}\")\n", "\n", "    drone_aligned = apply_similarity_transform(drone_points, R, translation, scale)\n", "    drone_gcp_transformed = apply_similarity_transform(drone_gcp_coords, R, translation, scale)\n", "    residuals = np.linalg.norm(drone_gcp_transformed - ifc_gcp_valid, axis=1)\n", "\n", "    print(f\"\\nGCP Alignment Quality:\")\n", "    print(f\"Mean residual: {np.mean(residuals):.3f}m\")\n", "    print(f\"Max residual: {np.max(residuals):.3f}m\")\n", "    print(f\"RMS residual: {np.sqrt(np.mean(residuals**2)):.3f}m\")\n", "\n", "    transform_params = {\n", "        'rotation_matrix': R,\n", "        'translation': translation,\n", "        'scale': scale,\n", "        'gcp_residuals': residuals,\n", "        'rms_error': np.sqrt(np.mean(residuals**2))\n", "    }\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Quality Analysis and Comparison"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== ALIGNMENT METHOD COMPARISON ===\n", "Centroid-Only Alignment: 28.57m RMSE\n", "GCP-Based Alignment:     4.52m RMSE\n", "Improvement:             84.2% reduction in error\n", "\n", "=== CONSTRUCTION MONITORING IMPACT ===\n", "Suitability for different applications:\n", "  Survey Grade         (≤ 0.1m): Centroid No | GCP No\n", "  Construction Layout  (≤ 0.5m): Centroid No | GCP No\n", "  Quality Control      (≤ 2.0m): Centroid No | GCP No\n", "  Progress Monitoring  (≤ 5.0m): Centroid No | GCP Yes\n", "  General Assessment   (≤10.0m): Centroid No | GCP Yes\n"]}], "source": ["def compare_alignment_methods(centroid_rmse=28.57, gcp_rmse=5.101):\n", "    \"\"\"Compare the two alignment methods\"\"\"\n", "    \n", "    print(f\"=== ALIGNMENT METHOD COMPARISON ===\")\n", "    \n", "    improvement = ((centroid_rmse - gcp_rmse) / centroid_rmse) * 100\n", "    \n", "    print(f\"Centroid-Only Alignment: {centroid_rmse:.2f}m RMSE\")\n", "    print(f\"GCP-Based Alignment:     {gcp_rmse:.2f}m RMSE\")\n", "    print(f\"Improvement:             {improvement:.1f}% reduction in error\")\n", "    \n", "    # Construction monitoring implications\n", "    print(f\"\\n=== CONSTRUCTION MONITORING IMPACT ===\")\n", "    \n", "    accuracy_levels = {\n", "        'Survey Grade': 0.1,\n", "        'Construction Layout': 0.5, \n", "        'Quality Control': 2.0,\n", "        'Progress Monitoring': 5.0,\n", "        'General Assessment': 10.0\n", "    }\n", "    \n", "    print(f\"Suitability for different applications:\")\n", "    for application, required_accuracy in accuracy_levels.items():\n", "        centroid_suitable = \"Yes\" if centroid_rmse <= required_accuracy else \"No\"\n", "        gcp_suitable = \"Yes\" if gcp_rmse <= required_accuracy else \"No\"\n", "        print(f\"  {application:20} (≤{required_accuracy:4.1f}m): Centroid {centroid_suitable} | GCP {gcp_suitable}\")\n", "\n", "# Run comparison if transformation was successful\n", "if 'transform_params' in locals():\n", "    compare_alignment_methods(gcp_rmse=transform_params['rms_error'])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Save Results"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== SAVING RESULTS ===\n", "Saved GCP-aligned point cloud: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/gcp_alignment/csf/trino_enel_gcp_aligned.ply\n", "Saved transformation parameters: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/gcp_alignment/csf/trino_enel_gcp_transform_params.json\n", "Saved GCP residuals: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/gcp_alignment/csf/trino_enel_gcp_residuals.csv\n", "\n", "Saved files:\n", "  aligned_file: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/gcp_alignment/csf/trino_enel_gcp_aligned.ply\n", "  transform_file: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/gcp_alignment/csf/trino_enel_gcp_transform_params.json\n", "  residuals_file: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/gcp_alignment/csf/trino_enel_gcp_residuals.csv\n"]}], "source": ["def save_gcp_alignment_results(drone_aligned, transform_params, output_dir, site_name, ground_method):\n", "    \"\"\"Save GCP alignment results including aligned point cloud and metadata\"\"\"\n", "    from pathlib import Path\n", "    import json\n", "    \n", "    # Create output directory\n", "    output_path = Path(output_dir) / ground_method\n", "    output_path.mkdir(parents=True, exist_ok=True)\n", "    \n", "    # Save aligned point cloud\n", "    aligned_pcd = o3d.geometry.PointCloud()\n", "    aligned_pcd.points = o3d.utility.Vector3dVector(drone_aligned)\n", "    aligned_file = output_path / f\"{site_name}_gcp_aligned.ply\"\n", "    \n", "    success = o3d.io.write_point_cloud(str(aligned_file), aligned_pcd)\n", "    if success:\n", "        print(f\"Saved GCP-aligned point cloud: {aligned_file}\")\n", "    else:\n", "        print(f\"Failed to save aligned point cloud: {aligned_file}\")\n", "    \n", "    # Save transformation parameters\n", "    transform_data = {\n", "        'method': 'gcp_similarity_transform',\n", "        'rotation_matrix': transform_params['rotation_matrix'].tolist(),\n", "        'translation': transform_params['translation'].tolist(),\n", "        'scale': float(transform_params['scale']),\n", "        'gcp_residuals': transform_params['gcp_residuals'].tolist(),\n", "        'rms_error': float(transform_params['rms_error']),\n", "        'num_gcp_pairs': len(transform_params['gcp_residuals']),\n", "        'site_name': site_name,\n", "        'ground_method': ground_method\n", "    }\n", "    \n", "    transform_file = output_path / f\"{site_name}_gcp_transform_params.json\"\n", "    with open(transform_file, 'w') as f:\n", "        json.dump(transform_data, f, indent=2)\n", "    \n", "    print(f\"Saved transformation parameters: {transform_file}\")\n", "    \n", "    # Save GCP residuals as CSV for analysis\n", "    residuals_df = pd.DataFrame({\n", "        'gcp_id': range(len(transform_params['gcp_residuals'])),\n", "        'residual_m': transform_params['gcp_residuals']\n", "    })\n", "    \n", "    residuals_file = output_path / f\"{site_name}_gcp_residuals.csv\"\n", "    residuals_df.to_csv(residuals_file, index=False)\n", "    print(f\"Saved GCP residuals: {residuals_file}\")\n", "    \n", "    return {\n", "        'aligned_file': str(aligned_file),\n", "        'transform_file': str(transform_file),\n", "        'residuals_file': str(residuals_file)\n", "    }\n", "\n", "# Save results if transformation was successful\n", "if 'transform_params' in locals() and 'drone_aligned' in locals() and save_results:\n", "    print(\"\\n=== SAVING RESULTS ===\")\n", "    saved_files = save_gcp_alignment_results(\n", "        drone_aligned, transform_params, output_dir, site_name, ground_method\n", "    )\n", "    \n", "    print(f\"\\nSaved files:\")\n", "    for key, filepath in saved_files.items():\n", "        print(f\"  {key}: {filepath}\")\n", "else:\n", "    print(\"\\nSkipping save - transformation failed or save_results=False\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Visualization"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== CREATING VISUALIZATION ===\n"]}, {"data": {"image/png": "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**************************************+43oP7cWLiImKgSU1PdgaTlbPax1H76EKYUFXdbYK7uPbYDprRhaNrGKWHE2AOjow9YEC2CdbtC1yJDU96wfKluEZZoTSUjSON+Hw5DiYptFxBOsI+s7rgbkHqV6JRF9SGwGcL+tjKxJ2XRG9QPhASGMeCKexWh6z6YeCp1zYoPcCE8lrOcDC7zf1/hjR04S2mlrEWOLDV/+Go1rn0DvPcNT1vO3cBpC2JXsUmtdQwEndv5i1se3w04jsbEcbelE8pwfVj67ru99QgfDPMH5XTTPj3QQXzLm5aBXvXS2EqpWgFQiXA2Z6Kn4KelFxewAHnhiHO/efRkdXIxA5XTMYw9toTjCzDBpCwfSCQD2DOXOQM3cuIuesBE6WBDI0eG0CsVCpeg68aGt2/WHg0ACL3Bv4m0R3dzcaGhrQ2dmJUaNGYcSIEV90kwwYMGDAgIHLgkGkXyOgOTHx1CR+ojn0yZPK+pj+PWqUMicOJea522tmxYf8k3FK1XQC+xvNmJ7hUlI11S8QsZWabcP0bZuVAxHZe//9fXfOJ/RkITF8OJPIN3zaiI2vAimrgKWxGiQcV8feemsgDVqeOOsdhyAvkPspyMgKMO4uZQodRqSLpCIp8OLjYUqYiTy1uGEQuau1sONtkexm3i0KLnJKxPpja6ywOexYmVSK69S2M1FTOYANgk89b3uoNGEOugn27g0QvlxVRL/5Ij5copQXTiXSVbbs4Go+Ih243U1/BbP4tTh8WHPhO77Zgcgh1ZiRV65YhlyGv+pAWU3ONfwswYUckUyW96F1XTnBRP0jZ1qo7a6/34qMN4pwb2Ip8BIFhxq0F4O8X/n1ulzCV7i2vKZBH0KaVPSkaNMjqsIlN0I9d5JSNeY6Mw425SF2IZEvwefOfOWLHbDBjsICHgAIcHP0rnlgsdo3RH6Ql7GQ3RF0r9JvgvocJ5sSkDw4oF7Xvb5Se7T+RJ/zWruUuS/+LVynBAMGtEAqcVJ8ujpcjEi/eOkienp7sP7wekwdNRUvLtFWMjN1+e+eZgpPusX9N19BAXZ316D52AHklm4AEi3+Z5R8u3GsF4ltXqWC7ooVoe2uuAd2dTUadr2PPVv+B5asWch54Mm+pDR/fxFpq6X2DsdWi/8/V7Hzwuca5B0VKUzatlsZv4kc5OP3xIl+RXqQpY0IefwOYTWziZSxUW6/Mva5wWX4j56P8fKhdNx1coh/f0z5SnOtkhI0johFR00dYubNh8W0IjwLLT5+E7lL50HV42nM4FZYA/EdFwPhVP+FII5x9O/+xm+xj/oJ9o73xiIyaTJmDM0GNrmAbscVUWWHAlnpJB75KxZ2dSr3vx7k8ZvfY5JHuqzc3tlyDF1R5/CbyjSloKaq0Parz690IFxtp/t4HEpne5BtyoZlmAVDPq1S6gIIlkVBz4lYK+Fyx28OHkhSx24i9RefHIzr6wYjctokYHbg2aD3DwXmlrebYT3gYvZLRe1KBg2B2m7dqXq6y4FwXtuFPqfaBHzsFuaKKReAlONCMCoM8DYRsS8+93qf9ylua+CKo729HW+88Qbeeust7N27FxcvXkRvby8iIiJgsVhw++2346GHHkKu1jzagAEDBgwY+BuFQaRfI6A5Mc2rW1sVAp1+CF1dwF//Cjz/POB4yYGIRjvwZIAh4nPpWXEupEtq76//0IqdOxXObQvJy2mx4HTCUlmpFAydYgHqj6A1LgX7twEm8jYW1w58Ik8NIMVJdzc29n4Fa+ptyF9LC3SNSXyoAop6iy5ZkaPl6a1TkJH8lXvr4jAyzdvXw5U80tPSMOX0aUx1FQHlAyCL1bbwtc71XgdmxdkxpVHZBxVI3LnTijavjQn6nxAVX2ThkpmJ5u4EXKh2IzIjFpYXC5U/ExchWV4EQVXRMxKBW1zIavZwU6TlwqkavpdsEUd9nZ7ev2c6v7YXL8LbBRzf3oiER4swOlejWJncxjDJcdqMCrSStzwVu+WFNqcWWEEu3PI+ONdQnGVDzgrBfkYmFrTsabiHvl7/ORx4ZYIdeIlIpDzggw+UB1Quyiar9geafi9C6Df6FwVukirs2LjLhv0TzBhN14iCWlrZGpxUCcdWRjyWVp9J9ROmZ+fh0MOFfdf2akHdhANO+GIqAeL7Cq2sK+qeXYNeVwkibl0JPPEEPTDBwTVRicvvVTU7YEetGTEH4zAyHRg/RCODQqvoobpfPX4hkNmjPA70b7LOWrXqMq6TAQMCiOAhUsEcb8aw2GGoclexzy/1XsJ/lf0XI9mpGOkzC57p871Ll44jzdeLBPHda7VirSsNdR/sBtrGYKzwjNL4DfNE9sjvnjwExxeb4Wq3w3ZGUVoHdq4+CGTtRarBIUOwcUIvuk+5MP79LcDonL6qaP7+ku3OQp689MCp/9/e3oxL3lZcSk5H8rxFmsTg2Cmz0ftJHUYOTQsevznhSEFOLSV1qHkEt47j5NtcUtnmIq/dAhsFIXaVoiduL5pne/DbxB7clfcvweOCOs4Rid7e1ojTF07DUvgKqkrXwN1d41e1hxy/KdhJ50HjKxHpYmFPLT/5cALhWkUtBUu9fsdvQSXfeuwgKqs/wPinnBg9OVcZv3lAVir4HC6orw9tLIatDkoGheoRTvYmU5cUaAZDmJVOZjOaR4zBNym6GU7NE36daEwRPdKl72SNykJWzj8GxrXta/t6hl/pQLi6H7pH8hJciKioQOKODej0pQLNqsUhP0/+3GhlPHyW8VsqxkukvmNKIiaOmIgFD64OuqZ0vZI2B7ePihpTBo1fpa4m7PUJhPPjUv0gYezWtJ6aNAlITOybQaHxTNM7sbRGeZ+I9wx9vu7QOmw7sQ2rF672/023uK2BK4Jf/epXeO655zBhwgQsX74cTz31FEaPHo24uDi0tLTg4MGD+PjjjxmZPmvWLLz44ouYSIFQAwYMGDBg4G8cBpF+jYDmkTQP5vP8P/1Jsfgm0JrspZeAW/faEXGwFMgJLHD4XNpkpgVgMLl67JhCxNNvrYJV67uXIdnbDt+JBAw/Kqi6Zb91muySEmXOHEQhH3ElQAqJT2jeLC8IB5Lqy6FTAIn6YhN5Q5MvvA4hm95UDnRXAwfbgKPRfRXHol9pOItNIiOrq9H4Xjn+4Mr3r02IRF/gKQUysoGcPIz1mvFfx4pw6JwZ06dDk9g8cTEDDuT4C5KGJUZTVfSkxGPQUuTrBSTkA8ie1yL4PkTP0QsXAqooLeJbWLiVlbjQcsAJ64ZSoFYpVsb8iB5WbT801ILt60pRsQ2IXx0gxTd4bfhNmWKJwwV7dW4b8+GmYre80KbdQt+R/HxtNqxcqRzDSr/p3uXKsuxsOLPzYHfaELGGbi0r8z+32gULJJlI1yicy4vwsnuK/p+IDTpPDrmf+rOYGQBYF1K/7SpFognIoMOe9SjFE6Tr4VclDiQLQLbsEZ+P994D9uxR+igvjz1/QUJ0wcopx12Jo+OyEZkTTBCll5UAjbvRuhl4IfZ+zJkzEaYnBG9Y8X6V3htHt7swqdWDA40ZGP+dnEAGhVzwViNDQ+8VxN+xvMseewxoawNefRW44w7g/HnlfWnAwEBBdgjRkdEYHDsYX5vyNfzU/dPAHyOA2nO1OLDhVWBPcJYWEVUdg7bBFCnc2yq2n9iO2pEdOD2xDXfR38RnNCEBdWc6cfjIx7jwzEHU3TCGeUMHkZX8AaCMMlKup6Qg6ls/wv53XoK1Wadoc6gxQ/fkpXee+v/701NQlX4BsXdMUfyhNZBOHs9UMLHqNFBxNPCuFe1oRMJRLyNImkdw9SoRgpXuSnyjJxuFdM5EGidaEDV4OJJ7Pkb6ojuB5YGaDGL7Y3LmMxKdlM0EsuoglTERpLpEujh+E2lIKVNEUhP6K6webiCcQ8wAJPQXwBXs4s63NmBweSt8UXXA/lpl/Ka+1SPsHY5gUvwMUPH68yhOdsK64hFGYFJ/Eynro+ZQBoVa7JM8wsmax28RIpwHqYhLaCFDVjI3BFsEtjt2oeL4NpwavBIur4sRvFZSdfc3fvO+kTMUamuVApyiclqe637WQLg6ptL9QT/OjY+y/hhqGQLEDQ6eF1yt8ZsgXEci9dMTXMw3Hvz9oJ734iON6KoBYqZbgJnKNfdngKxZg9b7HsN7cy1I+fYTSKvzBs9BhbmWGBwq3l+M6HYHRl0cgqmYoG1RKIsAhHPmFjOyHQ79P5HoVIeCjvHDLT/EseZjmGGegayRWUiISWDPvWb2ioHLRnl5OT766CNMnTpV8+8zZ87EN7/5Tbz00kt47bXXGKluEOkGDBgwYODLAINIv0ZAc0hSSL7/vqKQ3LxZIXYGDQJ+/GOF1xq6z4sUn0tRhhAcis0IpWMqk9DgCeQ3vykoLmVLD5cLtR8DyaS8QgLGpgmqblnNS7Yvt9/OJur5kaqYh/zT7TonMtCUWJn5ogVoTQ3KkYvXKq1oyLMGSDw9Nlr0YpehLoybW4ALdR5ElrtgEYUrGkVP2ZrGDSwxOXBXnJ0p3xEbsKNILypCuqUUmKguBOx9VU3Db81Fa2x+0OnRfumHDhlWN4VS+Ify/9ZTNsmLdbWvmWqRSGKE6Gfh2sZnASeLHYikE+eKdE4e6LStwpSHErcN6Xa6U5X9N7qA3Y1WRmjyOnG4J1DslvYfKLQp+fmSAqlQtdORrjW1401XPurWOTDr/SLUxSt5AFbhYvAsfH+zxWdEXCDy/uT+rOI9JvQTWZp0lLiQ4/Yg4XIX4jKZbzYj4Z48LFCP6bMDVa7h2Dj1BaTca8PSZwq17wPZyzdURgA/HzHFnI5P5Bv9iPeQWOhPVb9RQd2c2SMDyjXJf3ZHwkomZOOONH2OqxF4SVllw9G1ym/28EvPKPX1Dz+w4tj/AP8xoyqogJreK0h2Obh0CYiIUOIixK9QsgH9NmBgoHC1uxhp29nTiTJnGSPW69vrMSZhDP5xxj8yRfojzjSgPPidykifB1czknJ9shNTBSKIFOxcyY54afxOSMD5yG6kN1xEZnUL3IPS0fuwRrCZnl16ntUXXf4NuaFVm1YrHKMV9WcfhXu4gXD1/8enZmPXNxcFE2LSe6gu1otej4uR1hbThJDjd+ulTvR6W3GxoxFCKLPPdvSbq1qXtJlw1+E4TCFv6OOBoOiPHA78iNpxztxXKa4WerVkZMDyUiDrhXvd048uYUdzEK6kF/tGi5yW+0QviypUUGMgNU8E4jbySDmaIhth8aUowQUav2kfojpYzBiy24NJ8Z3A4Pe3YPi4TpSklzD1N6sDMM+KyIkp/mNFtjkRm66Soq/3DYTnW6VCkgL5XXGDCbsuHUfqz59D3eR4JVAkjt+uCuyt3ouZl2Yid0xuX4W2XA8lJiZIOR10PN7nnzUQLimtLWrgxn9tQ9Uy0JiHhgV5bk9zOToO2SsWFvpJ/aBjqO1Jofs0NRO4dVlflX5JCWLL92Fk0xFsvSMHBRMLgoukhrg/TecvIfpijzK4alhDkbUSZYVMHz4FK0g1IfS3npUTfUZKdB4go0Aj2Wd1XerCLeNuwZbaLWy7dXdreOEbuGyUUNpnGIiJicEjjzxy1dtjwIABAwYMXCkYRPo1AprfvviiIjYl8vv73w+Q4FRwlNbCK1LKkNIsFPWR7A2CbDCsSp0htdaQQrJLPsiJtcDe9cAt6U5MGOIGDm4GtjQo2/WXfhxCXk2HICsamqvTvMo/P9cj8+QFhbo4JG/3PHntKS2W6kbm4lSmBaZlarFRLaj7a2xJwPnWODQ1msmBQvtcKO3WYsFwrxk/KSvCFDiR4qlUSHSRUNRKBZbS49NjXUFfEUVS9FtzbcxTw+mH/h2iUFufBThXu8mFZ0XIZtB8IU6k8SIh/b6fBaVyqkrwxid6jYfwNo1fXchIdJEUT/HaMPZthcN/7TXgG98ACm2B+4QscQIJ3xpF5kKQOvTnWdvsmHyhFOkm4qKCffFff8yH48eBCx9VIHeGWkRX9F+VlVdaakJhMRmkph/oYljeH7fcoeII9GLgmDEDxxb/FplHN+GT3wJLnxH6g1+r8nK076oMqP+t/aRDyNkJBHpwaQHFi7FxyJktBL1Cuqr/bMsaIO4tn9+Rpk97NcDOi58bJ0Wk25hIb7LAutBWDowQPGbDAD1WFKQkToWSMbi40bB5MXC5hUajo6IxYsgIprKl/7hvL5GMsVGxmHKiFnBtZzdfkA3G5FzUtNRg12A3Guosof3AVWVwQ3UFzFFJuBAJJMUORhrZtGypAkqeDvib8+/J5K7OOPzstmcZcR8VGcUCA0RUhkVKyYFwdeyg8yqcF/w8UtuJkCWC1WK14tThMiQJ9imaUN/pZwZ7cT4ZaBoDUN1nzXGQv/tvU9qyaqMTliNuIDsDQZMJWRErvtt5IQX5Eoy2+gl6+q15nXh0lpPSfPwW7Gb6QFZO6wXCeUEQ0eObj9MEMZihVfRBGPssyPfPgYLGb06Siu0i2GzBpHgs0H2mAueSnUhLTMN9b9/HrI3+ZegiWFQ1Oh2LrrF//OZDYijltRAIj7+tAHN++jQm1x5H+nBTQFHNx+8Nj8G3/ygsb6wFvvPrvgpteT6k4Z0v3rtBliSfNRAu31f8Hl24EOv+8hzO/fU1RN22CD/6/p/7Br7EAIY83wkVIebZCfX1fYt/9jd+i7Zw/BgrV8LrbUPTXAsWpi8M7ENurwQqUHpoBRBHugwKJNB+aREj3P8vXPwQbbM9+PHuQ1jhVZ+VMMBJdvJF//2+36PnUg/io+PhbFOCmPTbgAEDBgwYMGAgHBhE+jUCWvcQKUTrLyJzRBKc5tTEkx6ftRJTKUt45UqlwJ9TsD0hxZBog2EFHnoIWL8euPNO4BVhjcoEQcXAjIessL5iVYowrXUpHoYEUpGIE3qzhuJYg2jlBDo5QpBtIik+ad3H589E9FMbI8n6XE+JI6iCLLlm0N6ffhr47mwHK25KRT7fRSHuKy+CpbIUp+Ly8BNPIb5R7kAW+aCLC4E33lAsaVRlUnKFE/EdblhSpEm7eC7qooQpzsnKJUGygwmVCiwrwzXIVPqIfK9tTjLFFxb1vN08NZyIdDGdXYsk1CuuJl+bUMSz7CvLr7lASgyk+GefayoEAljXcp9ztW3EE/+mTCHS6fZjTdQjfftZwMnnzr660gyUxCF1thcgWxcEFqe2Cjs+SJiDTOcuoFE4nhzYCWVXRApOtbhmkJr+crOLtSx3JHV5dew0nIjowBifV7nunDjji3eTCe6OOOy6YMYgNWBDzw35+pMFlK4tgRpE8h9LvB/4Z7J6nQfdQhQj5LEN7kgTKq27D3S2TWt04KlBdmyPsTH7VTRp95UeyDqLXmlEpp89C1RVAW++OfBkGgMGCJtrNqPV24r0pHS/wpYIdCJcy+vLmb3I13a4/ASX3RYbsMH4pIaRd7hBJQtVvPrJqzh9/jT77fdWt1rxpteOusG7sHLUFCzwpSkRpZEjcfQ3P0VqZR1a00djbP6jAUJWLHAYorIukehkQUNEOiGIlJKtPUIp1YVgplwIkL5PquYxpIYuKmIe6adUr3TdZ1d9pyQfqUD8uQ5FRS2Cn4/wLiILEKZenlzF+rcPkS0HwsVi3uJ7UIKe7USfQDiN2ZTNw99bMlEojmWyspfe/eL14du+847yoiLwc5EV7/yac4/AAbwT+7RNDOJzUpz+Rspymw05v17HXAbz3sjzZ19QYIg86Bn0jqeV2aVx71hH5wNzVgInS5Bqnq0eN3i/U850YUrFqWBlgjxeh1BOs+yLuYBNzcKoS1Gew88cCNe6r1TLoku//x5uOeLBR72bge/3zUJr3vEBIiv24eKRCqS8ti78ArW87yZPVorTkoWNfO1l9Tq/b7SCG/n5qLktC4fq7MEZIHLWnNSvjOx+UCpwK207KXkS9rn2AaNHA66BZQDQO+XprU/DBx9iBsWwgq6DBw1mf3sk11BEX014vV7mgf7hhx/C7XYrQTgB+8hGzIABAwYMGPiSwCDSryEMHQqMG6eQixy0bqE5KHGlqQX5gFUhtzqeLsIutw0N9xQqtidVVRiWGoesIV6MdxYxknb9eiuamxUy/ZWHAqot34ZKRrgfokkvfUY+MrTgSk7GB+kP46W/2pDcBaw6rRR9HJqiemB3dASIdA1fc9rVli3KZlTbjOpXioJWUsvTcblneDj2GCXblGKSd9cpxU2JDCz1WJGabWMFU8eqyvHsw6TgVTuLT+rfektZzJ46BWzciBSZUNZQsvmhp/TSsN7wk9iC+lqP6OW+14osXf1QXMBw9SB5StKiXC5WJirqxGNrKPr9KiPaB7+ROEkgFqjSOj+RnA93IUfHoTbz9HgiAygoQDcEDwRoEKPkcz6xzYECix05tFLWI65DtUPvWvK+kBXTdjsWdG3CkOx4pFNflMUFFr1yAax+7IrCuOzhg58HJxs0FrjWeyfijZ65+KfTS5WHg9ReK1b4F+/HTwBtLg/GjnNhstqF77qs7LnJI3GY3rHF89SzG5CJfh50CwF+GdsqjmH1sGKkWc34ph75rldkTioCtyrFDt/oUvzjUsBC97Rdu6/0QJYuBLoNk5KU/9fNEjFgoB+0dbUxi4GaczV+2w+uXuYK709tWZgwQhkUbelZOHSHE5FkJRszEpe2bUbHyATEC/ucmDyR+QHTbwJXsd9W2QhXwgRkjEgBtlX4yfnfZXbg1pZefDijB/c8YEPHT59GzqduDCWFKhWv1rLeEsCtZCgAQAp7Ir/9kK09NIqaEvFOJP/dsV6kqwQsKyZZ9jF63q0DnshiJDx9fykV/SwtRXpeHtLJPkXOouJB8DlzlHHDatUfvzlpzN9F4r4I4nggZ8NQxF8eH2gfOi8CRhKS1zeRuuaqvipeHggXFekioSr6YfP3qt7x+JjPt6XxgKLN4qRKDoSL6muqVzJQ323hemoGATRIVLpPJta1oaDGAkuOamWiRUSHM1DKIgC98dvhwBM7gaPDJmBU8kVle71Ae4jxWyxsyYIjixDsJX650Lqv1DYMmn4jPjq1H9OTMpW+IMKbfqvX6uyZSsR6W3HYtQ95rGE29mzZJeunPhCFFPI9JmclcPW6KKDQEFv4+6cXSItKwxtb3sA/pc3CUqG9QfvXG7+lbf9g+S7cFSWY0psAeNwDEmzQO4UCN1TIeVDEIFSdrWJWWismrzCKjV5lfOtb38L777+Pr33ta8wbnbJQDBgwYMCAgS8rDCL9GgEVuSO+9MwZ4DvfUbKDCcRHEh9MyklaVz35pELEEsGdEONEClc2u1wY1OXB6OYyJJ7zUO4u7rzT6lek82KPzm4TEobGYcwUM6bz4o1DhiiL0DvuwHfXFqK2HkhaD0SMU2wqbrlXUKRzaJCd9E/iUmnOTMX7yDaBOD4OspwhtXyfNZZkj3HpuBlpvXFIMJuZEp1I9EnzzcCEPKaoJTJwqk1SjpNyW8a99wYW4yJI1SX5PPexMdHzHuULPboYop2K1vnoQYsopoU27YsWyeRHz7FsmUKuU8e+/LLSZlpQay1IZSUZb6NW3xCxTX+T1eyhfFn7Oy9e1FUEr5grnidP9SWYzeyfYxPtmFSrBhf0iAW5jeLCSy89XvZJFfaRACB7zjSY/vhHRUVIRu3R0SGV0qGsZy9XxBaSbBAPqP574Zw5uPc9EyLfWAlWqY0XT1MX71Sgc9/FHJxLsOF+qY3LzaQwDyO7QC/bgSOEatMPtb2kDJ3xxAz8+pYDuMmzCR/tXQxs0yFStNRuGkXgLAU29o4LCnYMwGP2Bz8AnntOIdDJ0ai/UzFgIBTIs5fQ3Nnst/0gcicuKo6p0c+0n0F+fD06fvk/yO/OUhTTZiIdFcKLij927T6JjqanmWc63c+/vO2Xilc5EXsUPP/d00jb50C68xJyrpsMRLsDY/fs2Rg6fzb+eabiqS6qa2+hhtH4wQPhOs8uqd658p0X6/R7gcvWHiLU/RDRR6TbbR9dBKqVLKqVD65kJPqSWiVSZS0sVPYX6wAShXoOsoWZGATn6mvxORcJZpMJreNSsSOxEanUXpmMFf8tk8TieKGTURNWlgz/nIj0u+4K2KTw9xYnVOXjagURR45UAh8E0YaD5gK8L/QCx/L4PRDfbR4I59eCB/TFeREfv6Oi/CRt1gM2jD2TiEmf1gKjXfokud5AKbZRFmjI5LAgJBizuxKDMzOR2But/I0X5NVRSvN7mu5fTkaLGQZ63twDgtY8hBd/UbND7vmHH8H00K8RuVQNhBPoOyq5/fHkOJzwtsFlHaIQ6WoWCj1beXqWQup2QdY2emM3Qe5nrWCDw4H7NjqRmp6NrPSFKHl9NW7Y+j5ev/EolhbVaN9beuM3J+7VeynrgAtZRzzBlkthCjYocEPkeYunBSPiRrD3HVln6WaJGLhieO+997Bx40bMDSp2Y8CAAQMGDHw5YRDp1wgoO5tnyR09Gvw3KjpKXOhf/wp8+inwv1leTO9wIWe4uphRC12SJzLZOcwxuVhq6iuFgqXLs15cPOlCx6UoDB3eg2U56qKEBF0VTnTV9SCyKZbZyhx41cEUnzHXmZFBOZ1TpgQWVWKqqDTZpf9dp1qq8rk82ZgwBbbNxtwuWJHJKjNgDyi5iNd2byPbCeWjjm0umNzKJH9pLC1CShmJzgonOVQLFyXHt69iVVTcCUVSQy6inU6017pRUeKCCbR/ZYHvzM5jfTOpS/DMlAl2MXU3XMh9x1PSaVFFzB61mZPSnFAV/av7U2zz8+Npu2LfaBGkclsuV5ZLJILYLq58FxfA1A6uIFT/RrdCf97iypqNgjCq57d6vg2vlWLjq0pRyqV5Gunx3GOcdiAX+iJjbO4lTCBSavBghZimKJBGW7TWepfVZXrqK/naijun+0ys2ql6kPsfNpVwSb4jF3tP57MgFL83+bPX+J4TpxyVfe2VZOgFVPrzcxW3ETMhZszAoBun4aP9HYhapP+scAuaKY1OpFT2LQK3v8yLfZlFyvXmhVZF7+AwUwKo2yiAM1DHIgMGtMDtUKIHRfsJHVKhe3o86PH1oBe96OzuZGrK/PoFfcahyLylGFddgcxP3f7UiCBi7/Uipi5v6xyCIVE9geLaNHZdOIcKVxmWTlqtEOF+G5Y5iH+4AKjvZZH54z1NOP1IHkwrViKrn+eEK1FTjzhhbVbGQ/K+Log297HY4PYY5oRc5LVbYBlWAUDJosrf5AKW/igwBmhZo2mpn7WC4HKWjDB+nz/mCAQiBmewfTmPlDPv+SDPa06y8/kMkdYDhRa5yzOwSP3/1FMBUpoHcvkYLI4t8ktHb/zmNhyij7VeoFNrbhHuy43POfjxqX9kRTofv6lwJ9mHqIVd+7NEYSS21w7bAwESm65nxevPw1m1B7POxSFV7FtRWKB1r/LtKPBNwRYxaMIFBPz8NdTnvA2XRZ6HU7hb/puYJUGBLALPLOAWLKpd2ryZk7H+nmx8NzJgzcLfKcvbNQqJ6/VNONvIhcklcYJlVyUKEvPgM+dgX70XmTW9+GRIh/69paNA7zdAIrc7BEh1zm2zxKCIgauPMWPGIIHWPgYMGDBgwMA1AINIv0ZABPavfgV0dwP33BP4nObcRLLX1iocK63TupvLgB7FioVZOqgTT1p3nczIQjxZwJAXNVd60eLH6USitxFj42MQk6osgPhEeNOjDnhhx5hGM56xFAFfUReqzovw7mzA4Y/3ofert8DUXI3qPx7AW88C9fdbg3zX/VAn4svNNiDPqniBiyplDb9jd4kdSbtLQZTmglKr39c6aHHECWs9qwlJsRpU+IuntGstgNU0V/f7NUoQosSOLFK45+XhTUsh6nY5sNJkDywQZcsXMXWXnx9vm9Qnmgsb/jda2JNROP1+++1gJTWdm+grqrc4ls+P/+ZpxvwzMRWcSEgyoecFzLTaGq61Cy36hUAG9w4POm2+eBTUbjYmTg/tLa5p62uzMRJ9Tb0NcWVWLKV7h4MW062tAdJeUErxorxZ+TMU62Da4bFjAa9xIjp0iKYrpj7X69NQxAc/6LRp2p+rhMu8CS6U0rNZ1PfZa24xaRfclSG3Q8/qRes8+GdqJgRdgj+8ALQnTsTbaXPxvVH7dMn3ugoz6quBDmsuluVZlJoIRcB9ThcsHg+6y8swqdWD5ledQKx6cxGJTkEoyiiQrRYGcIocvLYECUupfIQBA/3hsVmPMVsUUkfKatdbr7sVb1e9jd6IXsUupfa4cp/Onw9MmMDuVwpqWTSyT/z3steLhJYOJGTNUMZCrk4mMrK7BiUpbszZWKyQ3k4nLJWVKKgxofXNx7BhbhqSVn0XR579FjL31mBnyzFk5elYIPDxe5oZyMiDjSxYdgljtka2DtnNkN977x1LUfjgi0DdGqChHWhpUbKoxILJsoWLjnUTC95TFhz95tALAq9Zg2EHK5DV2w4LBSKspHLNYwp5KuAaRPASGS1mH8lKceG85D4JGns56cjnG3y8OXxYGb/pGAcOBBe8pP2HGFtCzU+Ympf2zQdBTsZSv23bFpjPyW0dSH0THggnEcGRcuyPbMR0X4ZSgwfS+E0TVTrH8nLYbivo1xKleH8xNhzbwDI3/MSn3Y7B729BQ1IHNk4Yj2+K/cuFBQRxXqK2jbz2s+7PR9pf9gbP+8QAhDRI9+tvHy7CKdwtg7eF9zE/LzEQro6XU0ZNQen9Lwb5i1stFpbZ5dxYjFNCsV5NhJpD6AWy+PfEczOb0RpxETvaK/Due99G76getF0/AnOsX9El33lQbXl7LrIsFlRNM+PdHUV+u6f9J8qw7/9kImXFKiwtfCa4HgC/p8OAXgCE/NNfKn+JBf2emPME0gYJtSEMfGb8+7//OwoLC/HSSy9hHPmQGjBgwIABA19iGET6NQKxuKgImqe++mrAsYPWfV1pK9F6ENhhWYkWcz7Ki4HbKoqwsKsUBUTCM2JZnRDTAoMWeWPHIurmm5CSkMDU19tKXIjPUjbllitLyVtdTCWOioLXA1w64USEowIYH4+32vLwrseGE+S7rkWkqxPxrDwgi8zbyXZGVcz7QVEBig7QCVGhsdlmeKricKrbzObkVlm9IhLWsgKLq1GJEY2LQ53XjD+qc/wZvPAXT2nXW2C4XDDFe5iS37SS0ouV9ipHCCZ4neUu+Ko9iLFmIIVSUkUCgNuV6CnF+QXV+htPPydymRaplNotLnTCMeCWz08rxZeOJ/atTEKKKmK+r1DFS0MszmmTunUOzNpmB1YLBSz54pFSL6iomGKB2wf9ZqVbrYj6kRVxqhg5CGR3QObX9Fsim3lR3q2WGcraTa9gm8b5fRbB/oDS3GUyJRRk31PZhki4N3UL7updVplAl22QtM5DIsfKkcuE9OR0QI9+21k7cL2U/q1GSuYPTcU5RCMyRbnf3+WZLWpNhK5JZhzd7sJNaYLijRR9FISijIKB+gFrgEh0qi3x5z8bRLqB8EBK8KWTlgbZoYhkj79YKOGHeehx1ePkp9vxxqoxaN74GApetyDngScD73j+7qb3MRHK9KyoQcGGhAhs3PAcom4A8q35iB+9GumkzuSkN39GjxxBbN1RXFdfhYbI4aiKOoeaCb04ZOnAP+qdCB+/kaeo1kULFnofSWM34frKRgw+2Iru0WrQkr9PuaWYWN9Dfl9QOhoRwTTm1dWh8cP38Id0F1ZtrMfgho7guix6SluXC0m90Ui6fkFQsJn8pBvqLEEEb2NHI2K8rbiUnI7keYu0leI6fcIgHlf8nI83ZEdD14yyhrQsxkJFYLWCl2IdE9GiTSac+fgtBzq1ItB647cQCH9zhwulNZXIy8hBobgNH7+JAKV3bghSU7RS0YTNhu4zFTiU7IR1xSPADcI++Dud25YJ8yRfQzXz6t+aaUHBcGXeFzT/ElXOYvdeCesWtd1Bv8MZv/kPpZ2KWXDifuSsStlGEGDBjZE9rWiiYr3yoTWsa/q0S57fyeci/rbbmeVUfXUD/jysA+nD0vHxopF4snNCsH2LcM/RtaSgWvkdS5FV+CIj0SkLYNbhOKRTIHxnOSZ1tKK55VXgdKwSHOKCh3AFGyFAGT8OlwOfNHyC1CGpeCrnqcvajwFtWK1WVnA0PT0dQ4YMwWDK4BTQQsFTAwYMGDBg4EsCg0i/hqC3vulL3uWjqChfWVeUKFxxdLsZ149QfMWDJuGkeiNyVlAcb33MjlcP2DC2OHiOT/YvbPJPi0FSMVut6LhvEU66zRif1Q6MT8D1ZS58fFT1XdcCP65oecLJATpBwsGD/gJphPRYF04N8sBT51Ky2sXJPC20aX9ySjRfZL7zjrINLbrMZpwqc6FUFZbN4IW/uLe7nlrLbEbCPXkB1VoI20h/wdQUGwoKJSW8WhSNH+pQsQM22GHJldTl4gWWF1EUEKDUg1BEZbgIleLL/yYqzEg9SP0lkx9yYCPMBQ99lUh08vNnwRRxW5mc14B4GE1bX4cD+S478jlJL0L+gkA2D6uowRiLGTMWKkL08n1m3HwxDkk8zZ/7x/dzfgOGqNxiRitWblDU96S1FJL0N2Kk6V4h2wP5PtYrtCrcm7oF+8xmVuSXWewg2D4nKBijZTOgPt98V8uJ06AP1EyI6RkuLM5R+ECqAZF4sw2YIKV/q0iYYkGCoCbkzeQ1EeYB7KePPz7dR6ReHaDZOVef33yz8oqhr9K7jT776lcHfIUN/B2CVJBE4CTEJLDioASZyAoiuVauxMmWOvzvtAimYv9+6XGMrvkE8KX0DV4S0UTj99ixwE03sZt0Y9VarBlVjziyibkhP0AQ2p8N+Ff39ACTJ8ObHI9BkZ3ION6G/+NJx8dDevGVzK/on0yo8ZtA7wBh7Ca4EoCRUUATz/iXxzQCf87l+hc8kEtFNAcNYorl0ho3UtOn4Y4xOcF1WQY4zmkRp5vmpsDbkYTYO6ag4EF9pXjQ9dKzwZDnKRTEpOtE4zcPGg8kEB6u37aWQpyyqUQRRIhxNZzxm86bbH1YcIaCKfJ2RPbyYpV6hxGsVAqmF8AyzNKHVM8ZnYMc25PBJDqBrgedA/2W7qlh29owJtOCuAQzKo98hJmtbiSK8y9ZZX2lYbUqymu6P85Iz7re+M3HK5qT0nNBAQ55XiZmDkIjwG+zYfp6J3xRSbDQu0KEWkOBWewsUtukVUNGK7jFx2/ZfqeqCsOSUllff3XycDS6G5FHFosJkn2LcM/Zjjjhq4FSRFlQ/5tSzMB4F7q6J+Fo5XbcFJEWaA8XPHyGuS5/XmdbZjP/dLLRMnDlsXLlStTX1+PnP/85UlJSjGKjBgwYMGDgSw2DSL9GQGJSKn5HRUdJoLVaqTfmRxAxW2BjXtEEWmsQp3VLhQumLk+A4OKkGqWOk3Rc8DLeklOIygZgrHhwWtBy1RtN9Km6aUoKLC8WYrTPBzeRZ8XFuGtIKe76F1oQ6yxQ+ORftDyRLR+I7KKFM18EUiEzJ5i1DFPFE73IF51cQS2nRPN98WJrapo8L0bK5uIzchRiWvZI522SCzsShHRarahGn4KpOpN/+mrSBjt8oONlK+fMCVp536I1DQUZRoxQrhdfGF7uYlDru2JaOl9I0flyhdnkyQFfVt5XMpEg287Q4pB5pPQ9VB+bHj3SNwx+xAr1u7BhTZUVPc/ZsSSiVPFW1VLbaRVidTqRFO1hNQJ8OcBvfwtEVbhw3udBkl7Ww5WCcP89X2HFli3AuQ8csC4SFGx6xVHFDqHnkwh1ft58v6HU4nr9Iiz8c6gugQk4ZbYG+CL5IojXTyIr+K5mxan2SGo/niebJ5dSgPinPyVluuLYHqSgp/2L1kUqdG9/rT+EaekiJrG89prCOW7eDJw7p/ydXpdEsNP5UKCFvz4MGNACkei7nbuRNTKLEYNEBvuLdKpE+3MfPecnHaz5hTh3WxYG1dmxqseL1PhXMTS6Uzt4yYNDQiA56h/GMxKd2cSI9QG4fzVZltB3li1DUn4+hpWXw713L2ZWj8LsA3FAK0WxMLDxmz/3XCEspABN96UEk3vymKZltSFbmqnjd9w0M/ISXMgavxAgWwbx4ZPHb716DSGyeaYuKYB9skDo6o3f5Pv9wTrMatymFH/VssEQ30F8nkJBj9TUAfs+DzgQLp47L1RJ9wHBI8wDRUs4rf3qgAUhPlXV7O0ahLQcXNdqfohCnvRM9LzwHCtCGzR+99fGpiaWfbAsIQdF7S60XmrEeN8FJNI1F9XgV2P8Fvr8+ePPY0vtFpzb8QGsEYv6H7/Fe5fECgMdv9X+YSr0ROF6CuMx1VAgG6NTCWYU7ShihULJ4zxoX7xdWuO3fM+rmR7U10uWPcHWACZeRFfMgCOo94LlzoKg9gVd9zz0DYRrKffDDFxS0PLQ2UOYMHwCFo5fyIo652Xk4e3/8zY7F/rMwJXFrl27sHv3btxwww1fdFMMGDBgwICBzwyDSL9GQGsg8j+ntbI7UG8MfM752GPA0oN2eBKIiAashQHVKPGtWKMSljwNVFQxSd7iBQVWtrZabqaFpz2g6hZUb5ppzmGoiPtM2DUWcMz32GVl3tjsFMgfln74QlVUL8vH5AtGaiOdH6Wq0kKcil9RMVKoalgEirf2aZvYRvl8NNJpg4g9VUvsL3aqM/mnQxxy2hDJ/s/ZV9UrK6Z4m4hdpXR27u09EI9TGVrf1fO6FhXcMkkhEiNi4EGrgJYMlwvtbg8r5HqKbHxKFIV6whyJrJaOF/S/zO9faLPTiZ737Wg44cXphDikitevvzR8abFKduOtOWYMOy0UoOP+8VIdS/WjAXV30B+IAFePTadCiR9jj9mBHqkALl0HSlOXD8gLpO7cGVDsiYENrhbXIjf4+XOrCH5CwrPQVu5CR6MZiS8VYWeXqkyngJlWesyzz8Jz3IUjb9dilLMoKLhHwSxuj0Tbkj3Lpk0+f43UoDRz1WfXHzSSi5eGc/8PMNhE1/PjjwPvB+IFSNBJt4bIe4h1XQ0Y0AMjtNXfVGCUVLhE5HACiUif+vZ63HEuGfedURS+lCLvJ5hOjQ+QoFrBS8myI7+wkCnR/TYxOz9mCveIVauQTp/xWg/8PUAkNr3b3n8frZUO5nmcKhD9YY3f6vvDOScbb/5oAWzpWf5MGj95Jiu25Xoc4guVt5G/iyngL4zfPh6812qbFjkpEpIh/M6tpCDeSYpnAKO13x1E1L1z5B18p+YicuoUAUEQQRsqs4zmIlyMcKWhpTDm5yirl/kcQxQJ6AUedALhBG9PFw6dqUDvGYffD99WB1gm5+qS1aKav3CeRjDbZkPJkRKM7T2F7ItDg8fv/gK+wvi9MH0KvB0HMYYsgHjgQDhnakfxxkf9ivj+bF10rVE0bFGcNzjR2dOJsZ8cA5p7AvecXuYB7yeuSKc5Xn/jt5bogVshcU98tV8ax5tw+AYTKyS8R30HpaZns0KhWiS18/8+hLjag/C4J/ktYui8iURnhLyQtcZ8zne+gDnJcxQiXRZD6HjSDzDNdsCBy/Nd51kR56qmKkw1TWUk+mf2vzcQEpMnT4ZHrAllwIABAwYMfIlhEOnXCLjAS4vDpnnoyZPA6UtmXIxSFhE0P214fg3mOUuQ9MhKzTTQIPJTINat9iJFaSqouj2JKTgSOxujMibAslSHtHI40HWgGpVt5ahCfmgLZz0iUCXWmO+x0wGrOMnWUiHJyieeCk7gSur+QAsg8kuXF5DqQuDwmnLsfs6FlFU2LI3V6MdQBLQWHA5EPG9HhdOGiEcKkQ9VucMJBS3zb76woGjK8ePKZ1rFVQeCUIUgZdW1YH3iLz4qL8AFIrjP4lAPNhsqtgElbhtOlQBjyTrEBCwoCNw4tPuOp1WCXf0a/b/fZgR2tK8rxdGYbKTk5MECJ1OiE4meNlJSxen5uessVidOBEwzXIhsVNWEksruhReUtesnnyi3dKhLoJsxLxM8LhceeUS5laelkS++YF2kFWTh4OcyZw7wxBPA448rikFezE++r8QG8c940IwgZTy86aIsiiLMay/FrBFAlDnEyZaVIaKxESPc2+FrOorGRic62u1YvpKKuKqK88AtwJA7/BjwQnHgnhY9f+W+0vBfvVJp+nRrE991/rwi3iVOgzzR339fqeEovorkuq4GDMggUpsT20TGEURChxPtTzUmwFJeyTzHyRrCT0qS4ldr7NYav+XxKC0NbYmx2G7uwtmcWBQ+XqpfXMLl8nseb95frE0a6hFc6r6ogCe362jctgGN76wNFA6UFdtaqmU+ftPLlAqly0VGtaBHAKvj0Y7uWjz3Rh5+1J2GebIN3GXYmjjeeQmLdu1D3WgLKm6wYEpHI1JKK7UDyHwfvN+WLFEipHv2hH08XegFCgjyXEnMViKilQsNRNI/1P60AuEFBShTC9mSBz+B/K/JuoMFTgRLEP+9fGcBswfh9wi3GHGuL0ZDdQUyz3QhwenEzyIT4DxnQlp0XPD9oZd1oDF+55jvh3tEBmKidmuO/XR/v3HgDbRfbMfhpsPYkr8ldHcLVjSadi3CePVI7iOM0J2GNGDn6cA9F6K+ih806aDoLE0uxGCHnu2aOAbStRWLsKrbbkp24rUoN75RX47bKhtRn3wREStyAR5wk9C1YzuSmlvhoVQsUxEjy+0JLixfsRIJ44MV4uRzvqlmEyzHvZj77q6+9VLEQJmc0Xk1xu/slWjztuFk60m4LrgYmf6XI39BUmwSCkcXMjU+u469QMHE8AuYGugfv/zlL/F//+//xXPPPYfs7Ow+HunDhg37wtpmwIABAwYMDBQGkX6NgNdwEiGuIci397YKF8aSfct772H4cyVIcjoRe+k0QGI28oLRKrQkq8LERSCfsDudOFvXg66a04wUYxIVnUlvp0ch9Wk9TGvkPmS4TIRpqKGXk2I1z4rJbxfD9bsNaP3AiSlbAuputplgLUG72rnGi7G/yMOI6WnIvEmIPMhencJipKJ3BvbuBe766HWklr+nEOpcrSWo9o/+jxOT6ktx4FXgvVlmzBP9sjn0CGgNOIvt6Hm3FLdcrMCo/e1onpOA5B6BUAilxnngAbBGk1I3zOPpQktxLysOZcjFR8UFuEAEB52HVgEtDqsV8autSLcDc9khg4u3Emj3dZxgV4/HfNVNULZFgIxPt1hRaF6D1JoapK4UCBNZJfXee8HKR3mxSudPC1kipXk/cUW60F/kxtDbq4i4xSwRLbEVZXiQrQlTZEODBBBUnvlwIH+B0F5uXRSqwqokk2YuDq3AxUYgReu+6uONo6pEucReOglSlFMWRU0FsKfLhnT9rH32fW8bcGrIbIyfEovTFU4kVZWC7gJW803oGMoomAE73MdUkuf665X+kIurifesnFlzBdP06TVIbXz+eYXvIjstuoWJUKeu4fEFEv/r3dYGDGiBCDjy6H1669PMrzc2KpaR1aX3lwK+NcDREnaPE2k38a31iK/yAPNuC9zj8rggj99iEJiej7g4RFvGIT3VhNntAomnpYo1m/2exwcpDk2q1SNOWJu164ZoRcpzx+SiIUGxRjnw4n2YVF6Lo1gLrHomWMG6owjLp5lZ0dKtR0oxaMqzaP7qHbiTv3fUjBjnU4+y4pGRR8phQWACVOGqwMvbX8Y7p97BEzt9yHeNQqpMzqnjUfP2zZi5oxHepqE4d6YXF1MTkPLauuBzH4Dtx3LnEAyriUBny3nEevbgzPgMpMQlh1e35N57lfoVAykyGu74rfU+59AqPkqBCrHeh97+9ALhNHbzQrZqYOjQHU7F/1o4H7qX/QR7O3BfApgaeioPJtnt8JVuwMkRXei4YRoWUJLEp27kZH+lr+0MbyMVtaVz6W/8JuJu0iTl+aBzFTKcqM2/2PELXOq9hGPNx/rt7uXtZswqi1P8vAl647fZjPxNLuTbVgP1ZOlzNDB+6/mzawUt5OshX1OtOSffVqqu7n8uNzrh+8iBqRmAa6H+AE7ZK41r1yLJpHiVe/ZeRFJkA+oyrchKyOnTLzP3xMGc1AqU7Q+M31oZcJ/H+H1Dvj/zZ1zSOJTVl6Hb1431h9fjleWv+O9Vw9rlymPx4sXst026nr29vcy67NKlS19QywwYMGDAgIGBwyDSr0HI2c80DyXBqd++5cgRjKs/hbbYkeiNT1HUMLKCTVx0iuSnOHkXUkcjYUf3YS+GdYZQhz3wAC70jkEHbFiZq66znZKCSCySRmpZOr6khs7KA7IKrXjnfwBTF3D0GDBFOAx5wZO/OJF61heteNNuxYzqPEy5tBvHiAFoChQodJa74Kv2ILLcBUt+8ILlqS0zmDgssRqwXQAi9zmRQh0qTu6tVowrexbJZ12oj/ei3uHCeQh+2fy8QvmVom9B0rPxwM3n3sEUbxXOH8sCvrNiYIsJOsaYMfqEN6E/2wtpYenfr3if8M846F4iuS4ZR1PxUbp+fOEXjnpQ51RCdZnSLQrBTnrOQ07ANoer1tW/rbZiDtUIIA99fk9TcS5aiGulfh85Apw6FVA+yn1PRPu+fco50SKY+9tKCsof/AB46SXtGpayeC7LpXqDs6/rePBzCEpTZ3Ye7E4bprKabqp5EF1Wyfp999tm5NTEwTt3OMUYsCmlAN4kC8bAjGVaKjitjqf+WLCA/ZYzARS7KApkKYGPkLdrfj7zX75Z/d/zaxxwl9hhWhkIvLz9NvBwnRX/m1KMW7tKgeXLaSUWnC0jK1b1MmuuMGiXFBwhJXpysnLb07tC4icMGLhs64G6c3WIj47HthPbsHrhalj5+3jNGjx8qRMdDREY2tmjfElUWosZJfL4bbej4Y+vYc9wDyw3zEJOzjIkuFxKAJIsSOQxV4TgeZw63aYo0qmY5K4w7FH42I08ZKnvMm/2fMQcbkbShCymAmXq9sJClP/uUUaslt+xFFmFLzIS/YbjHnz6583A4T8HKQb2RzZiZE8rmiIbFYsJdUyrSq7H4QuH0dLVgoNRPpy+GIFULds6lwu5f16DWyvPoCOhC55LPWg7eRgp8vsw1LtYwpmZU9i1u+1gJ8ac7kTvhUZg7JDwAuH33w/cfrtyLS63yKhwvTQD10I/9Xk/8vGbasfIJCz3cR9AIFz2Nrc+qGFjl25jBPvZ0Y0466zApDNdKEi8B+Dfoxo4bU7ERDYig3z0Y0ZqB4eojfwZoawpCgbI47c4f6Ggcmamrlc7tft7s7/HCvquyl7Vr5VL1gEXso54WFFM8vPWvWeEuRMFjdzH42BiQaO+/8+xvq0MyY170dw2AnOgzKecR8o1g0iBjtWYc9JvUXEjP5exDjISRGx6cFaMjPTHnwHoR72PLEdI9NGAYU1OoFe5Fx5yvcLI6ZcPpeOrp4bCvSApePwWa5x8zuM3nRsF246cPYLrEq9Di7cFd065M+ie1bSHMvCZ8OGHH37RTTBgwIABAwauGAwi/RqEnP3MxU33OV2w0CTVZEJUVxeSSUFLhCcRgRyCyly2dOFKtobjHmx8zsVuHjYn5x7lZJOyQSJeReTkwJKbqy4DVBC5XyOoavhEWsu2gZPsRF4WFSH5jlx8uN3CLFXEdSEVVKUinYq/uFLYc/fbKxF/FLh0ZzDTtb/RjJGtcWhqNCsLcWHxUfMSMGoU8OF5K1J8x3DCOxsT42KR0eWFhZSx1A67HdMbNwPRjbgjsQzvLFytHDdX8ogdgKcjL0jaXWvG2e0liFhFNin9GGxzkAUNLSRpgRhK4TSQtHGxiB3fTs/vnqcna10/Iq61Fq2k9CcV/cyZweriMCF2LTvdSisa8qyBerYOJevBCifad1XCEZONVNXixX/+dC2pXVFRyjNBRVMpEMGL44VI0/b3hYYlgFamCEcfYaJeur0WIy38jQJFbL1Oinc4JFsb5cGgoFJzpQvd3R7UHzwXdJ+xAr393QcaaeLUo5eOm5HWG4cE4T64HPtSsnNRLF3oWMqv3/7ChuY2oLYDuJUbsYoQ7kHePMpWoUDb1VqAi5DEsQzEDdDrkj4jTsyAgYGCW7mQIr3MWcaK4jHPdL7BsWNIamtDEhXcHjFSKZYtv88JWhlJZjNOX2zCxuEXcCq3HaX3Cy8nIm9DgSuPbULxx7o1wCcCGaaXnSOM3aQitxNR1xkDy4jrcaHFG2SJQRYfpE5mymWAKdGJRKffMnLrgZhmoPrQCeS9kYeflScwtfLCOdPw8fVTcNRzFNf3+JAWPRKNQ4HDk+NgWpKLrDyVgHW5MLpzEHBpEAYlpaJ8wURM6UrQr0ESRjCbFySNqPAidlNZaKsYGXwsDOWnTgin9kOomjR6QRM+flPBcooUirYbRE5r7e+zjt90Lz1oZcGUug9qsXKUSQnsCLAkWmBxAu2OXaj1dcAUGY8E7msueI8H2Z+QbYRcnES2maPMQQqYywWrVTyz4Bn2E5aVS7jjt/D5u2RjM9ujFMgl7UJ9ObzN1ThZX44sgRyvPfgxkj0e1B7agTn8+OmANwMYQ0GkcAPhPNBEWR8JLtwd60W6pGqnuXzYhibqMVI0MlHWb30BzZ5m2L1efDXuFuWaUMYFFRUhqEGPxg/fwx/SXUpAYgCCk88Cul7tXe04df4UbrLcpGT8CIVI6R18f7YxgF9pzKei0H9ruHABGDSo7+f0mbg2pu300HUJiBH24VED3FqIjAje1ntJSVvVAqW0xmps2xuBiJ5uIKoHiOjV3pba5NPZLyEu6vK2pXdnqMyBoUPD35YCtmoRdZbaSWuvK7EtvdP4e+biRUSQyoWuH/8sxLbo7tbfL90P/F4ZyLa0ndaamINqtNHac6DbUh9QX+iBvCe5fVJ/2/J9EuiaharTQvukfQ90Wwq6h6qRMJBtqb3UFwR6JugaX4lt+3vufb7A/UTtDfcdQfcY3WuXsy0dL9Q7gp6Ny9mW+rdPMUCdZ3kg23q97D7Wfe7+Bt8RfZ7lUNdHgkGkX4OQCR7OpU41mWGhm4ek6nSDTZkSWDjIPqJcLSJbuphMON0Uhz0XzMyzWpMk1CJeZa9x1YKlo8SFHLeHqeI0rUPEhaRKsrduLsP5Bg+uW5qHedWK0kfkiwtJiWwJ7IvN9fdSQ/s2dnqKC74kDywpLlXJb8XKlVYQp/cvt5ejp2Yvxo6tRkqLB0NwCIP2tGPwJ/WA95Si3KL2UR8mJiJp5UoUSP7OlwP/+seRBUxYANAaJ9wCitQe8khXgw19fMlDeZbLf6drT+cm+m7rKR3F49M+KV2aiB7ah06hTv9xyNedfDHo5zIW4iI0166cNLBaUWHKC1i82IR+JF93um9jYnC0cwx+d2QZhn47H8/ESiSzfH+StQul4ov2Kjr46lcBshS94w7gz3/WWOfqpdvLBTSlbYN4eNnWRu17Wzbw52wzBtfEYULaRZbWTotWVgxUDmZp3V+8D9PT2TWs85rhbAJuG+GCieyiOKlxuQtgjeK0t5Y5cPPmIqRMIbJwEo7VADX/swlda5yw5Qffg3a1TpwzmwohW3kp36sKrSCJWILBININXAnPdK56RW6VMibTe4ZITSIlqMimzab4TCc7YZuTDQs9Q5z0lFW7FRXIbI7AknMmtKmEvR/c5ozIRa3xmxSi6vuZE04/q0hAjjgO8EC4nJ1DRaNb3XBvXAtfZwe8k2Ngv2MpK2ZI6lsiErkC1l98VH3X3vnzPwM/1+6rFMqoi01CQ0cjdjuPojg5Czl5KzDGbMbPzrXjFdvDiBx9mD2Y740B/i3Dgx/sfA9Z/yWk7KlB06ErV2JBfzVIwoA/yDAPwOPCu40j1Pj9+uvK+D1+PPBf/9U3EK5RuFKPLPW/H/m7WauAuww+fhPE74pqby6UuMLjN7v+i4B4+q3lMZ6djYobTNh16QLmDDIxixf2eUsL0NrKxu8d05IwaEgLHJNH4qan/ocVhw26l8WMCZr/iMF97smlg6++9VVsrt2MOybcgT/f+2f//epXbuuN3zLEsZvaJ+xDDiLxPs5Jvh6I24sJU+exQBqNx7nTcvHu4xb8w+8rgO1q9p/W/FCseK4WVCXVOxH4sw7HIV1HkX9ZEGobfe+jm9FTthnzIsexPv7047dR9sZDSMu04pupecp8C8Dp81Q3QVF+W6m47FUOgGsVeZazgQgGkX5lcOrUKYwdOzbs7evr6zGGRCyfF0ZT1WgNUL0KmvNy0PpFj4C7YQTwnzzERTfVVqBNhwzNTARe4rmYAL6+DaAaS1oYFw/8gb3pFDzyMXCyg4m11PB5AClxwFvCu/3xXUB1m/Z+E6OBv9we+P/CPcCnLdrbEjlfSuoUFXfdBWzcCF2IJB5Zjf7pT/rb0pjBSbWHHw4dzKexmpRthO9/H/if/9HflsbQ665j/4z4139Fyr//u/62Bw8CU6cq//75z4Fnn9XflgLGfIz79a+BJ5/U35ayLyh7l/DKK8B3v6u/LWU4U60qwptvAt/4hv6269YBd9+t/JsWkvfco7/ta68BX/+68m9afFIRJz2QbcHXvqb8++OPgVtv1d+WfC2pzhaBMrMpiK6HZ54BfvIT5d/kNEDWXnqgFG6yXCNQgJvmQXr4zneA//5v5d80jtPzqQfKiv/DH5R/0zNM9qZ6oD744x8D/y9tG/TsDeQdQfN1EmBy0P1J7dYCjYE0zxLHVfJE1gL97dChwP/T/cnrmMkYNw44cSLw/7fcoswXtEDrgLNnA/9Pwo7t2/UJ7AsC8XzXXYjcuLHvO+pv9B2BH/0I+Ld/w+XCINKvQcgED82nySah/K8uTBzuQebXNNImi4pYQUbykiYbDKucFiwo1dNGujEr2YW5so0BX4gTtBSuRKILn9PzcOqADd8cB6wQCXO9NFizDe444EKnF8mtZQEVudo88ltnVjFEo/FCVv0Ityy5KomYa1Z82z92oIcKY2XZ8GD6Vri7q2GKPI3IQS60tTox6HwTLqamATfdFKw0U3ceUMY6mFXHgIhFubH9pMn36V8CXS+KrpF5M73QZV9ygp5nufx3rSJW/Xm9axEpstqbL865upkqMtL9GGow1usjSQnlJ4fFbdUFGyk3458sDNiOiAtdHn1KSEDzO25EXHTh1VeBZ94WiHP5/uQp7Twi3Y/ikOYxWR4Hpr5nBxwa94Ws6uK/+ym4xU5DVZ5TXyeY4rBgNmVLBDx2iVx71GKHb5OH3dPYVB34Mr9m8nXROh7dU9HROFXmQqUHyB1ihrs5DhcPNyJZ4zsPPQSsX6/UaKC5pC6Ee5pb0zw4uBgpqRuAmFT4PDGo7UrC4Z7FyKh1Yua6UpzuNmF0fBySzGYsh+Ivv7fRho2VyvE/p/W4bhDTgIHPCiJkX6l4Bb9Y8wus3huPr7emICHJpDzXRKKr7+eOnz6NXSluNCy5B4V8jJAsXXgmzbCYBKyYvLRvIUH+HpBtyTQC4SVHFMKJE9dyBLMu1otTh8swlhSvqtc3kaB1Dc2YfuISzo+IRxzNF27IZ0pc0cZCfC/L9hl9oM45kuem4SbLadwRORs4zStQq+QutXvPHsw6nYQXIs8j3RMLnO0Itv2gwD4di7zZ24NtNS4begXGw8kE42IEOditlRElQx6jeSBcnEfQwlLLb4zfAxRkkC325LZdifEbUtYYfT5a3+c//rYCDKqzK2Q7kdA8qEDXOSUFRyvfx4SONjQc3Yfi/cWwVlpCB8Ipc4IW62FkCxCJ7unxsN9atjX9zU205oLyPiyTc5UMD7KvEfp4IZ1/ykz4EmfBfeAAs6XJwmLFjuV/H9W+LvycxeguDUxxcYiypCEu6jRaRsSi1lOHGHettqo9XGjc0z/qng14zjFywdfbgc6mM7jliAc9NTtwaugpDLWkIzkzEyNumY28DKUWRL/P+1UKWIYi1w18NuTm5uIf/uEf8OCDD7J/a6GtrQ3r1q3Dr3/9azz00EN47LHHPvd2GjBgwIABAwOFQaT/HYDmtUePAr4uG3AOeJZ5R0uTVJtQkJEsIsQFBy96RcjNRarFgm+yCTdCL8JkpbOQGs5RGWPFlhwrVrj62kvQOoQsKUhNS0Tgu2Rh4bHiQU8R0uCBWTGT9mNcjR2JpMYlyw51QXOoGMwvvaHCDORIqiyRRHS52BojsaoYC1s2AMVOZZFOUSuKyjU2whs7FseH3YSmOSuxbJ2ULqwuQuyqzQaReszvWjifAS9G9NT5VEiLE9QyuCJ91qxAmras7Nfbr/j/IkmupazSW3Dx74h+6LSQo0UemUkTcSHbBtF9QQvaUBFlvT7SCzaI29JxKJJJJLuOSMyRlQ/7gnzc7V2DSHsJhrZ58a+DiwJBmVAWOYJSMxRIiU4k+r2JpeSLom+1w8+F/9YKWvQXdOFkmIbHblV7GjZuH48Urw1LdQp8ke/6/gozpj9aBAu9Lzjxol7XsbPNyIsFEt9xob3Rg4vRCUi2BGyXmEoWVrzxhtIMEkbMm6dvcyPaEPAaB83xjUpEm46bk4Pzzjl4rWYuHp+7DxVeO5oPOBHf4UaSi9LiXex5I2vV3hzFzumLDmKGyoIzYEBEKAKJfIbbutrwxqh2ZJuysWCOqiwVvNDJzgQ3mBSCMZS9BBHaFe+hOLkC1k/XBBNJIYomy4HwlYsVoslKhBPtQ8yaKSzEH3cUoTTRg5+UlSGdfKPz8hD/49VI/+nTSKg9gGEtHazgn3jujMA+IIzRZjM6dpXg0qXj6Bi0DXhwtXbg2OPBvMETUHr/K4H3NNndDB+u7IvGm85OxJ/oxBRfDy6YRyqBcJ6yp7adFP2lUZWsYCTzumYneJlBcPW6aNrjhRq/6btEkp87px3slsduMZtQa/wOReiGGr95IUhB7c3Ud2KG2pUYv/WCDWKfCplH1tH5gedjdGAfzvXFzOokqS4Lvk9P4tzwKLCkelsYgXBqt1bKvwRSop/9qBQPnRuvtE1v/NYpaht0bjLkc+Xjt+TnvzHNi33ug1hygykwR5cFLDR+z8mGPdmJqWccsIrRXTWrIG5IGzyWaLTW1qG9rRExFOUniyMhw4EU+GRdMzphNJZOXIqC6QX65LZos6Y+z37bGILHgyHjRuOjyXHwpZpQ1w5YfZ1Ibo9Gek0TCr0WIBYo8kp2OZ8zRHKdPNINfHZUVVXhueeew6JFixAbG4ucnByMHj2a/fvcuXPs74cOHcKMGTPw/PPPYwmpPD9PnDmjFB2WIdu9hPLL36EqhDlKFoa2dhFBivNQVgwiSMne2wtfbwTO9lgwKsqJSNHaRcSv54S2axFRNCv8bUmVF25RWMqw4kpgLYg2E1RTiyuM+9v2V79SVNF6EGwxen/2M7i/8x2MGjUKkXrWLhxPPRVQWmtBtPF4/HFFFR3OtqQo4spwLXDbEcJ99wUU5/1tS2nOFETWA7dJ4QvQUNuS0I3mHYSbbw69LbeLIcyYEf625IIQ7raUxdJfe0X1dLjb0n0Ualv5uZe2pXHh7Nmzyv0ktre/d4R874nK8P62JYV5uO8IGuPD3fajj8JfqNLcItxt334bvu7uQD+Fml/9Dbwj8NxzgawJjvPn9bOVJBhE+jUA0amD5t+yLSSBnDb27bMicpoVz1q1F39BBRllAlEg9SoaLSh+B7A+Ih1HXoTJSmeRcHQ4UAg70qw2nIaVWUUEeTXydYngd825sTMVNpZFEptiQw59sGYNhj9XgvrzaXAPi0MCJwRV2wvySx/mjAPcAjkpk9U2G7NzAWXTbJDU3eQ3bjbjwuyV2B6bz9TmmunCbDdK5471moEyHX9SPcikh7zY5deC2kXnSBdbBm+zbDMS5H0jpc/K1zhUgbP+Utz538VCkLLXkEw68AWtZP0TVh+FIn+4Ev7WW+GwFTKC1lasEsPS/vklnBXnQs51HozsKIMp3hMgvAfgkasHysJjSnTap5b3rVaAQ88uRV60c+Uiz5LgLwOB2Gb7mDEDOwp24tLJ09i/GVj6jGq74Pagui0DqVTHoEDxXU/aUIRpKFVskvg9o17X9FgX+4hliZQAUxLUZ15Qsz5fYfVblNH4TfEUXSJdCGjZ4GLPbMzkbCBHCYBQu+/ZuRPftbyASEbSF+JksVLkmP5et6EKva44RNxqvuzafAYMfBEg4umxjY/hZNtJOM87+xBIVARv3aF16LVOQvzS1X0VuzYbK/a7gN4XrwtVhkVbKOG9+GnDpzhwqR7HKkuCiXQ9WxaNQHj+DVb2XWo7eVvft9EJy64AEcqtKkwpZlZ8kTyZGVm+YiXiRmQEFTM8tLGYFRft9KUCzdFBgcScVjfSfL0wRboD9iji2KE3HhBoHKRzeOQR9vI5PyIWHTVViJk3HzBNUBTpfH9kfzUnGw1L8jC2zQucHGBRbHUfzjYn3vTatf2eBzJ+UyCAFqhagXC9wtN64/cAbGn823JrPyKZVbV30D11pcZvyTIsqM8FS7YgYli+/61W1udEwP6kJQ45w6cioScGqTvo4UH/gfAwQXYuOFkEVFEg3K4tLtCrLyQGvmTrINGqh/ZJ3+HjNxEEgmXK/7yxBJ1RnSizDsFGfnzVNql6SBtS5+QwayTqj7oP1mHcT2uUABQ/dzVd3DLMgryMHKSs8KJ1eBnS6ruBQ3VBtowbjm1At68bx1uPs3/Td3TJbSGrjYJfdSlunGo0KbYxauHQG9LSsOi6mTii+rPHtZuBAy40HqlA11sbWEFZ23cVd/ZQhU4NfLmQnJyMX/3qV4xM37BhA3bs2IGTJ0/C4/Fg5MiRuO+++3DHHXfg+lCWD1cTZBsgevaG2k4Poue57CneH0Rf83C37Y1Ab89ghSDkRHp/bQqFgWwrEsRXclsiiEWS+EptGx2NXiLY6Pr1FzAl4lkkn6/UtkS4yqTrldiWrr9IEn+WbUWSlMjkcJ6JgW5L/X81tqUF5tXYliBv6/Ohlwgorfvps+w3FESC+EpuK5LJV3Lb2FjludPrJ3nbv4F3RJ9nOdxg4RdNpP/kJz/Bs5IfVWZmJo4cOaL7nT/+8Y94+umnceLECUycOBFFRUVBEez169fjpZdeQkVFBVpaWvDJJ59g+vTpQft45ZVXsHbtWuzbtw/t7e0sKp6UlOT/+7Zt23Crjj/U3r17WXoaHX+8hn/T7t27MVsuUHiVwTk1vj5ra+vLv5FVEJ+783n8fU47LJWBxQVbJ/GdyQSiYDMxeEsphncCjpeAfNG+RF5o8cUfVyxJjaZjz4wDNrqtOBXn6uPVKK65gtYeNrJ+EFSnJSUYV78bSbF1iI43AykB6xpm/WLRISe1wNU9ubk4jCk4czweoxfMxNT8XKQT+U92GeWq0oiTmIICzL9mLZKsccKB+mV2rlpZtrLam4hpeVHLFekyCRCKCNZSuX1WyCSCyKDKC3terIz8VyltmW8TDkKRP9RHKmFhj1UyE4ig9WcsCOe73KxkEZDSOiE2DwmCol7pYrrflGckJPrzEhIDDfLCXu4XtRie5r7lRTsnUXgAhQcyNGwaFo48gIQhm9DDHg7lnnF3xOFA80h0JAbKC1A2CBv+xPtBKmBH6/sssgE05wIui79PiWAndyECOVBQnbGQdifC/eJ/ZsU+pEmemtLOWl1IhVRZxVMGsppJavSgtcyFdFweqHspgE1dStybLulvwMAVBBHMRKJ7uj19fdHp+Rk+AVvyt/hJrD4Kbk6uar1T7Ha0/2Ud3LxAY5IJS85GILklGZZBCcBkBxyjlTYsn0aWJhrEnxwIFzOwVAJzaqRJqb+ivheCLCvygHdJoU5K04w8FP78xaBihot3NiJ6ZysiJowE4hMDwUCzmdVOSRDbIyua5XcmHw8mTkSTJRmbR9Rj6m02WPNLWUHEFK761cj8oqw3ZouzUwgChwt1H0T2+hW1st+zPH7TeWoV8abxWx4zQxHBV2P85v1IZPCiRcH7/Czjtx6pr1qGBfU5jd2khifb2CWWYGJYIvTFwE3CeBes1Ka3NqDR1Yg/fCsncG9/1r7RUrjL43cI+5o+1isE0aqHAhYhxu97r78XJ7Z9gCWOi+zZ5XM+er7fH3EBg5ZY2D1M3uuzGrcpmSoi6a/Ob1NuXYbCeer9tSpYEU/vi4rj2zD0uqFo7WpFzKAYpkgPSW4L8xGeHWNasZIF0XggHL/9bbAlDSEPKP/XezDS0YqmyEYsk+1yDFwziIuLw9e+9jX2Y8CAAQMGDFwL+MIV6VOnTsWWLVv8/x8VImK2a9curFy5Er/4xS+wbNkyRoaT9xoR4jyafeHCBcybNw/33HMP/vEf/1FzP52dnVi8eDH7+Zd/+Zc+f58zZw5c0iKKyHs7eThKiwBqO52DGH3/vCGvz8hZRa4NKQqZ+JosNduGAqohIi4uNFTR3LNYcXNxoLvRjnNOGwosqgrLCaZgDfIFVxcDDa+VYs9L9Uj+h0yYVs0MKNLV/ZvMNuS5lN/MqUVoi7jmCiWopgVpVF0dkonVo07gBdbEHemBL8xp4dXernSeuoh21E9BL9UV3XAYU11bAwsg2XtUS8GtR1z3R7Rq200Gdwjtg4gGOl9ZmUyFP2hRStvokddaqeBXGloLZr1zJxUbLcIpeqllCSRCPl8exBBIBH6Yu2evxIg2YEfCSvbn8elejDrmAg5HKdeRFq8NDWxfWRaLYsUTmxe8kCWFVYkLdW6lfGW/RLrcvv4scEIs7J3Fdvg2lCLSCVikzAf/Q6CerLPWi67tZYigzA49ix0VGXdOgym1gym7GVwupr6fjzKYKGvDrhLVrN3aVgp9/NSFZ4DEjOQCQFmCI0YAP/5xGKS0fL9o9RlZNWj5BVMGyGwzeuviMGz25d/LtDsaiujxCameN2AEwa8gzAlmjEscB0uihdknEKlNZCyp02taauC+oBbiUwkm/vc+FiRa7xSybDu+LVCgcc5KpLpcWEHP7aeVwGg77HPB9pfak40simDxgoEOByoevwdbu47iptG3wpS3Krh4NO3+AeVY04+SJYVGFhpvhlyYUUAK1SgilxNXC+BrUhSsL76oBgyoCGkWs/QI3qGk7uXvOQrY0zvCasV2Szv+dGobvr6xGNZmwWpDtlqRVd6Xk32kvr9IMZ0nBEG0tglJjnNFutyPodo0EMX55QbCQ2Eg47c8B+CWYeL4rQaKvj4USIqPQ+UlJ8wJyzCr0aSQtKRUF61TnE5YLRYlA2AeKboBPKr4hjefPIKkXztQfsdSZBW+2P959+drHioQ3l+R8BD1ZZzuWnTtaEZExsiQ4/f90+6He2c7TJ/+CTCrBDmN35HxuL0lBqkbnUCsQxm7KeAgF82Vax+J16OwEFWla7D59B/xduQhDI0eiSkjp+CR3Ef6eIn3gXDOCRYLFvBz5jUKKRAuj9/qsW/6tAWDmoGuenwm0H3z/M7nWVZIWG02YMCAAQMGDBj4MhPpRJynpqaGtS0VIiHy+wnVw2r16tX44IMP8Jvf/IYtwAkPUAVYZj+k7z/0z//8z/5Ftxaio6OD2tTd3Y133nkHjz76KCIkjyEizsNt/9UErwW1erXym8+NeXasYH3on1dPJRsSvUWAHrFbaEXOOqtiqULFfu1gXspkA9GZ6gSiBZ9Lmw0bXwW6XfVI2V8BpHYEiHT1OFkOIt+LWHFP5BfqLrhCrm25FzgtOoRFQtAueDFGeWHEd0iVsslInqSz99/PPl+4xo5WVMPibAAaO4MXQGL6s9bCUW9xG6rQmNrg5RRUyAvh8yzuQ1Ymd3cr7GUo6Hlxh+Pl+Vmgd+5chUcLceH6Ufc6XnJg+RA7zkyxYWqBNZAuHyKIcehRxWP746U2NKwoRd06B1aWFOHmzs1AWyPQGK0s3sWgHbeB4b61gm94jrsSMAHxqm1PSMh+4/K5DoD8sMPG+KVY2BQFp9ZDoO6nJq8oWJEtLvhldSXd43PnBtKtVGuIIOWnHvTS14XvEAlNZQXIpo0y9gdMSOsVbZOeb7E/0hn5QYr8AahINU6N4ml0akax0P5hBMGvDMrry9FwoQE5o3OC1JhEpBOJbhpqYsTss9uexdrKtZh/3XzkZeT5bVPEoLfWeB4/erW/QCMbsutcWN6ey4KH9L66b2M5UtOzYaujCxVcf2Lw+1twQ28HorKGs6K/bPwWnjvrGcC6EwAVSUzUKF7JmxFKaUoqXAqGSN6VPGCQesSpEOFi5pteOl5dnfIOdjgwvyEZ8SPovHqV81LfVVWqtcTXf/8eUrY7gidHev0YZiA85HnK+xC9zMV3HCmz5TmFTM7K7+p+ilpe1UC4xvi95tM1KKkswWzLbMRGKYUkWb9oBZrF8VstnEvWIJvmzsG4mGnYdek45vy2BAt8aUBLnVIAlcZWsp3RG2u58jqqG3k1HYicGOZ58wkzzel5qu/lBsLXF8NXqliWsEC4XnCCxu9H8pDU1ojWw2X9j99U3JWeE2F/NH5bqU/JXilRJdi1rqGsqJe86em5eG5mJzq6LyK+6zxWTF7x2QlpvfFbvReSmRVhEhDPKqJcNuh9saVuCzq7O9m9ZxDpBgwYMGDAgIG/GSL98OHDeOutt/Dxxx8zjzNa1JKR/I033sg8zu666y7EhOtJo+LYsWP+wiM33XQTW2iPpSIDGiDF2Pe///2gz+i4f/nLX3A18de//hXNzc34xje+0edvX/nKV+D1ejFp0iQ8+eST7P8/b9B8lOb+BFobi4ptWheQ0Pqvfw3Uzly7Fli1Kvy1lrxmCKynrIxYp4KE5KUcYyFP4+AilVE/suLTl8sxdfpekt71TYfVUhiLSld1PyF5R/V45LNeudmFtg/MWOgswiHYULcLmLXNDmRoFLPi/6YfzqBRMQx1YTemqgqDLx5HolUt3Km2m1uv3Od0wRJuCrjW4jm4M/0K5GFLgcIXQ1wceUEmko20f66sl/uaQ/YVF1Lpr1hqeH/tFkFtr69XiBShb4iQnbnPjmERpYwfeb7RivZ2K1autCLfJvmMCljcWIzo1g3wHa6ABzloiHEik8jwCRYgMVHpG15kjLzo6MHgBVG5by1XbmVkICEnR1VYSQfiKe0zhUwL4XvcXuhyQYEDu0UIqAgPgcxpmFbaQLpV+h1Wn2uBK1HDAXm30j0kZ3+oVvh0OYlIJ4sUPV5cl+/RKtp28CCwYAGweHFo4vAz9Dftat06XLO40uO3EQS/smi80Mj8xol0LJxXyBSW5Evs7fHi6a1P4xPXJ2jyNLFtX1n+CvvtuEG1gSFCW4fAFcndIsFixbHYjJ6fP4cltREouPsbwJ02hYQT3hndZyrwKVOkTw94pAvP3QcP3YZhW3cyknrW7LsGlHnlh6pK3tFdi6OV2zEppQvzioqYHQe100YqW5HgF8HbSgU8t28H5s9XvKTq6zHyyEk8kJmJSPG8iCxU+8B2/qJSzDgchCqKqf6dF7ycuiREUUbx/SYGgbnCmcZAuif15hRaRC9Br+D25xEIl33dbTaUVD4NT9nHGHKqHDU3jgMWAVVnq+DoegcFN1iQQ3asWvsSCueaZudiWB0QX92GzDNU+FT18Kbzpokugc6XxANy8VZ1HE7OzkbyPMmaJhyoBa51vxdOIDwd8GaA1QMokMdvqbgw2aCw8ZvsUPobv+VAuJaljB5kRb0UCKf2VLgqcOTsEUweNVkzsyJUYeSge4T/v974fYUtiXjbSZG+koogG/i7wS9/+UsWWH/88cfxn//5n190cwwYMGDAwN8JwiLSSTVGJDEVCZk7dy5mzZqFr371q8zzjFKwDx48iB/96EdssUrb0WI3nAU57ecPf/gDSwknFRmlit98881sfwlEdkloaGhACk3WBdD/0+dXE7///e8Z0WDhk3cA8fHx+Pd//3fWH1SR9u2332YKOyL19cj0rq4u9sNxnqrCqlWAw60QT9v1UtVuYXta3xJxRRBtyWkdQBwXiQbp72+9pRDp9EP/fvrpQH0ovr3/Az65XbgQM3Jy2PpIOb4yN1atilkx4Yp9C/FALjDjBwvhYztRN6yowP2urVj1H7fibNqd8JHEmH9R3eHv6hairQq42VOPmQc3BRU1ZRN8alh/fUP7mjEDf3wB+H9u4B9bX8ClE5uwcAkwNgW40b0Jvok6++Pnu2yZ0gHC330uF3q7u+EjkplX8vb5/Oefmr0QD1Dqajht5F+ihYSwL7Ez7REL4Y0EYiMW4gG1/4IvTvD5+vchfMbuj7Nn+/a1uC++6KQFDv0Wzy9wo+GKQ6vd9M+ICPRGRMBH5IDQN0TINrrMSOocgtE3mlHv9DExGvFh97+nvS/CqNQIYEQkcLYaw0/sw2iycUgaAh9dY8o24H1BDwVJp3lBVNrxvfcq+6P7n0gNIorpOxrH8W3dit6jR+EjdRi/PpxsEq9ZqGspQtrG310VFfAVBX9XfAZpm8n3z2A/yuGk4wl9Kr4/+Car6u0YUxn8XOqCH5ieU68XW1534fFf+Fi30fuEQN3Fu4zwwgvBbZXb3ge8D+n60IbZ2fAtXozem26Cb84chUCQ70+de+uLhNa7+nL28Vlxtcbvv7cg+NUav0k5aUmwoP58PTbVbAJ6gRmpM/w/y9Yuwx7nHowcMhLDY4czn2T+fXutHZtq1e/U9/b7jlk4fiHbln4/8+Ez8JhcQMQYfJ2+Iz9Dvb24cXQObrj1BziblgYfBV3FyQWAlxNrMTG9G1NaapBLzyo9s/SA029OOPf3TlGP+/O1y1AWdRb/tv9jzGk8hsn12Zg8ZgxTu/tI7a41zvI200vm2DGFRH/iCfjeeAO9u3fDR+9x6bx4H8R9ywxfpWtg47c8LxHGkks7S+GdANgzLey66b7zxTGCH1f9jNrb294OHw+E8+/yfVEAmBfhoiAwnR8F/wnCnO2qvAO12s3bfOIEfETQqi/+lVEr0fPeCdzq7MKJMSmIV++3sugjqMlNwnuZTwT6Rmyr2Yyhw1Nwy5x7WSFK7D6I0aY0YHg7fNOsSuCfB8KHDVPG6DNnlHGcinLwfYUzfjsc6C0vh4+24aQ4TZhpni+P3+FAut5ZefnYmmnBsnYzfNJzE/Tcps7A5DvuZz/K4YTjSfeu5piiM9aHbKPXi2OeM/h921EMvfNhPE3H8PlYW966660+GTNb67ZiYfpC5Jhz+rRd9x4RnhnN8Vs8N/9Fufz7Vm77Zx07P+v4fSXGbgP9o7y8HC+//DKmUaaGAQMG/n6wbfnnf8zeCKAnDYg6rV/o92phwbuf7/EMXDkinZRqpCT705/+FORHqrVYJuUZEcxPPfVUv/vNowWJChoEaYE/btw4rFu3Dt/61rfwtwCn04nNmzezNomgauMiMUDeq2fOnMELL7yguyAnokH2lSWcPXuWLejDnZy1tbWxCR4R+AQSFvPufvttxa4yPl75nH6I4ProI+CWWxRBDxdu0Vpk40ZFyEKHZ/VAedEoYttJZUR/oM9okkJKGJY+r+yfPiJHlCOD09A1vAA/oe/TTmlBS0WnmpqAkyfhi49HW0ICetPSEJmZqRxo5062zSHnNOxJL4Az+RiuW5Kp+CiSubJwPLbPMEDtYudxOg3dXZmIn52GKXenwXMgHh6+X9pA3B8/XzohUsYJx/OlpaHtwgWl3cJ3+PmnT0uDe2Lwd0I2jneauK3weTrScCCzAOlsE3dw2+RirTLUPvcNH442j0dpMxX1ofOmIk/qtfDvi64DqYrpc/r/MPv4asD3ta+h7ehRnI6bhPLfuv2X/vbboajrK1KRO+44ikb/FlvTpyEjb2Lo5t59N2oGZcJ7qAYTfDWIS01SSAfaF32RzregAA0lW9F76iIiotOQevtC5YDUj7y/iNjn39Hq6+hotI0Zo/S1ej+zxkv3EW3ufXMvMtqqERfqWupdb43P9W6n/vYlvj+ObqxFiuMAjqZEYzB/LsO9j9XnacPGaUhMdOOTT/S/emvaMczIPIDEtGlwuyf233b1+rCOo3ZNmwbfhAlKu91u/3vvbx1a7+qBgnzAPyuuxvj99xYEv6rj96A0FEwswLGWY8iMy8S0ZHpOAg/G/RPux9hBY3HLdbcoJDCAnUd24kDDAURfikZmTCbSotLgFp95ag+9j4Tx9Fgy2HfmpM5hx6T9fjRoLGKvuwVu8dnXG7/LyxFJ+xfmBHNvfwzbM7cjM+p6uC+Yle/QNuPGATT+6D7kfcHP87q4kXAfbgIGDeozNh9Tz3ta6jRMHDEx0FZqv3A834kTaIuMZASvOHaL/U1wcx5vIOO3OC/hfT5uHNr/zwOITwWykueEHr/5+008LvVpfDx8UVFoi41V2k2ZGfy7fF+jRyvXtKdHmczRPI3GLb4/jqsxnmu1m+7p9na0JSWhqeEEPtr2W3Ztbk+9HfhKFJt4Trn5FkoXxFN1mdg6LBkZE/KU+01jX2y8TU1FzYlPcKDbiVvSkjEyeTTQ1qb0PT1/x46hafdWnKuvgSU6EXE0RpD4gia4fBwOZ/xublbmSgAieQBQahc9k3S/5XqGY+wJaV4qQ7re/vvs7bfhlsfv5DmI747v86zLbZSPJ74/altrWdvmO5owslqY24UCb2N0NBpSRiE5Jgm7az+Be4r+/XLUvhEp+xw4OsOLtNvTQrdd7D/hmfl7HL+vxNhtIDQ6Ojpw33334f/9v/+Hn/3sZ190cwwYMGDAwN8ZwiLSjx49isFERPUDUqXRD6VTXw5okU/qsBryndYApWE30uJBAP3/1UzPfu2111gKeDiWLUQuULq6Hij1TCTfSdGWlpbG0uuHkbomzMkdpajTd7Qmd+QyQQJZ+k3OFQRSi9IPx+OPK8IUyhImRTmpfEm0y7bnO6CFHSl8yPeSJsvV1ajHGOxvNGN6igtzH1DUNbSOi6mswAPntsJ0Wvls3+PFGGzfhIQJJlyX3A5fUxMitm7FqKNHlYU4HYyURZs2YWlUB95vm4upN5yGiTecK7CogaEUvJLKxmQ2Yy55JA+pB2qrgdPjlROnNFhVFuv+qBqH28dg7CwzxtO2dJ5E5hD5YzIFiXtuPH0aEfX1GNXWhkhqr9oW6ifa5YCg9yXhc+r+oE3Ua3E4YSbeKzaF7grqq02b4BsyBBGpqRg1aRIiSZ3E5cCkpKMF58yZqH99K3xbqjEs5RQSoz3Cxf+cICm3fCNHIiIjA39dMwqbNkWy28/fD3Rd6PpcuACT2405izuAe/vpfJMJ3/zFXHjKKvDAmK34er6q1OMPhXr803+tR9Thapw8Ox6fHj+NhdiKMVCVlEJ/9ekbua+HDlXuD+pnur9JRSkoCQ//YivKT5gRdV0m5mvtT+/hFbNDpLb0uZ0EpVnb+3tQOWQWxk/JxBjpePT++OUvI/C7343CE3gddydswpiMuP7vA+GaVcz8tv/yjWgC2t5SRJp6XzWV/RzYshHuc0tQfPo/2fe+/W11n8UhFLTCSfb33vtbxJVoM6m9Pyuuxvj99xYE/zzGb5PJhLmT+77b7jXdi3tn3Rv8CjpWjE2nNiEuKg6eHg/G94yHaebCwPuDHlCeOUIkY3U1DnfXoNLkRsdtd7PjsP1GTVS2JXWN+gzu++XjGPyBHQmWCRjeHYWPR3ciyZeNiQkJiFTfxWxO0NGBx594Ao/j8UDD5Iw2un/DHFv850ljVrViF8PeezQGqHOBzZ/+EVHvb0TrpByYEmYoxbXpmFS09j//k1k7bD1WjPyTVUg624lRUVHKu7m/bKDLHb/5O3vhQpho7nPgDfxi3y9Y1sD9NNbWV2NrTDWyLp1mal5dyGNKfLwiOuDnro4B7up96Pq0AkkRQ5HQeuHzH7u1xu/cXJBp0vsxR/GnUx+iY3CHch9TvwvzPdOH+zGHMvKke1mrP3/R9VckOT7BpVNDYLKtCh7/Tp/G4eN7cLzhCMyuGHSbbkFNxGmYknowZdeuPvMd3fE7JQURw4djVGwsIvlckyCcG3/OsvbEwUSFfYMmJ59l/NZ+1o+/uBq9JW8hyWTBiPaePsej98cP3v8B/nTyT7jt3HCsOJOAloTxyGoY7J/DhgRvo9mMAztrUT6sBjdOWMrao4dbjjTD9/FhRA4ZD9P9pkDb6fzeDTFH/zsfv6/E2H0tgLLCwsVArVH/6Z/+CUuXLsVtt91mEOkGDBgwYOBvk0gPZxH+WbYXo8u1tbV+r1QZtMinomHcJ5VAxDV9fjVASgQi0vPz88M6p/3798OsVXhSBaXLa6XM0yRtIBM1mtzpfYeyVLldcyjQ/J5bJFJdN7InZLvjO6BU1M5ORZVOqbODBqG37gBGtcahN8mDyDHKtpQJm1++RvHtXONkn3142IzprbFoPdmLUT2daHxrDwZNT0XkqHhEih6JAG632XDYGgmQdQX3RabGiV6k/Z0QPxnRFoYfhxbavNBRbCxaDjdg+KkDQG0cIqk4If8OLfojI/27IuQsXIgIWtBWVyOSfxhO54YD0dNcKBBGH2993oFMpx0TH7Ehq7AQ76l2lqlOB3K36vjO0n5o4j5zJiJGjkQkkR5ESNDnvC/U72xdEwGvj/heM5blqMf+PBc3Yier/Un39MKF1IZIpTn7BE9aug8pZV/1X9Vsq+QPujrejuLrbSi7vhB/LFGcW/Jzg48/dkw2Dg9bjOoEGxLfs6MXpYi0mpR+pL7S8wtX+/rTmJk42DwSw70zsWz8YeV7RDSRkoyf29atsDaWImJcHuJ/WojIULaq8sMrPqSkqKWIF32mdf35ti4X4k42InpIL9beVopCjdu1zVGDR7vWoM5nxt60xfjm7V7FG57OS+xbsU+Fa7YVuf7L98wzyk9IkFm6z4f6igbggxfwYYUNueusmvfB5b73/lbxWdt8Jc718xi/r/Ug+Oc1focL2wQbiL00J5jhancxf+BI8ivmz9Fh9X1E7SX/8EGDYL1wDsNGxyCl24nIuH3KO0TjGfzQcxjTu1vR2noSUReHoLvsFLoikxDZGo1IvXex+K4g32/uwSzsN+yxkI9Z/P/Ly/1zAVtbL3zHfBh29jQiexuV+YlqQ8PG7+NbUVpbitSbpuGOrkxEzpyJyAG+Z8KC2ua6WC9OHS+DqcmMrMhclBwsYd7gPe/WIvKJN7F2yRjmx553fCtyXRH6vvFa47d47qqf+ubfPQpvuw9jMjOxLEH18P6834dyf1qtiBg7lgULFh/fiuXtZmXuwW3j6Nyo/gahv/aqdQxuz7gdb19sx96ETmye0ICFd+cH/LhpTN3fiLG+wTBFDoF7xzYMb2vE2d5eTJ2zMqzx23mxCTUnqpESMQspJ08q9zWHcG7LO8yYVRaLsVNmIfK62NDt1xu/NQqi691PMWteRdLxerSmdyLyge9oH6+mBt/bcwlj2tzI6x6K4ZGNiDzXodwrWucsPpu5uXCMiVA8zm/7Bf7UX2FcEplPtsK5/UNsPbIRU7/XgJwHntR9d4TC39v4/WU6z6sJyvYKt68vXboU9n6p3gvZ1pG1Szi4EtZsIe0ePkf4eiPQ2xvBfn/u+JJZFl0Ji8W/B3wp++kLuP+NZ+8avp8EDKTdAyo2ykED14cffsjS+uSD/epXvwp7Pz/4wQ+wfPlypmQjRdgzzzyDQYMGYSWxXsyuMB9jxoxhKdUEKiQyf/58lpZNUWgaSB0OB155RSm8RSDP11OnTrH9EapJEaMu5PmindLJ6Ycv+isrK1k6Ovm7jhgxwr+vrVu34vjx43jwwQf7tL24uJgVNqNCbYT169fj1Vdfxe9+9zt8GSDXq9Td4J13lBTi5GRELl2BpkYzLCk6hYFou6IiLDM50e30YPD1GagYnIO6ai9u9LgwTlzc0ORe63hEnGoUQQrrZCRSmoEv6FXCPHlyBg6PycGw2bRwdfUpdBRUB2lGTqAgJycwJZAVeQknasOs0xhUlCkuDu1uDyq2AfGrrexjbLEjpbMU7hIgK9+K5WYHZsXZMaVRp9CZWOSSFpF33aWornQWcbTmcdcApmUDKC55JSF0Ml/jURYwiZf867Eie9/7IFRxL6nIVY67FDk5Trz6vh1H620ogRX5WcEFX1NsNqRYrYh3AIeKicInOBW1ZagCsjRxr67Gqd6J+P24hzFkjwnLYrcGFxnlxQq8XiSY4rBgpVkpVjqQInzyMyEWk+PnzPcjFN3r3bwd3RNn971d1WMXDGvAWLyLD2PyEPWjFwFXUSCYJGwnBrOqzDa44wCT2QabwInI+w46L042jczFqUwLPMecmN9ZCrqNWWcMpBCqgSuGKzV+/z0Fwb9IhCrulzUqi3ms9wEfD6hKszp+J/zDPcihZ3pXpVJ8k55TXniafqvP67I2E7oHOTF4/PWITJ+CsUf3YeTxNmD4KP13sVbR8HDHb2EsZG3mYxYfu0nZqrbRQvsjv3SRdBfHb7U4YhZZ4AxKU77Li8xKbVnz6RqUVJawwoSafRhGm3s9LiS1NbJCkeSJTfvqebeOFXClbWwP2JB6xKkUTG0vHvj4Lc2DqJCpfbIFOXSeYZCgVwVqW6qmmVnhVrIbIhsTUtznjlFFGOL1pHOjeUao8U4ac5bm5eHg4vvwi1Ev0x/RW2cP3Ps2G8g8KkG99mdjvWg9XKYU6iRf9f4KwLtcaGmpR/XwTpw29yJz8qxAIFkk/OmabixH1vvVwOAM4MUXlXbqFXHX6Sf/XEDrnuX7UO+n+BEp6Dp/gQpI6JLvt7aNQEYt0DhuJMYuflhRhsvzaY0+ZbBa2buEFRxWCxH3N37T96m/zGfPY/DhFmB0TvCcwxi/DYTA1SBSTp8+zfgAmkOEq/y/EtZsuiDP5M8Rvl6g7dJI9KIXkZ83n/cF2oB+URaLfw/4UvbT5/zcEYxn7xq+ny7Tmm3ARPrPf/5z/Ou//ivzRiWPU4oic4j/Djf1mkhzKgZG6XPz5s1DWVkZ+zeBCHHxAsyZMwdr165lxycP14kTJzJf0+uvvz4ojUwsLHav6mlCJP1PfvIT9u+XXnopaEC9hbwVVQXb17/+9SB/VTrm5MmTNdu/evVqnDx5ElFRUWyb//3f/8XXyKfzbwxac2T6TSLy++4DVq1SVKUiKUxEV8k2K36R4sX05mY2sbc8UwiL1o4pyk9kRUsLWwxMocXfojy2YEgodyH+fDOS4i4GL27khTdXssmNDWfhQp/r/U0i2Tl5qgcrHLCC2GwbfFANVIkY1VDcUNOeew4wnXKgp84OZKnHCocoFdpVUeJCiduGdPVrVR+YkXAsDiYi+2kx57Ijy1MKZGQDOTrkBP9MLPZEoAkiLcQFpt+/P3Y5PofFuHxN1R/6mPz7qU4YWWnOjVVToWm7fiM9ErQWdU4nlkSUAmOAqJVW1oaG10qxsZcI5ELkC88C+ydddnOuov4OY3GYbWrEt5LexvAbZwYIKbpPqJ8FMoEFSkpciM8CrCLhr57XhmcdaFxrR8oqG5Y+I5wr7yvef3Qt+YJf3g//KSpC3PijuHkRqfKkBqvFv7IsFpjGAxkzGhUSnfbLyTS+nUTi/LbYig3VViwtB17U4kQ0zstZbIdvQykqU/PwH9GFWDLDgZkT7Zi40tb/c2vgquBKjd9GEPzzgxbx9fzO57Hh6Ab8R9l/4PlFz+P4ueNYW7kWq7JX4ZkFz2BDmheNZMGWbEYmfYETc6L6m95RNP5y4pF++Pj99UX+d+/oNWvgJssMHgjXGpPloB8nw8MZv7UC4fI+eXCzH0KW+od+aBLv92/WeM9QcOK5j55DfbtSpX3ARLratgiRxOX7WQplImU2K+1ptgO7+hEHhBq/t21T+sZqhfUMYN1JvhEARuOLgdqfv934KDZUbEB9Wz2eynmq/+sZKkCkMeYsP1uFyftTUT3dgoUUOHA4UPH68yhOdsK64hH/NUtXfxhGqYRyqPHbbMaIEWMQl9qF8UnjgUNVigUNV3QL887mE4cxyNuKro5GpOiMc7oBGWFMZt+h7AnKWKN3WXR00D54e5PoXh80BDBNkDpLfW4XLsTym/Jhqo1E5KxZyt+mTlW84PkcVSLQG8ebUDb+IqpjK7DwjMMfbOK/dcfv9cXwvLseZ65LxsWbZ2B/hBsLYycH+tYYvw18QaioqGDv9xlCsVpSs3/00Uf4zW9+w5TnNB+50tZsuiBrtM8RpIal/0ZFORH5eRc8/LytxD4jvoy2Ul8EvpT99Dk/dwTj2buG76fLtGYbMJFOxcho0SkSzpcLWkyHwjZaQEi4++672Y8eqF39tY0IdU6qhwKR9nooKChgP18GaMyRGej0qKYRzfPJxvKllxQnFKrrlJhIxeeAk4lNmD6oWyneJJOipJoiNp7S8ehLycnAihWBRXMT7SVrAAEAAElEQVRRESyVpRidMw3uzBzFw5EvquWFt0yo99f4UJBl4gOZ7IvHEyZpepsS9/SVoXaFsCUiliAr80T1kUhkELKyEL8630+is7XJIhfQQ+o0NfAgk7Ra4IsaUn8QgcDJf1qU0oUk8O+KKsTPA0LQhMhVO2yYWqCo76mpVIOQamoFpQqHQWQH8/PSok7t61SLHd9kG9AXbNj4KrCm3oa4EmDHDiLRgDvvBF6ZYNdPw5YDAdSvNTW4bhgwZGgFTLEdSlBCVMLpBEqsGmr8hFftmFZfiqP0uhGJdK0FOT+GnhpM/lx8Fvh1p/uago+y0l1uu0h+FQcfhuKQ9P7gQTite4rqKYxsjUPUSC9+kliEsVPNSKexSRX9Gfj8caXGbyMI/vlBi/hytjmZN7q3x4uXyl/C0ZajOOc5x8h0ItJ/4yvD7hsb8atPgExuGyKCW4ZERSnPPRG2S5cGP/ecBK+vV0hA/n6Qsn+CxjN6N1osfrXyfRudsJACnjDQQLgcSAxH+SoQjqEKLxKBS5P8MQlj8N3I2fqKYX5+ciBAbVsQiSury11XYfy+nPnQZ4HU90Su2tMVZXwfcE9weQ4XToBIY8zJKrIj63g0MDlHUd+/XoTB72/B8HGdeGroU/j+5u/jzil34hXzQ30D9XrnQH9zuWAZnIz7PUPgPtGmjIN9Gqf084nki3DMTULs3BQUaKjxycam5w/PwTOqHiV6ARl+btQ/RKRToJ6ur3g/693rfPymuTMPDJHqgO4vCqrTb3n8loISm5KdePYCBRgamLK/cF4hqs5W4emtTwfIf63xO7IR8T0t+KT7Am6MHIMnHnzVIM4NfGZcuHAB27dvZ3OEizSuCHjsscfC2ofNZmNBdBE0d6CxvLCwsA+JfiWt2TTxeRNqTPhAiljl53PFl5AU+zLaSn0R+NL10xfw3LHDGs/etXk/CRhIm6MuZ+dzB1xp0cAXCT3OjUgwWj/S2o1IdKpBREIZmucvW6ZsM4nqztUJX5IX0YSJExVFuuxvwifnNPm+/XaSQwLvvacsXChN1mpV1LguO26KcmJKZfACkdYUh5w22MgieiDpo7Tw4ItPStW127HBa8Nvyqz47mwHlsZqENz9dZYG+CZLzTak0hqGycmrlHOmdHqa6Il2HHQcOY2dPi60Bq9P5JRgWoCJiqlwwBe1s2cr/68qRTUX+p8RdJ0opkKg2FKftRa/Dw4fRvy+DUiIc8JusQaJ8BjnEauq8OgPwn3GcgT4OhiBhabdbg3NKYiLUzWAQ0p0ItGpO0icQskWRKa/sinEdZfJC95/pGKhB0ZMCefXTCATSInOAyXiQt+uCtVmT7QxnmvSfOG7Wickkw39kU+0L7JmonuSsGCB0m5qM1UcFusHcKW7uA8BdF1Fof6BVx2487QdB161KeS/ancj3qNUlNiX5MHkqDIksYW/es+LQTNjYf654kqN30YQ/PMDV1mLeCT3EbR3tyOiNwKWRAvOdCgqflKkE4ggI0zqSUOr+yB2tFcg9YwjoPzldilEktNYRc8/RcTE55FvS1FOIv4omE7E7uzZ+GBSFF5q+h+s+Pc1mL7fxRS+RE7yQCSRjESSpqZnoyAxTIsXDk4e0thFL0abDWsWm1FS+TS+e3Q2lp6O1R+/1TaTSvqvt2Vi5qWZit2IpIAWgxPW16UxWbbQEt/9OrVNgiCP36LqP1zojd9XIQge0uJGmu/5SjfAmwFmL1MwvQCWYRZm7cLAA+HS+z1IeU63qfq3oAAR3d/99aPZjO7bb8O5ZCdauw7gQvcFrD+8Hq9UTQgdXJDHb96HNOkgJQTNTdX7LOj65uUhbpoZrQlKDQL/961W//09qyxOsfHBGKTwgIxO0CUsWzd57OVzWWorDcDULlK9qAVD/UEwcfyW5glTzziwdL/6kXoeFHzb17APbd425ZprzAmn+1LgiRqBUWejMLbNrUzy+rv3DRgIgU8++QRLlixBZ2cnI9Qpi6ypqQlDhgxhRWzDJdIpC00MwhOGDh3KaqLInxswYMCAAQNXCwMm0r/3ve/hv//7v/Gf//mfV6dFBq449Dg3WjfTnJwLXqgmGXEsT6q1hBgf5iiAs9iiqIgdknKICDpKsdczCOeTc1pAUOqpUGyOK1ovXbKiqcmKO2McyB9jx1ivGenqYoQRpZVWNORZUTiQ+TpfdNJvVTXf2enEh61WLK+yA+Mlv2mNRQ9b87zgY97dYjaNlkuJIncWvC5Fr2zq1O3blcUOQVzEkfc6fS4vvrRSggUPevKrftdlDb2OEQtdyYGPK7wYp1OmxARy+KHbYfVqqV38Prh4EbFxwLhxwFhbXxGepoUOuw8C6/OOGjvzP09wOnEf7JhqMmO608XU5rqdISyk8wut/ltVVKSHTFPm/cWvFV1T9Zza3Z14f50Dxx/PxzNEKAtF9tZUWf1icC2BXuBRokCKFXj0UeDlDexEg4IHvFnhqO3kc6bFN6WX8GeCzmP48OD90XE1AjWhEjsemWjHMHcpzk9kO9LsNkuBDcwLSl7w8yATb4OBzw3G+H1tgMgvTnpyopLU6WXOMkaM0t/IP73+R4+h7sxB1B06iUPkMy3bpZBif8yY4ECr/N6jTDPC++8Dhw6xf353yWnUnqtH4/Ao2MYBg0Z7cEt0HEzTzCzhhBN2Uy/Hy5uTh+Tvro5RLyW/w4i/5Z9UAc3j9cdv9fy2JtejwlWBjsEdfiJdVECTKjfgua1+l7+fBAKfDWz83UXgWXha9hwc8vjNgxbq+MHUzCo5KwdI+h2/r3AQnEAk+m7nbkaq8sK1/nZJc4VhSakYk6l4tPex0eF2NDzTSe0L3u/O8050/LUGOZ+62fhttViU+1GvD+R+zMtDzq/XIYcu1bsPMRKdFOkw9yN+kMdvLkw4fx6NY5Lwums9Or/3T3hGOJZzTjbeXGKBLT0LhaP7zm35/W1KMSN1vEvJelPHXMoWedOrUdNgIOM3/5wHUrq7lTbT50T800SdK6bETDWN42gF4m5pGoLbd0bAO3+I5nUmWO4sCK5HoFWrxRi/DQxw/kGWcJRZlpiYyLLYqIbJ/fffz+zfDBgwYMDAVcC25Z/v8XojFA97st/5vJX7C9792ybSyRuVPE4nTJiArKysPoW8yG/UwJcHRJBx29Pnn1fmymRbS97VynrSiopGhVimxG+rKOTjizoi4LQUKnxyTsQZKWjIw+Phh9l2a+9T+MiRI6kQHdCRYMVP3Fb8pKwI6eTfrRKlqdk2TLXpTNb1FiH8pAhqRfeh8cCQi8CpiTZgUf/+naqdtOLdPVdH3MTV0eK+ZNUwLYS54k/sMyLRxfRctsMQiixhIUNFH0s91tDrGL6o1Tq/K7EYF/qeiGBqGq1PaT1NH4vKcf99cOutzHcqR7VaqVrjgLvEjlErFyL59rTg1HBBxXWfs4jdB0Qu7zpuRlpvHBIaG2FxV8JC+3V7gOIQKmd5kai2/ZWHbHjllTAWglxtTfZFRJ5Qv9E1vXgR3hQTzrcpCm3wTAe65mYzep4rgocXOhXW4kRQUwYIcdw8aCUj7Ax+TuzQBZAVk2o7WHvVzAx23c+d092dSJ6LVk9ynGxRvhkYHAdQEVVZsi4+l2IEQV3wNyZkoOxiDqorbFjouLpr8YHUdv17gDF+X3vgRFneG3mMEK0/X8/I0YSYBHSNOImZGUDjjeOQLyp/6UGnqCf5KxNpzDNqtN57588DmZnK801ZOCtXYtVYxZN91KQs7J3jZcfaesGNb9SXI6vIxQhS67zC0AVT9R5OTurzYDMVGY2z4EjzEZzKnQhELNIfv9VxI6u+HB3VezEzfaa+RY5ss0aKe17wk7+3aCIkjt8cQ4YoWXhagXD/AaWghTrWu4/HoXS2mo2mRyLrjd9XQZHOsxfoGvqtVrhyXMyGs1iQ1BuNZQk5NLqj+HePwlYHjP5qfrCNjmrvw9vO+5uI9F2XjiPN18vGbxZMDSc7SWP8JhX6K7ZNweIDPfD5Dp9rUS2f1lZg1Cic7WlDR3sz4n/1IhC/1N/mtbEVeLliA2uzfI2e3fasvx4BFZhFnvoHNRuRLFFKaypDX1+9gV4uEMoDKRTspsBSiP6pi/XiPzY+yj4aOWQkC6rpFdL97vkp8DXUIfL8FP05oUz8q++M/dfFYl9LHVLSvGxN8EUVWjbw5QMV9X755ZdZZhzZr5CXeXp6Op5//nmWJXYnU7VcHrSy4AwYMGDAgIG/KSKdUq8+/PBD3HrrrSyNaqAFRg38bYLmyyQa/+QTZc1Ea0QuCAuysRMn/rLPOd+R6OtIk3OyfqGFOP2/OlEnWxnRY5mTqmOpwGZsHtsn+asX5AVbvQStufTYRnFDleC7zmxDIVt3h1cgSbS1JO5g0SLgz39WPk91OmBz2oHnK5Rjieo0WoBrkQKzBR9WWhxpqd+0LorGeZnMNuTpxQCowXv3AjNn6vuUXonFuND33JqGW7zQ7UBe6HT9/JAJCipAXWJH0u5SeM7XA+2ZyoL9wIHAuavH4fcBBVQ6alwwEXGeoir+ZbWU+F0OeZF4uT6z3NtUuHbO0yOxrXEmnhxVrKjJyWeYbIuKioILnQqgR4MI6iNHlN3te8WBtvV2TLg5F3c+rCjA7isvCh1E0oNd8noXFPL+G4YU6S+8EOg7imbR85mbG+SKRKdLbaTffSD3KWVxqCp6Cnz4r73V6n+2M9K8LPBRjlx8ryGfLFvRS0GXq7g+luMMf+8wxu9rF0SaEYle11qHmnM1mJ4yHTm2O9FgA1NlEyE15NMqZB1Q35f9BXLl9x6NKWrGEJVFIC92jqrSNXDbSzClqxE43pcg1SqYqqvgJVKfbKgES5QnH3gSOeacgA1IP6Bt0wal4TvbvoNNtZtwx4Q78Od7/xxMxsk2a1rFQOXxm5PENM7SgMeDq1r9J1t6qO9bUu3niXYhmiegXeBcy0LrsiDMkfKtSnYDt3gxJ5iBTWrfiJM/QSRA1zNp8wb4qOZvR4QyfvDxWyo0y4MqRIqSIt0UqRZGoXPUqpFzpcdvPt/hcy26diTsiIrCqKhE3NoYh+ta4pX98vnbr+/Bw1taEYFAFiUHkei152r99Qg4sf6byjQs8vRgui8DeRnqvRpu2/QKfXNbG37fUYCFChvRwCZ4vlP/nDpchg1QCjQPjhzMCunWnatjmSkyCW2ZnAt8UgPQb9XznQI8Y2O9/oxQbstzaGMxC5hYahrZNT7Zcwnft7XhJl/ZVSXS6djk4+6+oHjDG0T6lx8UuOfes2TlQj7pU6ZMYer00xQ0NWDAgAEDBq5lIr24uBhvv/02U7UZuLZAa0Zan5EAjQRX8+crgrBbb1XWHzSnf+89M+ZdjEMS/Y9VUrfxxYDs68hTw2lhpRKlZINBBDq3cv56RTGyqjcAGSoRqVFgrM/6SUjZdT5a5C9i6feBJagLI9LbMc0dO2BgwSbbV3DQ/nt7FfKc1nCbNwc+9++fLzLF4lEy28/V8UJ6sp/glMnl/tJ8VYWv/1y0vkOKblrQ0kJRXIiL27lcaHd7WBFM8u++LIJRw0tetIDfbyIVobpA5GpHifk3rbSBlkiTh9Uri9sLF/oS/MJxWDtXUwq1VAiTIN+Dodranw++RHw4u0aia3AmIqYuQ3qhcJP4fLjR7ca6V0yIfHwNsEHYh9eL1F4XvjmfbGCKgDVmvP0bF3571Iae6VasmuTAwl47Zpht2PCCHTObS7H34zzgz8q9wYMHRE6Hsk33K8EFH9mgBTiR/mqfMqIbM7D02G9h2rZJufekYmWiKxJdNrkmmm6fCs/nVPXa13nN+GMRMPkdO8ZVlaKtKg6+QR6Yra6guoZytxvq8asHY/y+tiD7W9O/j7UcQ0xUDPNOJx9rIqCKBE/nrCMCaSz7K7OdqoMijd9EkNK9QnYSNBmQXkZcMXrfx051vxlAnkCQEqxWzYKpfPzuo+CVyUR693N7CnpJvB5og56/N9m67K3ei001m+C55MHmWnUAF180/N3ICVYeWBShN36TQpigVThSD/RCzc8PnosI59InEK41fqv2eI0djfjDjqLLV+pqkNFk60IFbOm3mEXmD/QLgX3bGeDQHU5EkrVXB7THb+kYrJ0Prg5+yYsKbD1yfCDjtzxvtNvReKQCXQ3ViOzKgOUZYW5pNiOlvR3ZU2ciUnoGVjWmwOdMQmRjin+/H7zyQ7yUeAzmqeNx/SkvHnGmsWdlyJ9eRJL5HF5K9GJR3ndgMZtRuNMFUH3f0f1cBzlIIGch8qLAeXlwPGCDvdaOpbu9MG3aFgg+qPfx2CmzsXRihl+RTuQ+BUrp+exzj0jHJashypL4t3c3I/2TBn/kWQyYtEcOxSVvK1LjM3CTJcufyXC11OO0LyLRTUNN4QUlDPzN48Ybb0R5eTkrNj5//nz8+Mc/Zh7pr7/+uuFtbsCAAQMGrn0inYqDUFq4gWsDWkQy/3+aYx89GuB6GffrcOE8PEgSF5yySooWpiRnJ9URTdb37FFU2+STIim+DhU7kLTBjpahjVCXLNpFE1ULEcJys0qG87Tjtjb4GqKR0OVkHtrMbkJWlnFIiztRgSsLvIiTHjqUCZdwxx3CH/h+OQlBlVn5l0UFsIZ3a8jFYH9pvvI+tb5DiiXqZ1qI621nNsPdEYe6Zi/Sny5SyOmBMpc6nuL8dJhvOSnH6T7hBtvCtWTBjSwgawHgM1tRdbwdLes/woSmasRkCGo7+Tg6Kn2uXnSWu/Cm7DKksQ/mX/60jr2/pFTscsWhvdGD1jIX0vX6QyW0mX99EfDw5jIkEemxfTvaK46y/k6p9SDnEvD7g1Zse0I9hgvYcacNe9cDiXcKi2iVkOhXfKfhIxukRBdIcrtLsSuyLJiGrMVCsTLBJiFfjf3Qe+FptX9CHjfEtd/1JxeK6oF7M2y45yYg6lQt4uu2o+GEFxaVexJ38cMfAjt3Ah98AGzZgisCuUjq3zuM8fvaAve3JhCRLNp0EAnFSTTyTyeCtGVEFhCn2m9wgli2dOCeTjR+UxEVXuNEyBhyjFaILrK+qHRXYmqkSQmc8v2KwUhBlRxEHqtkXm59An7SFMf8phlke7RQ47d0/hxb67ai2lWNCcMnoLa1linS++yDvxvFCU64ZK74YtEY20K1OeTneoFwsUhsZiaORrQg6dcvMzLb+uBlkJYa84+gYMdOe8hAP9X04Mf1lZej6cONOL33fUxu9SJBJoXFl6/eeL5mDVoPVgSK4obyFqfxe3AVKz67crBkXSIVR6V/t0Q2o62nFU2RjaxkR5/iLKTuVp8Bf2CoqwtjOwcDTV1wPvUoGqorMHS/Axnju9m9v23kd4DyUuB0Ce52xbNDda78JrBADbKoaVD8OdEll+WCqtwOTfbXp7lAnZ1lV1hSFyBr8eI+nuXp3li8uORFtjsKMCXHJbNgmiYJLV0bvk3asAqwVDHVwubVT17F0utTsHTiLNQc3IMR9R5cHD4MpfdLdXdo/N7yQ+w8tRMf1H2ALfmffQAPKghsqNGvCfz85z9HO40lAJ577jnk5+fj29/+NiPWX3311S+6eQYMGDBgwMDVJdJ/8pOf4JlnnsFrr73GKm0b+HJDJpJFe3FaN/G5NhFr77wD3JJuQ+QU+AlDtm4kwpA8J3kxUa7umjIloJalyZOgsuJrstsa7RiFUsRMyQZyFP/0oA0EEplbiDASXVKER+bkYFyFE5PcRE6qZKIWpAWEqMCVQZw0ceVkIxtEtorkpUxCyIWt+MKa/DPJw09U8ve3WNdQ5vV3PkwdR0pCsUKqvB3Zw8R7MB9lik0KKbzDJdJl2bD0//7TcgjKcQ67HQ2vlWLjq8DpVVbML1MKhw69Jw/bRxYgtaUaZg8QE8ZxxH2KfWR32sLK+g4VQJEDJTHzZ+P06VimoNeFeuL0TNDx5wyZjZtT6pgK0V3lxcHzaRg59DSGX/Ti5XRBOW6z4SGqpCt6tQtqsf7E833aLC7MxaK2ZjNsamLA2DkTg4uVheifiW0O5LtCZEkIJDwjV4RrX/f/zPj2+SJcd8aLBclluNBVj0G9jehoLMML0jV66CGFQ6IsEMpcv1LQiff83cIYv68tcOKc/+ZFSIkE+7ThU0agE8gvufFCI1prgfbWeFTsKkH8bVmw0jtOLKYpejqRJRt5mtWrGUNCIJxbtSxpM+Guw3GY0qXad8mKbq6olVOWhHdUygUg5bgHGFwOkO2MXFsh1PgtnT/HwvSF8LZ5EZ8Uj9dufI0RcZwkXc4Komqo8fXG7nACulqkuFyvQn6Ja73c9QLhkt969vGLGNcKRREeLrQrpWsXpbSBWe7Yk52YqhLbzvXF8Ly7HlVH3kHbPz+C3vJyv0f6dutItNWcw4jTl5DA+zOcl68wzzvf2oD66galKG4/xKleACWoT1XPclNUCoaeuwDL/2fvW+Cius7tFwgKCoKCwOjgA98oJJFRieZhnLxQE3tN0+sr0KZtTHuT9PbeJrTJzavetCW9t71t0jSxvcnVxJC/bWxNosTEsebhk0GNKPjEB4MDo7wEmeH9/337nD3s2ZwzoDEPda/+6ESYOWeffc6cfb71rW99HQFSDUM/en5df/NTvQ/Kxo0Y0NqAjtgOtPQNw4CODvzH9nBgXlfl10i3G4+alFEZWhoFS4Tz3/FrkfzcSZRCc31rDtAJDI+bAUyYqSW75B49wvz03bsPd56phm1UEGU8zZHD0ZXoinACE7RrZN//uweL95bj8LWA9dcvIOqWGYho7ESfQ1WswoUT3A/ojV+9rV40tzfjSPWlWcCNmqQqXN6gZ0QOsnZ5n5QdCgoKCgoKVwuR/vvf/x7Hjh1DYmIiRo4c2a1Z2W56uFO4bGBGJHO/a5FYo2fumOttsL6g2U2UrXFi+hYH0FoT+GG5PJUIdYnc5XEn+UDnLDNQnwUjkQ0U4dbsbFiZvN7E3oNDCu7ExIEM4qSpytr9bhEaj2xGWaYFp3a4mY97SoRb65YqkxByY0q+T5m5NSKG5cAzmDLP5Hj84I07BdUxqwReSbkMO1K/BS3gDdJw1XCMMmFgpqK32+G052r/ybkFux35vwXeOmPHyT8CyRE+TKhzY88mHwZ9B2j8p/twvnMYBuXYu5tbm6n67Hb2lhdL7fi4yMYqB8xyDiL49f5Qpl7dIM4FT3pERzMLnKOIQNTyXKT2Iqbj+x3likCly4LyT3wYEuXF5IHlSBjqxcwE3ZNYTPbI8yzZ2fSKCJbfKDe1dbthywamTNFEeIb7NZifnGiHsYctPz9ifwDyVGWbtLHqkblFeQivKcDwNjewvQoD6B5wy/WIzFyELKktAPW4JBKdLLvvv78Xx6twUVDr95UFTpzL1gqcON94dCMOnDnA/t3U2sS8kIsGRcLRXIpRP16C8Uf7ao0fxWaa1AGZ25XQd53WNMluhKw9CIs3uGA96EHVqGhsmRDJ/L9Tg63fUkKX3ZvG6P0uaM3qqaGBvH4Lxy97pJO1y5ZTW5jHs63agcqGIsQecqJsvA2p0RnAgQOBlXRyU0q+P6PSvSD3bNN+FT0cS0AinNvoCOsSWXswv+roNOZxzaoCgy108hiDkf3yGmCzMd96IoGzdGL7xYGl6GOpws7o8wgvzsf339qHiP0NKDq8G4P+/V+Q2mckBrSXofT/fg3Lay8j9jsPBqoyTBLhJ/L/iI8H1mFA+jhE3Di9VzYePHHyUKjuXS8mB/TtE/HfUXkI/a61YfgNt5nPleBHz6/rfjccg/fMRtT0a8fAc30wNDQaYRGRSD7fiQRvlHZOzJI9QrWCXSewezwm+frh16LgUU/nYErSFHj8C3igZZA8P23vlmHOsRC9+7tJlQSvymA7sGkK+plg436wfiwGHvdgQJuWQIsdOxmIiMHeDKEp7VAbI9GrvdWI6BOBsYPHsiasCgoKCt2w5a4vf5+dIUBbMhBWDoR0fnn7nfXul7cvBQWFy4dI/8Y3vvHFjEThK4FMJD/7rNYElGI5EsI0N2vW08ePa97p/BmenvfJ+3jYvgI0DG5BdGys9gej8lQiZ6jB4YwZ/p3xeIE1U+QEnCgC44ocXipuhLNntSCAAmG94eQFe3/3YMycng6kJmxGRlUBPG9GIrbKi84yKrXV1XcyCcEhe6dyuxuaWJoXI7sWI8VYT2Xj8nGQuor2QSS6GChFRqLjkBdE+b9rzUUqqaB7mhOjMZpZ0/DSZOEzK102RpBSJcPvf6+pUV4dYkPpWWBkFHBznx0Y2FSF8D07UfuNZfjB/2QgNHQq231I0UpMagYiaH5F7285GKaA32HDitVAUxMQExNY2d3jdZ9nEFASiU5Jj9RUFCXMR77HjhQxFpXnOnA4flX2S+8AzjoLbAPc+OET3W1U/JAJDgOCRbT1pcuNEgBzI4IYihso0v0b4b/jDfMMiCv//PDKAjMPW36dU6Lm4TysKrJja7P299zH7EAGtITTRx8B3/wm6yxM9jgyBbFggUam0yv1TlD4YqDW7ysXz299HpuObWL+4KLFC/2urrmO/fvdWDcWPrWakejpeyrg6h+LhOgweFOTYZV6cfjXHYPEtF8tSgrWGAfej3PhtTAPa6LJiHRp/WYkP1excgJSVwyzZscAqr016OOrg/tMKd75PP7fOtKT0tEY3gh7gQvYVoAbQlpwrg4YeNYFdHqA6urASjq5KaV4j5YT4b1peGlk29Hb8hh5/Xa50Nh6FNsSPaic8y3k3tCLRqPyGC/ETs5u99sB8aqGj+Ob4LwxFJaoGCxPW4QJHRWIbDyLc+VHUeutxcL7n8XHnU8hbtNWRFS2AM0vd611QRLhf9n6a/zV0oC6yfU49D3NmqQn+BMo/BlT9AunBXLRIjhSAN8YIGJmInK+Z0J6m1zXrg0PozS8FnsHtyMu8xrMv+VB4/W7B0EE/T+vhiAVNzVy5Up+64IccxEFvxbHjdMeaowazwa5BtncPJraNTZ5nNK1Xhbhw6kHs7BrYjQKYrSH/NwHfgWMdmgPcHv0a7+ggFnvUHKFJwcWTFzAyHR6XXHXil7Ns8LViVGjRgVtcF5GMZKCgoKCgsKVSqRTWbjClQsi0Yn3onjk7rs1rq20FDh5Uvs7ceL0TE7P4gcy7NhcCUQmWjA83I2JcCFRDGo4+fnOO1qweuqUnxQ3q4xOcjm1Zp6657VYKs5jgSUuB2vG6PePpG0TeZuWhqKELDhKLRi1JA/9n7AjlUyfjdAb/3Fo1e0Jz85G6GbgjM/CfLLjk31A+Y6uJpoUjHDI3qkc3O6GCEwynjdS2l+Il6oM8T00Bl6uzAPLzEyEjolABMhr3ngT3bZlNEazEncDT0/op5CuHS6KevRRjZOgKSs9sAj9+gJnUxeyhIW4+7LmHCxKt2JWoitQ1WeQVKD/rP3QieFHHBiRyfTv6DWMyBOaM3qgnzwZY/oBM8ZQwifIXBOEQJX5r+fbUJ1ow9EGoHE68EPOe9D76JrWS6rZ/mSCwyA457skxwXKH92+x4G5E4JcEwaKdPZDpbTUQJCUmJQl6wmCh21AM1cK7OnfvD/Aww8javV6zOjjwhFqLmiXrg3ebMHk+Fas0H4Uvlio9fvKBVlxNLU1sVdONBKB94+yf7C/90EfJMdoib/Mbz8Bz6B8NFa40OwrR935clj5TYav33xtbG0lc33tniETevp3fJJOrt3VICRUhfWbW1wkHXQxdTj77kvVWyfiWuCcGYsDqU3YF8wOo5eJcFLHzpwwE6GRuxnZTw3SmYqbr4l03ze6Z4q+4BxyIlxO7Bqt0Wa2HcGOgydn5fW7qgoZ5R7gmgRE9aRuNrOUCWYnx8l+Ibm6Y/pRVtVA1QyEB6c+6G/smjokFd5BUWiPGYiY5DEsaYEJGYgathwn459H8lYXIoZYu47bIKnA1M8+B459fwHqTnx0cUpmvn7zZ0AubiCRxv8uh2OCgUe4PNe8eo/Wbua9no9rBrYiegzgGNUHk+ZkYP4cKXHBn0N6I4jQr//X9r6GysZK/NtHrfC6YoEYq/n1IK/fXCzC/QapIkxoJG4I8XzL16B0rROJHrtlO8Y3p6Lzu/O1OaPvHr2X9kNJJ/0513YasG2lZ1rNMobIc0WgK/QG//qv/xrw79bWVuzZs4dZvDxKwYGCgoKCgsKVRqR3dnYGzSIrXDlYvFgj0+mVOBeKCZYsCXzPI4/walgb6qw27HcBrxYD3xnjRE6GHoCL5Cc96G/bpinSOaRggxqITo90YGKVK4AU37zagtlFech4zM4sI/x2MFldfqH+0nC7HVGwMRI9vaIAnnxtV558B/O3DiDVxeZdYjAijot8MITS3xSnU7N0celKeAJ9jhOEBDMvDtnuhr8aBYWUyaC542XkvVG2ie95+22tDN/fIVYLmKwv5CLHHwQG8b3mAZquFOwV+NgBVhXwyc8LkdLoxo2Rdnx03sYOSVQ607D+y5ONrKXZePTRjoByZe1QbIhizWWF8yGeN/YXG2tWa4cDv5roAtqKteBOJtKDVR1wMoeuoRd0NRwnIcrLYfUe1q41I8sdca65gpDEi1tsXNDOTkFA4sKopJreJFYe8OCcAnzqLbBoEez2bJZkqip24O1OO86EdTUkNYRsf0Tv44kfIg+oiaB+DQb0OzDjrQTfdsN/U9O5TqClVYu3A7YThKzptUJT4aKh1u+rAyLRyUFENFejt6MdR2uOYsnbS/DETU8g++UClBSsgmddPhLmLwKG6PcHvn7zRCplz6ncJwj85JpL80QvTQrD9kQ3EpN9mCs2DtygqcMDoFe1RKZbUBftRuKuHZi79hPEzB0HZ0qXXU0Aqc7v6fK9xKhaSF6T+ZpIXcSpko6aqrLBBWlIISfCZbsWoUF0N5hsV7TiscnJWW43wsc6ZgyiMzIwi7ZhllyQ52Tu3K41zQwS2d/QUI02Xx0qijfj9vJqtA0PwZ4+e5jn/tOznvbb6JC6umxyMxYNseGm7/4cnsHaXLNz9J9rzCvsBEKXLESYdcyYLKywPKC9N0pXaRgdl9ECxdch/gzIq58yM7sRvn7Icy2u3cO2MO/1+vhUZPxLDibRW67N6T4Wvkb3UhBB53jXupfwT3vPAe2hzG89qAWhvH7zRAGBPPQpIU7XJDVYP1iI1Z+6g1dwyNen9O/hEzPRuacMadfegfnklW507fPn3N4IOxQUDPCjH/3I8Pd/+MMf4ORNuRQUFBQUFK4kIn3SpEl46qmnsGDBAvTlfrgGOHLkCH7zm99gxIgR+OlPf3opx6nwJYHIc1G0SM/JTzzBBKfM2mXgQE1hXFmpxb9cJEzP1tymJSDwISxdCtx+e2ADTOlhPNXtQKq3ABhDTUez4DrmQ+N7WzCqORrhLg+ziLBYbOzZP2SqDSBS3CDAov8/lWrB+fJI1ERYgHwHYrcXgGjaACJdat7lJwRFb25OpMtj5sGTkU2H3IRRfI8YeBup1HhQSIEgLzcn1tlE2RZ4+MJ7iBhubNQCcSmIp880Pqk1+YwW9y0eO/e95l6xsj+2EYSAtqghA679LmS0FqC6D1DZAYxd6wAeCFSRi6+mkEkQ4YM0pNj1DnSgAJgbpCFrbwI/biEjKsR5ECs3n5PmuuJQIz6rtOCGlkjE+nz4z+g8rEy1w/agrbsrkZHlitn1RSXVpFynYRdkM7KlckgBy29cn+zSzvN773W/zuQxcsI7WzBJp++i3mz0mW8BmzZp/Msa4kDk76+RulF+jY/H+eZwuNrjuzkdUcLDARuWFOZplSTc91WccxWQf2FQ6/fVATO/8JnDZ+Ljkx+jX59+8Jz3oL2jHc99/BxTFduyspGaJXxGXr9lj/QeemcQgf6PuON4e0gdPknwYmnJ25ibF4H+6RbQgnPuxqmaEpffN4TqLaLx6efVF8Zj3P46VHs3onFPOcoSPcBtkjpd319DQw0jflsaq5BoVi1kptSW12+zJLhZIly8d4mJRdlP3cSiLaARpVFyVl6/bTYt8fHgkyzxEXDexGOX76/y2mAEfV3am5KIkpTz6F9Vgan7GhFRCdhK67HP8yowq+vBkCVGboOmjk/qarwRkBwIpoDX/cOpQoElVxpWmlcGBlu/+XrKk/79+nWRvkHsZALmWlB2k+f6vXvKkDj/DsydY1C9IwswROtB+fqi7eqCCFt2Nh5vmY6QU7VAZH8Mj4zvLqQQIa/fPFHAFen0fGexoKjoPfyi9R0c2KuJGNh3xKAReE+J8NqyAwg/dwatZQe6DYX6KniOR2J4hA8pNB4+X0ZNeRUULgJZWVn42c9+xpqgKygoKCgoXFFE+gsvvIDc3Fz88Ic/xG233cYekIcOHYqIiAjU1taipKQEn376KQ4cOICHHnoIP/jBD774kSt84RDjZXolnpVis5oaTaDGOcBu8VKwgJTDyGNbD0we+DQb9/w5C9M7t6McyUB/K/s7f/Yf/J4QqIrktD7gPrtd6NPiRV2JG9cutjBP84GZkvpHH2PJKic8Rx1IsNg1b1cRxCyKTdakoNYQsuJYauZkevzifws+n4ZzpgfxB1x2FBTbAjdNfyNDexO1E7NN8diBBGjqNiP07w9QE0JO9u7cCaxeLTCtwcvIqSqgrNWJtiMO+EbZ8c39DtwbVUCm6XCtdMABOybl2PynrqPDeAoDjkv276XpANgcMDqYGpT2RIIYHS+3KCHwnXKFOA9iDRIKRA67d1swse9xlFZZ0LK1EJ3eQwDqkdG3LzLmE7Nl6847GVmuyOPkAfDkyVpJtWAnkPSdLNxP76MvY5mQCLqQ60w6fXR6KS6nTXU7AXxeZAWm/B3fsQOJqMItkTuwN7rL/kncnL+ShF/f4pxfRCDeQ3sDBR1q/b46seqzVUyhfv919+O/bv8vPLn5Seyt3ItaXy0j1FfuXWmsYJW/27wBJk+EG92g9fX79b7F+NXE07hvbyfWFQDNAw6hMvw1eI6PQkGmF5OOJiC1qEF7PyeZJYxLuxn9SquRHpKI+M9M7Ez0+9lHDUUskcl8sIVxGK5/PTX/NCIg+ed6SoSL99knnwz0U5f3r3/Gr9IXbTTkRLh0Lqh6gCw4mDBAJtJlX22CQUNJw2PWrXhGJaVh2/23IeTAMRze/BGG17ViyjE3zo0ay95LDTzJe3zSnBzk6srlDmEBD0gOGFUQCPPI/MPJ5ocqFIyU3b1Zw+QGseJ2uAc/J3xlUUNeHrz796LsyF4khvRHtNuNuSRfr6LKg64qw4DkgFB51239lq8vqsCkag6aW7cbGRnzgKEZgVViRtec2fpNv6e5pvV+5kyWCH+45WU43W5YWi1dFjZGVW9mCXD99UD5bmSeqcPe8t2srYmId6Pd7Lv7zI4dSDkoPNMaNeXtJQLm9HP0QVC4MvDXv/4Vg8k+TEFBQUFB4Uoj0u1EHDmdLNj+f//v/2H16tU4efIkvF4v4uPjcd111yE7OxtLlizBIGoqqXBFgJ7HiTel3ovLl2visQ8/1OKC9nbg8ceBl18GHnzQvB+oISFNkFVa9B4KIAsL8eab2fB2LkI7gKbQaIxr0hTj3P4l4+A64FQJPt0K3JiTjRsjnfjtPAcyyE+7uBjpiQk4ez4S4262aFYs1BiUXg3wrtuGAq8NWW5NDRfgcU4TcOgQNv29EQ+fmorFi214+mmDh34xSDSzcJEVbMGSDVSSbeRlLCnT7WkA0gC7y6E1heTKOFKk8QBPDt5F2xR59/zYedkyJ3spe0KZE/q9zF7q23elZWG1NZeR22yYm1iYrM8PjUMjfTvWF7CGpw6rLeDwjxzRuGEjy/CA4xDIbCIEmeqtJ0/0YHMtl4bTTvkxUpk4/Z1bogjl1aSwji1yY+TEFtSecON8FdDcN7D5Jj/f3JKIvjtT9lvwvahIjLuvy5O9a0ptsFETWE7ScELIyE6ASAJKAHBvd6PqiJ6OXZ9W2g05vdD3OGDiRTIiWBk6YdEi0OFXRy9icT73xCfw7+35eDvykItbD+RhyCEvBqREI6432zbB889LSnoFQ6j1++oEkehkU0EoWFqA5bOX4zvrvoOzTWfhbfMywjP9pXRMGDIBj818rPeElnh/kIjY2FovOtGJfy4GrncBJ2NaUJ54FmMG3IRndpQjo/AgcOwU+7jFk4vK85VIGpAE90/c/m3dsK8OaBwIdAwEYsMxa8ai7nYm+n2NGh8eEBof+u/ntK2GBvyy2YHftn2sNUK0PxA4fjO7DpGAZJMXRNVspGbnCfAeEuFEytrckvWInAiX1ltSohOJzqx4ZMhJ2uho7VhofSB/d1rPZAWxw4GGv6/B4aH9kDgjgzXAzKW/3QBgWXc1RUfBetbAk7zHxevlSM0RrDyyEpaBFmbV0s2X3GD97paAMFungq1hco8T0TJP8uAXzyv5s7sbdqP/+dM42nYYk71RmCnanwnXCBG+aw6swRvFb+Cpv5zBHYfaMXDmLX7CnpPCd6VbkAq90oHmmQQJ9EO2WiLBLlaJ0U+wZEcP67c1xoqD1Qcx3Tq963wY2brJ25H+PbsqErFNoch0abY9IsHNz+X5vj5swQ6MGZAMa0m5cVVdL8E941/d86pmM2VQSaNw5YGeNUSbObKdq6ysxJkzZ/DSSy99pWNTUFBQUFD4QpuN3nDDDexH4eoAPY8Tic6JMfr3q69qnGpnpxYD0N+ee06LP/hzeTelKHlQiiorEdxOJCoKvmbgQJHmOPEGstnPzZFOPDFF21iqQ7d/mWAFhsVgxftaMHm914HwTQXAEk2NNMrlwqj+HmC0TmAbkXX6IO/1WTA90s0U6YyQFQMMOsioKLzw/jQcqdSOPYDfNmpYytXMMjg5Gswmpbfek/oxtcRbMGKHAzGeAsCqf0YOxKWAP2hcJiY3RJUU9x29447uY9S36yB1vNAslvluu236JojAt8EGJ0Lh6Gp4KvjZbt6cjD1/LsKEdZsx//d22Ojv9Dei5oX97HzbBfy5AOc+BG5jZP3nlDDLpeHieRAal7H3CWQ7jYYU6ZF9j2PnSQvOtVswpvUoknjzTUGxSMdOoDhhqseN2vrAhnYHHnYyi5rKIguQ4Q4sDdcbxfobofamMd4FoEcew8AL3RC6+X2SE8gSXGEIqYUrkXpoPUoqiuBqzMDmKAsGIgsZTS7E9RVslS4Q9FXi+R0RSqluDLV+X13gXunUYDTrjSz27+jwaEZkRIRFwH3ODW+7FyfqTyDDksGIM0OlqJwIFxcR/T5XHR2GOq8bHeOS0CekHvlplAYHmiJCcUPfeCSVUL8JLzB6ApAwjJHMlcffZ+8ZdrgysDeEaE1y/rxGiMqZev1L3o2MtttZs9XKw0WIqz2DtuoPUD39PNaWrtUaIhrdEHpj2cbJUTOrNvGmwxuByODzRj5569ejYdAA1HacR2i9C1bR3i1IIpxU6N2U6BxyEp9XKxGJTnNJazglZPlY9M8UHd+C/EQPUm6zMhJdvAZoTsnLnGxYyH6GxhqR0kWucmwu24zXj72OrLFZeCEhB3id5iJwP3Re9u0uQNIHHnSeLkKGbU3vqheDQe5xIlYK0DHX1WlrOyn0hXWUji/2cBFGtwyG9RzQ2Xa+y/5MsuGhY91yYgu2lm/FmabzON/SFwPJV18//45P8zQV/pgspHKinNu/TJ/eff0Wn0k5cW+WCO8BlACj727A+TCydesB1mVaF/jDGdHdKgpsm0pgy9+CTyytKGkoAypc6Kg8j4G+esR29r2o9ZvGSyR6RUMFS/hxIl0p1a9sfOMb3wj4d2hoKIYMGYJZs2ZhwoQJX9m4FBQUFBQUvnAiXeHqAj2LkxKd95+kiuWbb9bEPfR8TnaUFRVAc3OgArUbH0y+jtQgiXusioEnJyonTMCOhgzke+xMxDOuQWsiuS/Sjt235eI2fduk6v3wYwtum+zGmfhU4CzwcZgd37pVsPcgj1JS6/LgxCiY0AeZEhmJFPp7IZXfaoHwAytsWLsWuOeeDDz7bDJ8f09Av1pg7NgeAnBRoSezeDx4Ki3t8mCXA0iaZBonvYqQt6kf06kdbjZfATYtciDem0DVbMwU5HFSmAJwMRjknuu6//XkeCeeiczzN4v1RIIp/QOEVrk2Rhj4nWvz9PmrqMC1teMxsuEQUk/u09TrBPEi0n+ef8mJcc0OHD5iJ4vWC2NNJVUckf2efDcyPF5Ei2XKsqLLQO1Gp7D/P9wYPrUFsye6sb8YGB4jXGf8fPt8sDnyYPP58E/hO/D+oEycuy6whJ2uc/J5H+iKBDzeLuUaES5EEpCykAh8EcL2e+2DawD+Vspn0Q8VJQQ0vpNUlPL2De2AeYNYngTRkdzpQhY8ODMhC5syctHf4vR/5y4GpKDn+w7Wf1BB4Wr2TCcSnSvTeUPSTGsm/lr6V5TVlGFE7Ag/EWdozREsEa7fhzznK9BcX4WJralM/V1duwVFzU2YdO1tSIrJZO/78NNVeDlmJ9Ln3o+nZ2Uj6b80Rfo3KqKAw8I6yu+7ra0aCWwEI/sKmw3PNq7HC7H5mDG8Hx6OSkPYdbcjTlekd4OZZzpf9ziEKjBT5bDYW8XMPkW6d7piQvBxHBgx7V8Te0iEG8LANiXg9zwRTg9vo0cHbIuU2W/fPQZ9MUa7BpxONP75Sb8vPcF/PdyQG7h+i41dAUw+1Yxb9xYBUejud26zYbXPgY0NHlzf0YSGOJdmIXIh67fwXho3Xavf5t74oh88fxajc0HNZKmprJB0psapROL+YNBARCAGrsF9Mbw1zrhficsFm9WKP3mS4XH0R9HwgTg9fQYs9+mz4HRiyQYXklLSMLXBEjgGnsiQk92c/KdXTqRfJPh3lObC/28Du70e51lP/qQUrMIz1Hw4UZgLWmS3b4dtcAxGxPTBcWsICsYAw8ZbMS9ar+K7iHGTEl1ujmxqDaRwReBpo0pbBQUFBQWFyxSKSFcICs5h0vM4t/3kMRLFDGSpQMIbI6tvzr3Nnp2B5Hv0MlBOxolNm8aPB+bNQ1RqNmasdOKbpXmogwvjmouRleTCGMG65KFyGxZ48jBwawH+9D1gtVVTO2eIwYFAnpf5LOh0RyLEZ0FKT+otnTxcu9bGrKnL/lIEpO/C/9w3De/dOrV7vCBvI5iqnI+Jq+04xACHyAMKCDduDCRuRcU7V51HRmJ4JlnX6DYtRF7yIE5MWgQLoIwU9YJdi//fsmxZ2A5/K1l3zKJqgegE/9iygjiOBMxfRQXS24rgHXkeQ/p3KbWIEF25jnxJu3iN9PttePNNGxYvRvD5RpD96eeayH5/ImKRQbNOefocXVUF/fMdOFBnwQBfFB58cRomlobAnQ+E03WmqysbPF543tyBhCgvohvdGHe2CuOu1xMEguLNSgkgqiiQbYB4kokH5OIxyt6wnODpTWNYg9NPiSM/P5RjcL7ligp9rFtW2/GPwzZWPe/nnuRzolsGRVsszId2uMWCDHceHbD2npKSAO/83pLfsvCTW71QIo6U6nz6FBSuZnCiil7FhqRzx81lxJUl2uIn4jihTr/jFg9TeCKcEnpy0k6/Dw0eNQGl44Zh7PxFWEOK6bI8YE8B0GAFyK47NRUP1Zcjdr8Lmb95AYiaq9m5GK1RupKWqdyHhCNkTHzg2h2k+ee+s6+iNqoW3vYYWKIs+NmN38UTU9caT4zsaW3UALynJtF87JQgJ/Uz3XRM7M/829X7cvS7MxN1GRF+Apu9j5QJYiK8tw1sxO3LxDpPhBOJLlUv0Xkv9hTjO21psBVri0GGgS+9eD34CU6+34oK3GONw5yTwzGhwqc5rWVlYX2yDy/qlRB0zdFni24qwj+ucbGETsA2jObc7Hy5XGhsPcrI/vdnzkDOhGVdz2+C/VnJmRJ4Wo9i+MRMpPj0h5G8PJzf/0csrvRgaEssxoyeihmDb8H52ips2ZaPqFtTteOzWFAX0gL3gY9gdYbDWtMIa5UPUyzXA78TfMQcDli3FSMnJguo1m1aKCFEFm+khO+pKaecEOrl+l3kLsLm45vZnD6/9XlsOraJ/W7NvWuMewGYXNuUVHiz+E0sTluMp2c9jdR9bqSSD/ooN8ilhkHPVEdmZmJ4RATaInzoLN2BhJnzAPquS010L7Y5Mk9wJEYlwnXOxdTpiky//HHu3Llev3fgwIFf6FgUFBQUFBQuJRSRrtArGNl+irEsPbuTnbLUy8n/7E5cGlO2yUpXThIWFrJGjDa4gLZi4F4KRrI0eSkpvej3AF5MdmD9WQv2DcrC2Xg73n4bKHtmFfrF5WPA9xfht2ezkVxlx2L6uN2OU086EFvlRd0Od2AwLjYb3eLGmOR4WPVy2wULNGLxwdGbgaJDmBh1CJOGbdbbWxr4TIoHaqYgkwN/sZEZ/yyfXGLxaZvDhgFxcd0bcenkBfm/8zip6lsr0XfTejTfNg/43ePBG8MFU9QbHYORql0P1u8iS5wsG4b7LMCOriZcKWcLkWsldR+VutsCYy2uWOYXSmEhInftwoghhxC6r8pfYv0fDTZsLwGO5HcRpiRoCRC1WCxwVUcif7UFJKIK6tXPj0OvWAhIRPTQO5ZZAJf6YDvuRmRTE77bvh7rB87D2XmPAxkJeHdzKFPgP7MjDymUUEhLQ1FCFradt2BGghuz7hbU7aKNASE3V1P2y4Q9TzIZWbuYefHz7wu9cs93g+CW/vzUU9pXj3qijRgBnDwpzVOQJAQP/O9tcmF4iAO+/sJ3w0gtL1c4iORBSwsGHKxEVKQrwDtf5IOmTEGvrV6oYoaEiFR9r6BwtUMmrDiIpKIfIkjJA3r1vtWI6huFO8bcwZSi1JCUCC1HtBUzbpmBhHe3dVdl69/1xJGTkEj3qDPorsrVb6AvNiej8mAVptWEY8ufn8Qz+73YVbELP8Q0/FdYFtYfXo8XDz6Jh5IzMTcrCyd2rUOf+irUle7oTqTr9yjNCsKNb//ve0j8yInHrknE/tkRePT0cMQfPAl0btY8qo3ISTnjLyp4ZZg1ieYLBJHf/KYj+4HL2+Xrty+CNeykY6D5IAJ7wNSZ2n2bJ8LN1m+jqiGjZwo5ES6v3+lkWZaFO/+3CPhoPZCSgujYhG6+9NSolq4HtsnTCDyuigo21wl9rAiNPaclCrKzGYnOKyHo+qNrjRG9kNbv1mrkl61G4meW4F7ZfPxFRcjcsQ/RU0egc1mONk5xsZCaZKYlnIV1oBVLNhQy0vt7TVGoralHZ/pYba6jo1G0LZ/Z28zYsFJrgOpy4VxdJUrjmlF1TTpmWe4OXL+Fddc1Iw2OOBemDpuqeaRLa2S3ppxiHx5hHmRBRzBS+vW9r+O9o++x7ydZ5jS1NbFXw+tB/G/JooiSCgsqPdh36lVg1tOBtjPinArNx0kowJqOhhcC+9yoOliE5r3OQIsiyaZlSlLPCzgR+mT1Ut9cz/o4sClQRPplj9jY2ABf9GBop+ZbCgoKCgoKVxqRfvr0aQwdyjsiKVxtMLL9lHlkiiWJjFu3TrNdoJiAuEBSie7fDzzQ34Ib+UO6RGr6GzmKpC73E+VwOHBbWwHCp2XhGW8u0jc6YXfmYW7nOiRXlKDy9/WwdrqxNdKOzkdyQX0bExZBa861yLj81JPvQOz2AjSX0bi0AHnFCrCfjlUWeLYd18a2b1/XQcsIotjuNlk9fZYmmZhNYgTp4VMsOTf6jO6tcf5IK9qbAO+eUgx45hltG7StYNYcPGjSA98ex0rQg6vSD11o3F6MIxEu3Gp3IIUavYpNO8WAkETnjziw7aQdLpcNNqtENJDi8Z57NJKCki36mA17tjmd+PCnDrx8xM7U6U9HuFFT4UVjkxubBcJdGm4gh8KJDE72ywkS4cNLXA4kpdmxvsqG9q070NlOMmcfBg/qgyXzAI8+Nj7NzGufbdKOKNjQxwFEsb85NTKcE+j0xRC8+wO4D35eCGY+6KKfvWhH8PDDrNfA2Z0uxBzzIJq/VwJNM5HoNAyK6+lH4iHM9yco0qd9WITra9ajeTAF8T2o5fl25AZxRUXof7KSkfkjhP2Lc9ITkU7DIm6CQFb+ct7haodavxXMIHpAt7S3oNpbzQj1hAEJKD1bykj1+0bfh5mW6wK/t1wNbfRd52pYYX2/zdMGXHMftrQeZaSlb/tnePhYOzLcH+FU5VYguR+mJbVh44x6zH1qGyILLPCQzYTQWFP2UD6wYSViN65HdXUUs/igpouHHn4BHatWwbNtW1eW34iclB9gRAVvt0kySYQb/Z4SmfTfZClC93p5u9Jn6Xja248juaMTo8gnjz5D6n9al83Wb5HwlNfrXiTCi15/Hh0b1uPI0Ajceq0dief1P/BMJNmO6Mfj8DkYiU7XA1PPk/+5qLymbTc0aHYl9KykP6uIlRAiAlTQ7gjU1FSg8WQTNgte2YbnW/CYjwjrh4w+1kAvdukYeXUFKbXXH1mPySfjYXXXY1xqGhA3Fh3fWATP7bezRZCU6Cm0nw0uYJt2bKFZc9GQAgyfo5P1BDkJnJWF1XOszI6kMtqqeaTza55XL8iJcHms4n/zzwZTsUvgdk3+eTZ6fpOvdX38PKlwbpTuWyhaIfJGqLJKnl97+jN7TWgN6tvqcDa0ihXWGdm09ESk03mmXg6+Nh9TpHMiXeHyxz/+8Q//f584cQI//elP8e1vfxvXX0/lmVTpvB0rV67EL3/5y69wlAoKCgoKCl8gkT5p0iT84Q9/wGK/p4KCQjehEA4e1Mh0slXkcfWePRq/unm/G2OtXiSJpDB/cKfA0YjU1cug2avuJ0nqZ1L+Joa50DSgGEcbrTiHGHT4orEgtgATRgDD9AaPqdk29iNKokX1L6mSO8si0e/mTGC0xL7RZ6jUmoKaYCW6vSCe/Z+RG3nKvqay+bOo4BUDGv4ZMq7fvh2J8cmoHDEeCZ1unD/YCpw6JZhWS6p5Avf0YP7whXAVurG3yoJrE92a1YhcIi4Z4B8+noYd3iwMa3IFNHrtdpw62ZJ6sgAzqHIZ3QMxBmJyiYAXPHgNe7Y5HMzWZ1wzmMXL06vtaC0Cal32LsJd2LfDYUPZGiemb3EAy6VAMJhfvb4va3EBctJciHU5sKdfJobEAJPu1wNj3Rs28BKg/9OOn3mjMyJHsOfhiSJeiaF/F2iaqFGr3eVAVZELzYe86Gcbg8SsDOMkiJktT04Odhy1YttxXQlvwibTJolEpzkTbGx7hnStx7keBsjGWFR/m6nlJUWmP0HgdKIfNQ7UyhW6baY3hDi3AqZjmjtXeaPLUOu3ghmIkF4+ezmziCBVKynSqRkpEZGPbHgEDc0N2FO5B7uPnoZNTuxRGZpoeSKThvy7LjRqjhoKRlpO+V0tJh47gbC2TrR2tCG+thP3NA9E63iNjvM31hTWbiJ1RQ/lO7dWoe/WOnRkpADLlgTul1ukmDUc782NRrZJ4YnLYGs/rcm0tlE12fz53e/foojgySdx752ZONVnFBJCPcDHH2uq9mDrN/1bJDydTrjWrsTe0Cpc25EI64Ic44SAcCwr41wYMNKLYeeaEP7BJuDWJcCyZd2V0S4XlkQDSSkzMGl2jm57UhK4ftN6Ruu3lAg3rIRwOtH/Ny8g1lKLN/EmnravRuvpItTGuboR7pyMLftwDaZXbQG+tzzAGochiHqblPO2rcAqTw0yDtdhwhHy+9L7z0jiBF6dgQgnEKM9n1ndbuT0tWhkPR0zv5ZEH3+LBfYU7bmUvi9ECDduy0dGnZ7EpnN09mxXlle+pthBBva/aajzwLPhTSSERpkmwm3DbDhSe4Sp4M0qTgwhrc3j+HHcsMj8uyCr5KXv9OCDRRhQ2whrR6JhIkNuTGsEOs9tHW344dQfsvf7m90qXPa4mXoz6Pj5z3+O3/zmN1gkqGPuvvtupKWlYcWKFchhpcsKCgoKCgpXGJH+3HPPYdmyZfjb3/6GV155BYMHD/5iR6Zw2UAUq3I7BYp1eNxBtncUc33YPhvJncD9RiWngsVLAPnGyV6uuqXS0rOFSPHqpORtWXj+T3a8fcKGeyxO/L/vOzBWIuQYBFUaNcfkcUFuBO3Lq5HoPFDmQS8HjYsUV2QETWotftBGkMlnuQmZSZO0AHAGmQJtCtqIpJCJZ/4ZIi7KyjAgORGj27zoSByP84MGaYp0EWKAJFu6uFzoWF+AEWdbMCCsUrPRMfJ2FbbTvsOC/tVuHO8/FdfPsnY1ehXnTyBbhoyIxCSrBXZ6TnZI5IpASPcIiwWDhkXCUuvDi8nks21HxppcrXGZDvLb9q4tQMk6wPKgDdMTHMjwFGhNTGmMvNkX+f72MkN0x8n1OJA6F97fF3RdWx0dXd7lMuTzzBXo5JtK4P6pegk1NfhktkbFxXC2pKEIWYhItCOHyiqM/E3FHgMiSWSz4dQiGz7JB6wUq5hcpvR1oqG//LI2HazJ6MWQz0Zl6hxy0z4OI5WlAfEjclQ01cFAm9iyRTsmsfGxgga1fisEg2y7wZXARKofrTmK1vZWPNdnO2YMbEJM7Dg8YGZ5IpOGctUT3ete15Nmudpa9Ktjq9Bw4hCiR47HT0cLVVQcwjpkvy+QnCP1KiJigZETA+8hYtNOPkZBZd3rRLi8Bsq2LUaKYU4S0bpNNyUSCdCNSf6M3sSRbGtS6DMn89GRPlyrRDNbv41saBwOdBSsR3xbHTrCYoEYXSVu4tVedLoIW8Y3oiZrKG6oisD1vikANc8UK5zofNF5LCqCdb0TOZSd/J5UcSSv31Ii3BArV+K7he1IGhuNskWL2T4zbGu61m89KfBmYhXKxyYyonh6VYLm285v7LL4wixBoh/v3SEtOFcXiwFjU4BR4V3JHqPnDhPldrdKC94I/L33NEtCmuPTYBY929qPA9eM0uxxjOxaxGuKIPytJN2CHbsbcbx/M+z9UkwT4e4GN47XHcdzHz/H/t1rIl2+1vV+BKbN6eXryICMT7xlHjChe9NRf3KCrd/BF3Dy4I8Mi2Sv4ucUriyQ+vxleuiUQNUm3/ve976SMSkoKCgoKHzhRPoPf/hDZGVl4bvf/S5SU1Pxpz/9CXfddddF71jhyoP4DC46TixdCrz1FvDM6Qw8Xj8V5euBp2Wf7Gef1T7Q1ATf2vU4G5aEmPi+iJ4hKJ2NPL1tNsy1AOfygbmLyBrF5AFcCALs4q9KJLWaGOTcdx+wa5emEqMgnLoYhoebB27y58WAhNTPNGYK4iRFUzfwwIU8ckjxV1amvY8HriIByZuSTpyoBb4UHFIAzz3SjU4OBycZpk5FKKwI21KE/jWVhnMmbocU/XWv5iELBQgZk4aMDIE4MSIa3G74arxoryhEyPNuYJ4FrrQsrbmk3YYpU4IQ0jJIRWX1YtxY3UqAk+MCSFkfVROJsvM+XJefpzcSNfC+nTChy0fcCGIpeT/trWbEdDfSQlZlu1yo3OdBeeEOJMd7kTQqsnsJdVgYvMfd8EWNQ1ISMHZqEOJZTkDxQNjpxPB8B4Z77HC7zYNRukyI4yHHosrKrgr9C4bRddVTAzmJrKBDWO2gpsEXT4DT55Yv78Ge5iqGWr8VeotVn61iBB352n7n2u/gv27/L2wr3YZ3Wo/j3Zg6zD+6EQ/I9yEqB/noI3YPa3Buw6Fd65A0PgPWBgSS2FJvCPri3npaUKAaEWjCOtSNZBMr1ghGTTuNKqCMbjRGFVj8s3R84vodzMuaJ8J5h3Zaj+kGK+9fT4KzV04Op6RoyUmz9dvEhob8qVsrSzHweJPxs4ywJq1sXofBJWX41zPDcP+jqwPHLz5jCQmR0jOl+DfeONRuZ5ULjhRg0hzd/7q36zeAwZGDcF+SDdgZAUQ5A/e/ciUGvL0aiaPbsHVaNO45f1QjpKn5pbgOitZnZo1Z9ffH+nyIpWete/UKP/QiES6v3z4fXB/8FZt3/gmTflSEDN7IWmwETrkE1qh1FKKeWt51LYtViHIShFd06OeMfN3XzCZbpRTMny1sQ8LslNl4de+rzFOcbF16TaSbHSd/NfoOGJHr+rXY8Pc1KKLGtE8t/1zkNyUGvG1e9qpw5SI5OZk9ezxPneEF/PnPf2Z/U1BQUFBQuGKbjY4aNQqbN2/Giy++iAULFmDixIkIIx9IAbt3777UY1S4zEBC6uee04RVnE8lgVWfPppg7c03gacjpCCPAh0KNH0++Lx94O7oj+aIcESLVi886JDsXwwtQGQIwQAz3+DP/FwdzYMxsQycmFN6uKPGX1SyzEkAM2WbUWDCA336LKn2CHTcshLIyK6DAtmYmMByebnMW/ZYDxYcynPBt5OQACsp7r9J+xGIZQOSlIZIlevp5yxI9kWidnsV+pQU41r+fiPSwm7HhleBVrcLo8gGJiMLq625Gh/g6F0jSX5dVa22YFFnJKzXa1YCa3dY8LPxADlW8CakZE/jHezFkLAdGO6hOZY8akUVdW+YW/5+I4uVYAkEcZ+rVqE8/yiK6ylYKEfSTboVAm1Tr7So+tM6RJZXwRL2EYZbD2M4uzQEKwCj886Vg3xsLhcyPOTBTt7s5sdGlzYRz1QwIW6Og/Ja9D0V57XX6K0nC/cIdtl701+tRwQTliqo9VuhdyBijgi6YdHDmEKUmoD2be2LTGsmDpw5gAddyUBhQQAZzohBuuf37QtP/xbUnzqFhLJK4Ia52t94E2T+XuHe0KMCNdgXW7Q4Ee/D6eldTTtp7b4ABbN/n+L2KUlAx0eg93Av62CJcFq3CVyRLifC6WGI/lu0wumpOsvEM52aPFrp32UFXXMh3YdXhZcgf9gWJMdMhv2P+zC65DBW//vtuHnmEs0Oxmj9JkJ92TL8sXkdvDv2oe3dMuDR1X5f8Cyy6OlFI0lKztB19URiMm6gBto1Naj53a/w3vpf4vjPf4ynqdGljv5hkRgROxj310Qj46BHI9HFtdTALsgQ4roZjHCna1OwpDFcv/PymJf7uPJ6DP2kArhmZrdG4Hv/9zkMKjuAlgG1SHpxJbBAGINZEoT3GNCfKe+N8GF6aQLrC5Aa5DuRYcnAEzc9EeiNLsz1y4UvwxpjxWMzH7uw71ZPSXARdjuKjm9h/Q7IqunzEOkXYgOjcPnit7/9Le655x4UFBRg+vTp7He7du3CkSNH8Pbbb3/Vw1NQUFBQUPjiiHTCyZMnsXbtWgwaNAjz58/vFogrXJ0QhSxUsUyCsGHDtL+98grwwQdgKlvieZlNr0y28XLozEw0no1ASJELCc0Sad1TiXawgcl+zYLSKCC4FgNzCrBIkU7BuNQ4zVTZJgcmfHtcMU6Bl6hKEgNiWXEvekuL25TJc9FShSvSjU6M7K/Nx8CV74SCAu3PJpbcNCWkYr6rrxt9272oxBjsOZeBa/mYuG2KSJjYbAh7wobq/1qFwZ0a+WDXuGPTGJjv7y6LE6lubfz5+TZ842Ah+vQ5BERrNjvHCt04VqcnZ/RYnHm8k5hOPNd8zvX5dNpztWmRT2EwRZbsVU/ba2hAafQ0OF122EnER/YsRoGo282U6ESis1duhUDj0b1wN/7Jh+md1dgTeTPC40fjxy/Zke4Dnp4rnS/xWuFBuZAUiU6IxKxMn3YSYf4dCcZP0XySCFOc10vNaLNeBbDBMhXIMnGHUbi0UOu3ghHEpo7kj05NR6nB5K+3/Ronak5g0oBJ6BPVB4cePtTd6kFcv6OjkVB+DM0D+mFgZ1SXldXnGlyQNUxevzkRTO/TG0myjD6BFi/djqPHdVWfk8qGItwQ0oJY8vnlHvB6wjKAnJV6gvjvzfQqj5/fq8XKugsFrbP8GYTWbnroysyEa0YaHHEuTDrt9DfnZOf20zysO7gOJWe1db69fwsywoCws7XMFsZvB2NEUttssH1mgf0nT+KWkhogYSXsP8sJSnzSPqkRrL0MjKTPP5iP7a7tOFxchhu8FlYt0NjciHPNbazxqJ9Iz8lBP6u1y9JEVHIL1V6iIj6AwDW6Voxsceg4+bMSkegmVit+WCwYPHgYWkNDMKA9tKspq9AvoPX+TxBT34wRe47jZNnLrFHvbbZN3Z+3zCrLCgqQ0tKCFCoRq3kP2Bf8GdfMG53I9d2Vu3Hw7EFGuF8Qwd3LJDi7pnwOWH6wCCkN7s9NgCs7l6sDc+bMweHDh/HHP/4RB6mhFsAq4x588EGlSFdQUFBQuOxwQVE0lWT9+7//O2699VYcOHAAQ4YM+eJGpnBZgeKENWs0uwiKw9ragMZG7d8kvKJL5dw5LY7VIJFtgqycOFArkdb5Emnd2yaR8sAoMKIybwpQiM2nAEj2vuQBMQXmnGCnAIsGTD9ckd4bJRTB6UTR8w5sPmjB7AlZyHhMCqJlAtSIIDdqMioqnMyaTooNe4wURjIJS/PKm5sG+QidXzqX588DGyLsaIoH/lprR8QNNJ7gp4GdWmZhoiVC/L6iNluX/7WgDKMmoTSG6ZEOpHq1wSxaZENMCTCgCX6bnYzJPvxyfx5Gp1JwKpR/ix7d5GdK1ws/95JPPj8+Npxgiiwp6GVjSEqCp7IRrzXlojLLhly7RDKR5+tKh9bI9bYsTI3XLX3kpnzUEHJKBJprLBh762gs/iwXxyqA/URkn9V99smn9oUXjMckW73QPgw8+MXcUDA7W0p2cUX6pQZdsj/9KbB1K9DZSc2mtGtL4YuFWr8VzEAk+mt7X8Ore15FfXM9WjtaUddch/a6dkT3i0a/Pv2QFJNknijj67fTiWhqLk03Gvqib9yo3XOJ0CYFL6G3ilejtY0NVvidvH7zRC7dL6nKim50nEiHgR2HkWWFnpTeHFGEzs82YfzZSMTeOR9YsaI7CS5UAokVWN2I22C2IfyY+LyI67eJz3kACa83HCfkPTwG64+sx9y9GjnJz+2aA2uYz318/3i4GlzIah+CqHY3auNjETpzbnfbFLnqjwjbCYXAsfXsmczvcz/UZuh/TfuM3bge3oPN2FJzFJkLNXX+uLZk4Fw5MG4cfG0edMTV4sXiZO045Wcbfk4MnoO4Ij5t70p/Aogdr9H6beZ7XlGBs9Y4FDaU4LoZabCKzw7i+r12JSoPFWF8R19YJ9m168pg/Q6feSPqqwrwyfA2HI1pw5GYI7hNPLdGz3y8okNUKtBzqsE1ysjrYw7MiJuBBNn6RwAp1Ot99UyRfsEEdy+T4NSYeNOxTbh19K0BvRUUFHoCEea/+MUvvuphKCgoKCgofHlE+p133slKsKgsPLtHHw2Fqw282d/evRr3TQQZxYjV1cDw4Vp8NmfOBWzQSBnFy2CJFL+QgRF4gEKqcF5eLSrT+fu4xQy9ErFMDcvo3yTD5o0dpSDTEA4HwjcVoLEpC/8xLBcFtl40DZODGLnM2qjBGW9mSt6q9F5KAFCJJGdKjRRGfLv0Xq6MElhlOnQiW6lnKYPTiSUuByr62fFBpI0R6VsabWi/0YYdAhkdYINi5CErE9Hi3wiCMuwui0aiD8+04O31WfjjL+1I+Raw4u0cwKFvv7gYsyN3YPZkL+CL1Eh6DqPtCx73oiI+IPYOpsgSiRauhnS7MXy6Bd8564Td5dA896XrgBq5ViALB5blIhd5AU35SExYuNIJOxzImEd2QlkosdiRXAOMPOvEN9sdqC6tQpzZWGSIVi/idS1MMfFMlOQKRqSTCv2Clei9BE0dkeg0fQQ6lRcKfgq4NfNFWdBcRVDrt0IwEOH22x2/xZnzZxDXPw6JAxIRFhKGGcNnYN7YeWioaUD04GjkfZrXRVr20FeCga+3nBjsyV5FhFFD5WBVWwRaK+vqgD17NNUw3eyoNI5vR7LjCLbf8aNa8Oq4SEyIH6E1MDciwbkyXOrbYkjcyrZfIqEqrsni+i0Tw/x9otKfVwMsWoSdFS/iVP0p7KzYGXBut5zYwioM+rT3QXl9OY5el4pHMh/prngOVvUXbG0XE+EWC5YcdGHveBveb9+Gt0I2I/JUKzZlb9JtVg4zdf+4/laM6xwLHPZqzzFi5aCRT70gquBNZ13nXIxQZ0OhazLY+i2fu927EXGoGC3tR7H6mYeQKxLvwrVAiv2Tg5vReE06ZoUnatdxRASc9+ne/qe1fV87MhMNKafQmVyP/Otb8UDnFGx5MAtjBiTDKl7zRmOUny0M1m/a1/vH3kdUaxRmTphpfO0GUapfKjzw7gNYW7oWnZ2drDLgUlXCKEX6lYl9+/Zh8uTJCA0NZf8dDOlkyaWgoKCgoHClEent7e1sEbQKDYgUFDgoBiDP5Tvv1Eh08kOnmJUsvh98UKuypspoM4vpbpDV2SJMSELTgdlsWP+sE1WfOZA4yY65uTZNFbsKiNjvxI8mu3GbpSTQ15SCU9oHkfaJiZqRt9zYsYfxtxYBtS67P84NAA/ApaZhAeBBLScfglnK0LiIHSXZP008Z0qNCNfCQvj2HcLZknqtoatE0P/jH9pu6ZXB4YC1uACPZQDDrDYW61dvdCIn2gE4pfMjB4TBgkX5/HF/WLsdqQ5diR6RhWlluaiuB/auJVGgScApXhNSAy+/Tz33qiU1vOamIp+y3imyRKKkpQWjItwYbdUbhh7VSRP+PrsdoS4gAnb9cAOTCR7iTg4BHSgArNq18G6eVtGxIMqBNFcBPL4wxA0ID26TsFJSrfNuv+g+xVFRGk/zVYHm4cMPNa6LkjV0f7hQ0Kn/4x+BU6e0+w2p5+nQ5TyTgga1fisEA5FYCf0TcLbpLJIGJGHfD7sID1IcezwerDyyEgXHBNIyGOQ+FKK6/ALWUAZq1Fy2DxvWP4fE+YsxNysLz4XvwG/b8rFg0AKsyF7RZW1GiI3takYpbYeToKZrrvD+sekWzIh2Y5jYBFUmyWUCXQQngMmygB5+5PVb7MfC1/qdO7XKKb5+y6Sr/r66LRtxrq6SNRll/uazZrEKrFNbTqG9s52R6Ry208CfSsbAkTIGJ9LiscO1A3eEZpLLWNA5N1V2m63fPBEeGQmr1wtrVhZG3LgT5edakVx9JHD78rrN98eTDuJ+5ecgi8VvBSISsQFjNIL8t0ceQb+GekxsG4zpG2iRdmq/l5qG0xw3pgAj5uQAp7vsZhp//iTa24+jsc8W4HvL/Z7hzkTg7vEzYFn5AaL2VKB50DAgUqg+MBojq8DUqwJNrk12jJ1AetxXSzYSiU7XGCXaHpx6EYu34Jlf7a3G0ZqjKHIXMT93Rapfebj22mtRWVnJqijov6mJNSVhZNDv6TlFQUFBQUHhiiPSPyTmQ0EhCLgQTW5SyPtf8jjLSFhtuDGzP3LLDhlG/pg6Xtxhw/YqG7I3OjE3Ig8HXHZs3WrDIz4HBtYXAOGRaPB4UZSQhajlBdrHKbghRTr9Qy9bNwwgTcafscaGDPn3RqXZZpAJACNLGU5Y0PeT3kd2DVRKL6rNDYJfnxeob2lD5PlqYFBYQIA/bpxGcpKQjzZxr8+CFAqOp1qQy6ed2408XxTgpRqwHzOywszXnbYjy6Ttdiw4BoS9uQo/iMgHntXVhJwkN7oWeOUCD1wNAvFuw4E+7iCe4t1A26HrQ2gY6jrmQ/NHOxBCc6ZvnBrBdRXrB5IRCRY7Gt8rwUBX17j46T31oZ1VkX+zbV1XlYQMPt+8EZ44BwaqQd4/l1eG91bZLcb5n1fQTMPZtMn8UHpDhNN7fv1rjUQnm2+a/nvu0USovXF8utqg1m+FnvCTmT8xbF7IMTtlNrMF+bDsQ+a3TSSaqfLVyKIjGAlrdAMQPrPB8wFWDalAZMcOzM0twG+fj2ckXNmHa4CS0exL3+DchsND+yFx4VwM7Rev3fel7fRq/dbfT3f0VLMxypVkRuDrDu+HIa/fYj8Wbik3aVL39VtcR3XyvfL8GUS63TheWQqrcK+fbJmM+lP1mJww2U8yL9nggnVbMXJisoDv6dsys42TK5uCVWbJ4IlwIal9f6OPeaD/0p2qzZcZUSxWedE6J67RPOFQUwOUlWkiBH0R+lze2jYbwltaMKE2BqHbioEYB1NYkwKdJSj04wxYv4d22c1kfOZBckcnEkI92thzcxE1dDlrvElK+c2jyVJoGOaGpQLOkq4SLCPQ4qrb83R71tTnmo6TGrtSUotNmZBEoIbANM+L0xYHNG79IrBg4gJGptMr//5fqLL8v7b+F/PrDw8NRyc6cfDMQTy5+UlWNcGPVeHKwPHjx/02cvTfCgoKCgoKVwpUpzGFSwrREoLHApwn5XEWkXKHD2sVzGQh0msE86/u4e8PZTpxb5kD1/cn5VMx7kxw4ffDHDhQa4FrdBbORFvQXu/GBo8dKQ7941yRHkxJ1BPkgEhW2nMy2Sgg5/vjTU65IpkU1zLhQAExBZnEKtKY167VAngqtyYDasmD9ViVFREfrEN0UxXQFAfMn+8f20svaW8vWeXEuScc+CzJhZQxAjEtltCTXz0Fdjwg516xvcmWyL6wgke6ONckQke5Hmi+WdYVWIvl8qIiXZhjmt4DYiNQMzUkH4s87mDsLv2NuurSq94w9GhWHmKrvKjb4daIdLPPi2QNNVP1dM0vP3RyidldBWyuvQP9kmNgFUsbZP9gSswsW4YynwWnsvKYJU5KLxrZ8cMmSyYqZnj1VWMiXY7zvwgE+3rTpbFhg2YVpU81FizQLvMbb9S+EqdPa5e/goLChUO2hODk2OxRs5HcJ5k1Ltx8fDPzUfe2edHQ2tB7C4neJFiD3ACuSboG6XFxmpI6Lw8/Dr8Rv438BNlnEnHqk1fQ71obSq9JQH6iBym3WfHoVgSu3Zdq/Zb9roOB33s5OSxbwonPAuRzTmtqaKimqCdlOmUF+biF+31DnQexpyowsKEdMcebgB92bedXQzW7kWO1x3DnG3ciqm8UJoVOC7QW4WOiuaEbprhei+tJT9Ur8gMeh5Dcfho2jdil+aIFpL7euOk7HxtZnunJBX8i4KCLKdxNLf16WqPN/iZXHNrt2Pv35xHfVoezoVWsV4/p5+12RFNvXYn4D1DKD7SivM2HU4+8gEGnm9CXJ8LlhI5QCVl2Zyb+8mmeP/nhP/8GoLnhtjb0nSw/V85eZSKdq78pQXYpLF9W3LWC/ZiNxYgEf2PfG9hWug0zJs5A9rXZ6AzpZKpksvuI6BOBioYKnGs5h4nxEz9341KFrxdGjBhh+N8KCgoKCgqXOxTtoPCFQeZJueD4l7/UrCuITL8QrPfZUeUGEn12zDVTM5n4r86NILK1ABicADREIhFV+L61GPh+FvKQq4nEM4AUUYTFFcfB1N16kEO/IncNfqx+hbPYhExUxxlNklmQ/957WmBN24qLCyx/5tvgCjIeINPfuTyXIBH4mzJsaN9nwT935mP0g8Yy4zEnHbixvQDnqhOANGleKfikgH/ixC4PXNHPNpgNjXy+uC8s+dlyD0X5M5xE5v62Ph/qNu5AbUEpLN71iBjR1UjU38CLHE9Yn1Eb/hBvQ3KVEw+OdeA27k8vBsdySbvLxZqEVha5ML65mAXNvSFhEhbZ4dFfDc+vUWAulviTB+kDGkH8SooD6RUFeLspC99PK0BBdg+VDTYbI9FjtxfgFLKQUtAD0SMcNl0qdLrGjjV+n2DD+4UhmL0t5Vdo6ihvQ5USNHWjRwOTJ2uXD/UTHDoUmD49sFefgoLCxcFPjnUCOWNzurzUt/8WvjYfQjpDLmBjDlT+5TVs2P0qwh5/wljJKt0Hxc+S+jcjaz6w3cUqoZ6w2fBExqN4b3QRCtqciJiZiElzHmNqYEbElZV0VQv1AqZko3z/lm9SwYha0QZG9B0Xk76cjOc3VmooQzczWuOoTIirmIX7fdE1CShLrMbN7kSM/s6Dgc8UpzV7ko1HNyLlaA3mljdhKvWIFSu0xF4wcXoS/WLWb7nBudDjxHT9pqow3cKFEgKejkYkhEYhOjZBG6Ownq3cu5I1Tg1JtOG6cWH40HsQt02ejNvkG7x4jvR/l0X4cKp0ByY2RyPxuEFjWfH5jlcckg3F2kR0hMXC2pFofP7lc0slmPTcoqvkuX84qbVXWB7Aq79egg8TGujAMZXPgTxenpgpKMAnf34Ysb9bz/zlrT0kwjnhTK9UJUJq7rFx3Rdwuq63u7Qs+BflnS6OxQhv7X8LTXVNOLX/FCPSH53xKJ77+DnUeGvQ2NLIrGKoEe7y2cuVGv0KxsqVKxEfH4+55MMH4LHHHsOKFSuQmpqK/Px8RbQrKCgoKFxWUES6whcGHgNwJTrHt76lEYWkKO0RQqAq27Pw4JUIT2roODApErF9pYBRHoxB0zOx8WRArMUVS1wdJqu2CPoH6NfkdEIgPtuGXlq4GLGHcnBO+6P9U4BN29JVUGt3WHDs1w7cG1WAkT/QFXKPPKKppOmBlJhGHrxJBD5teqUrG/+DbOSkMsMRP3icd+cIC0KORyJllBCI87HJTS1JLc99aY3K2HUQSTzsDQcWxhRg/LWRgb6wZN7N50qeAwp0RbI/Lw/nKr04f7YFvjAggje1k+bx1iIHTjXb8dFhG3Y32rA/woZD7rye/V9dLnZNnWxOQ2N6FmYZke/0SteHMK+p2Tb24680EC0AxMkV9y01eAt704LvnnfD4bNg5JwsY599TjpJKsduRH4P4IfNrgc9EURDlzkHefq/CAQTjNI9hC77UaO6Lg/6WtDXi6ZA7k+noKDw+cBJMVKkcxDJ9fztzzNyLiIsAuNfGN87Owm7nZHoOztPYfovngMepUXHxkh0smeZXrUFCB/TnfDVP+t/5Tcpyqg5nZh6sw0HfrTMbynBSbgOqvLhinR5LTEgvwPIxtbUrr/L6/PFJMJl9beeCN+542183F6GmLkL8MCyFdoNltYMImbp+EQ7L2EcUUOBPXtXYg/x19emBq7devKDCNWcg/WYeyQEgyfVdG8uLmZGab+86i3I+s1J4lcOpOCek/27SHeeCOdrndFayRcQIYnu2fAm9kecw2QfEJ15d1cDWP0zyb97Hss21QG3Ag+lleNY7VmsG1SOQ/RHcfviOdLPR6fXjdj6KrgmpSKREjBGz19yxSE9u5HffIz+XGO0fvfgP0bzQ5ZD9LqiZDTmHAvBhtEjUfrfT2AqJ7HFhJFuCce3by8DOo4CoWMTe6x4EK/3X936K5Z4YEM87Qwgo7lVk5ll06VATxY7Cycv9CvSCalDUnH76NtRdb6KkelNLU3MKkqR6Fc2fvGLX+CP1NwGVKCynTU//5//+R+89957+PGPf4y1FBgqKCgoKChcJlBEusIXBh5zco90jhUrtB+KSW69FThyBLj/fmNLCTFQfSgTzJ5lUpsLp14pZk0cyb/SATtItzXMasG8DF3xZUR6SkRpsMaTAZ0ZqdyYK7goEOYBJFe7OZ1Y4nIgxGZHeaJNi4nIl4PeE6xBpDguk2Nmf7vjDs3TgkygKbjSFW7HCt3YX2PBtKZIjJSVd6QU//3vtcM1cY6huJmmgBH/gVXLDEtcbliLSSU2BrDqQbWohKYP8qapXCUvBuAGSjYi0Y96LXgLWXg6Uy+LJhkxzRN1wOQe6dzDlc+BHMPa7ThDTjAHLZg9wY2Mxwz253BgdnMBYtKBxGQba5D6YHLwygXRTmdg0VEk9I/HqDFCc1hhPEaBuH+Q5EXCGWmaD3lyxcBeUsP/IDYS55q9qEzNQsaa3O4++9DHIyjhOJpSbdg5y+ZPDvUW3LWAXIDouqDGwV8nUlr0dt+9W3NCoGuXknJfNMGvoHA1gpNjvNkoB6laiQgj65Baby3zZjYl0gVPcbJniSvx4Zozzdjy5yeZnzQR4ESik+IctjHGSWd5jaR1R7+/JkYlIvcGA8JR7Kos23bJiXCnE/9ZGI2VcamwEdn4Z73/B72PGjcHuxH2JhEuq7/1RHhryU4s8PiwtW4NQES6qJK+6SbUHS7GpxnRSCJiVJgD+n+u1ubnyUgZbCtcCbjWa0l4qtaicfBKNnqO4MQ2rbV0rD2s35Tw+G5JPRxxPtyT9aR2DmgN4g3O6Yc/nxgkF1j1gc8B+3122F53YEB7CIbWtmJAX12BLxHHi6sS0eGKRWhVIry3L8a+9a/iQVcycHhl8Co/alwY4UNd6Q4kzF8EZAnHKVeCmVUtmDWDl5/NxKa6TidLMjh8PtgjUgC7BUn3fgf309+uEcYneuNznzId526cCk/NUSTcKPWK6QF0DVASZc2BNax5p6jsli2bvgosTV+K25NuR3l7OfI+zWMe8sWeYmSNyTL+/ipckSgvL8cYuscA+Pvf/45vfvObeOCBBzBz5kzMoobJCgoKCgoKlxEUka7wlYFikq1bgeZmzZtZEiR1C1TnsoC8AEUtaShAFiJgZ02gQqba8PejNiyaR1GDMQnrhx50rf2nVYjbdRTt8GE2TJhmDiKxxSBTIOH5gViLC/BYFgWo+jYcerAkBq8ikS+Wd8v75Qo2XtbNm2vSqzAnMePsmLzWgRFRgopPUkkbCuacTjQ+6UDccTswSif+BVStd2LImw6cuNnSVV4sj5GXgnPFfbDjEZIXC2OKGYlesTQXoKoCmhs6LkoS8A6Y0nnnCDiWXM2epsADdGQAGeIuBdVbdEIkZmX6MIv2dbdOoOzQSX+jygUOt5tVN9wYvgNgyQSDygKRrJEHyf1c+bXCT0Aw2bWu6k+bk6z52y+yaB7vK52wwwFrjvE1KvI2vRFImoE+v26d5q5Dws8vikjvZoMk7IdiKbonzJwJbNli/Hk6RppSulx60/dXQUHh0oJIO/LfbutoQ3JMMmb93yzsqtiFO0bfgb8t/JvwRv2GFBmJDK8XGWl3Y8voo5qXeZmDkWgl8xehCPlImDMVqWakp7x+P/5PiCuuRHtjMWYbvVfMvJG6OzISu3avR+v6coQPTca06PFdBKpoHcMIx8Ku7Zip2Y3sWTief17rpsz7k5AdGfUvoYS4kAgfHhYHhFZhfNy4rs/y5Oz48XjlvhSmLs8qc3SR5fr+kyOk5tI6Ss6UYMuJLbBEW2DjJK/sRy5WfIme6D00T/1B/Tgkle1B5bBU7TjI1oTAq8H4Omji0RXgo2234+DxLdjWDszoMwpJBopv64SpwJ6jQL94PL2T7NzuBg6TkKGqx0R4itOJFF8EcEZ/HjSoIjRMhPPrVVTbm6nfhf0x5OUxpf49kTcCVbrP+312HNiwEva1KzW1O71XUKTLTTrfjXajINOLrGh390a3PYDOOdmknG89z7b5Ram7zWyQnt3yLH6/8/fMCuqRzEcMk2ubyzaj4FgB0hLSGImu/NCvLkRFRaG6uhrDhw/HBx98gH/7t39jv4+IiIBXtKtUUFBQUFC4DKCIdIWvDBSLfPghsH+/Fsv89KeazzE5k/hhQDwO8lkwfocbCVO7C3z8GxZfDXDsEzfivF702UWl0wZEN32WPD9JcXX+PAt8Pm2eih+8kY3RtU58c5ADUyx2LdgJpjIWg1gxoBN9zuXAlSvYeFm37B2rz8kD9N/0fw5hf1JwyH99l4XIiS62NcNTAIwCopbbuu2+6k0Hxh0rwGFk4YYVud2JDV2xzaTLcgM1Iwgq9vH/moWn2ZwIZdMUrD7zDDBjRte2DM67PM2mp3mlrigkuxhRicfJAqlB2AU1ihPnQiRrzD6XrJPickBO80cdPKm0ns6VTjaxcdL79Qua8jGx6x3oQAF1S+2aE4EkaXwyD+3HLWjc4sZdZOmS1T05QqBdvvUWsHQpsHBh979z8XxlJb5QdLNBEk4zkejUP4FezSDZ/SsoKHzJ4AQYKUvJn/lQNTPcwLuH35XeKK2DujWJ38ucPiOTh73IBh7b/4m2fu/YBRxH9/cSiU2dzInc1at3KqJc2JHShpGNpzCxbhCi+QODvJCIKmN5LEJiwHT95lZsvD8JVbS1tmqvwn6G63MyXLxZCyppe4pGpbJ5kojvxTPS0DlHs7QRQQSnd8cnaHu3DHh0tUZ402fpWIUmoIwEp5swjT0YgS48D93z8EtAqp5EoLVMtHdrIB9w4TwYbCvAR3uojVUk9KGEjP5vQ/9wUYzA129ZyGAE+Tz1NhEuWwDyfYtNyGlOaTGlsijes4V+z5/T+JpvsTBCO3bjembXwixjaF6EB9YDGwrZ3yvHFwHRGbgr3QKYEMxEYL9V/BaWjl6KhQndF3B3g5sltxIGJHyhBLWZ5zpVptT4avz/bUSkz06ZDYToFRPKxuWqw2233Ybvfe97uO6663D48GHMmTOH/f7AgQMYOXLkVz08BQUFBQWFC4Ii0hW+MlBMQcItsmf4y1+0350+3cMHSG2Ul4cUbwHA4iibMVncg7/k6BstwMZItE/LBLL0QEgO5HhQS0S614vDH7lRegw40GHDx0025FJsKoyLQSTixTFwlbys4BZsaFaV2Bi3+kTyItxAn+E+pt0yBd3nxA+pXJmanjK/9vd0qxEKCHNyWPNM5vtNvi8IDKITF9tx+E3tNYCYps/Kvt7Bglkz5pMH8XPnwpnzAmLvm4PI+Cbg1KmuQN/Ai5QdKm/iCjsrdw9KpPbvD4SHdwW7fM6J0Ohp7HKjOKH6wH99TJnS9X5RqUjg++CkuFyZQCd6uxaMsvMsBuBCcE4zd8Blx5kq4B8uOyZx/3JhfJQUSe6MRILHi2i6JnlVhATaJdnRUz87IyJd5pBErv9S2afwS5CGTv1qZcKflOhckW6GYKJ+BQWFL8/2hRS17xx6x//70JBQZt3AibJnG9fjzf5vYvHwxXg6+2l2AyBbD1Ikc/K0W6NCux2uehcccS5MkvyeOUZPvhE4shHtmdOACQYe1uT/RGQ2JcJ19LVY8ZuZ5bj5zABMbE3ALDPfc6MbjOyZLa3f65N9eLFjh6bSffDBrhvnhd7AaPykJKB5ys6GjZ6HXu+uHLdaLMjd6gaoSG1o18dp/0Sikzc3mweR/Of7FkE34WDPSuJneRNv/iyTlcUU152P/AhJTdHaXBO5buIjLvtoG/pqy6IB+m+RrBaT+F9EIlwWCvA1WW6+ytdvqjTg46BzRHNQX68l8N1u2G+1Y/MNRdgT5UJjuqWb8IKU6kSyDzzrAjo9SEUWUk3OBxHYO107MbzPcCyc3n0BD7D1+YJIavq+R/eLRmp8ajfPdeqVwBXp9N9GyLBkYOqwC7OtUbhy8Ic//AH/8R//wSxe3n77bcSR3RXLexZh0RfZyV5BQUFBQeELgCLSFb5ycOEWISamq2KYSD3iW8n2ZexY4Fe/0uMyKdjyx6V5XUEfo4/Ne0JhgbUQSDoEJBClnAGUlLCmpXurLLg2LQtWrkYjdTcFm1OmoO2YHfH1Ws8wEk8bCt7NglaL5JnO1WGCDU3+FhuLzZ5DNgoKss2DSxFy0CqXK8tWIyJk3299W3Pn2oGne/CtDDambsMyIQ6qqpiaelv1dKTGuVGWPAM39jSPst+tUQM5CpopCCYVIAW21FiWgmZ+vD01gA12nGYyeDMFnFFZPUFs9saTEuXlaPB4UbQjAlHLc7UkiCMPthw78hy5GnehcyOyfU303QLZYHI+aBrIOv+mm8wPVzxVJLij65F4AZo6IyuWCwUNWbSelYsdzOxcFBQUvn4gwm66dToqSirQgQ5EhkUyMo2U6vS3V/e8ivJz5eyVKVQN7ut+QpVuBq9rN4PVc6zdbU0ELIjJBBJrNRI9NxclBavgefBJTGyORuJxj3bvHzw4IDt4Ns6F9PZtCE9JQNTs5V0qaA4jAlhMqOrksVGCvCrRje3XaXYr2dkFgZlHvh7x/h/B9idDthrhhLCJfR1TCFMjV75ds7XabEzcy5zbjRitd3x7Ph8OPvod9D9yGFFDDOzNpLEFHLOZFZycoOfWb+Jiwf3cL1UinKoXqPpQFFPwffBEuNx8la/fgvq86mAR+vrq0BGXgrgbbmPvZd7lGRn4bYzH0K7Fb18jJguCENgT4yfippHGC7icmOAWLJnWTNYY+FIQ7HRdeM57MH/CfHatidcKfb97bDqscFUjNjaWNRiV8eyzz34l41FQUFBQUPg8UES6wlcOEnCRCIsIOyLSqXF7v36aMvbNN7U4hapsucDKVA0tBIy99ormJbyRkeg45EUFsnBgWa5mdd7Z2VX6O3UqavOACROA76Q5kWPVVNGs9ZcYEJsRrXzMcqkwV7jZ7WSJzdBNmCEfbzBTbF6uTKypqKATlVliw1CRUDYjqUWZstmYJMibCoib+faKipC57xWcapuHZ0OeQXR5AiPSSQzmXGdHDlmvyvPIzzH53ooqeaOScNmnvBc+sN3Aj5OYZNkTl7roBrEwMC2V58wxHy8dsK68IxJ923ELZjyZB4zp8nW123X1pr07cR9AvgfhY2gK7r5bu0R6A34d0qVkZsVyoZC/HvJp64lXUlBQ+HrhsZmPIXFAIopOF+FIzRF427yoOl/FlOnUDJSIN3qlf98b4UOKWQJWqHyy/yzHr66VfaRlMpfup0271iH2QAnKJ6UikbzOud0Ird06qUrq9m+VWXFXgwWppPJmy7fUgFz20eaQ9heQIM3KQmKyD9frivRuMFu/jfZ3332aJY28fvM1jK9DwnODaVWU2f7Nfid5mVMyxDHQypqDst/zSgPhWSa17CR2DO5A3aSBGL/0PkbeOpvXIecaa/e1m23ExBpHSAx3U6TzOTfyc+8JRudN/iyR6OIi1JveL5TM4AmNw4fZe96fmQhfYyyGjR8MatdD4gw6pm52LcKiR5UXHZWHENo8BtanX+iRwL57/N2YPWr2BVmwkG9+n9A+/uTW54FcPRLge6+sWhR6gU8++QSvvPIKysrK8Je//AXDhg3D66+/jlGjRuGGG1gtroKCgoKCwmUBRaQrfGUQ+eC339YUsMSPkuI7JUWLm26+WYuBSBRO/6bP2MyCWq4iKizEEriRlGbHJJ2A7Cb+4mSu4GU9sL4cw6wWZHBL0s0ZmDEjGQkZCX7rmOmRDkys0gM6zhKLATEvf5YhE61is04aB+uTugrZyAeOZwJ5QimzrCgT2UeZmeTlykRKiAo6UZnFtyf7m/Pf8zJm2RfU6HjkYNbpZMr+kCo70tK0eRDjZrZJK9nx2GBzPYyIfsA4K0AVntddp22CVU2X2HAkxoYCeSp5EM9JchqrWYBspAa/mA6cBH6+5MCfShNILSir7mWpdTDyhB9ThEaGE4nOPOzHdBEG3UT9wvVUlO9GvseOFJ5oMoD4laHvmp4bCgrOFYjNQcU+dRcznfz9zJ1Huozod9Sbj1Tpy5crMl1B4esKmdzmNi/Pb30epWdLcfDMQeyr2ofBEYPxvSnfY58hwu3Wj1uAQ5UaISzbeNG9nFBVFWD/QgR8N7JOSkyPiu6PuphEhNx0B5z32uE45sCMuBlImJDhHzMfp+vxh3GqYD1C612wiknYYAStnAjnC5q+vs5dtQpz83eg7M7jyGvosrZhMFu/pf2xOfVtxoxbZiDh3W2B6ze3GuFrs6yMN+oNwg7aoEGm+N8Gx0pNK6mygJIhxR59rQLw2t7XWGXBb5IXY66enI+qPoITY5tgmXcre/bIf/MpbO9bgiNTY1BgdAOXfcRlkpzPq6hIlz3SLyYRzrchzhtPhPPMstGzA68c5Ou5/CwhnNtJt9rhmGDF3A36s4J+LN3sWoS5d+xdCd8YICIF0FJHxqBz0tLWgt2Vu7XrWrSjMQFP6rR2tKKstgyXAvyapu++SKjT+ERLJwUFI5Cdy3333YclS5Zg9+7daG5uZr+vr6/HL37xC2zYsOGrHqKCgoKCgkKvoYh0ha8Msv0mKWCfew4ICdHsTUmZPmKERvzx9zJVOkyCWqEZlbW4ADlZXcxdN4W6XPpbXo7Yvl7My3AzkTn9+v33NXE392tOdTuQ6i1AVXQatkRmMUI9sVgou6agyqx02Yho5cQqH9iWLWj7ZDvObi9DwwALzhUBmzJsWOJywyoGl7LnuL5d2uSBVUXIiNuFhFGCl2teHl4tsKB8VxbCBtnxRKajmxqNPtuY70YG+WyTGk4k1P2TJoDvVyaMHQ50rC9ADAn6l2n+9Xw6Ogud6LPFgb/U2OFy2WDTy8vPDLRh8ACtsllUQlO1s7jpgGPnCRCCUcl9bwLt3jDCsmeqSIbwC4QGzkvDRabYqEmdEVkjsNxk5YJMOjaDRIfRuABEpYKR6AY5Df9uyXecqj3ou0U8S2OjOZEuT4t86XKLe9Ey/0JgZLvLQSR6QPWJgoLC1w5mStSG5gZUNlSiqbUJ4X3C2e+spGpOsbPPWAcWAdC7GcvJRbo3LlvWLWnZzUPdYB2Ii4xGXGQb4IvAX8oceP/Y+4hqjcLMCUKzBX1/e0OrUDEGGBZaBSstMFwFzW3XjCAnwuUeJ+vWMQWy11OCbafaMKzyDRy4ZRYmzcnpUnDr621JugWe45FImDMVqVld93ean+Ob/oIpnceBMTP863fRj76Fg585MK7Ch/hRkzAoJAyfNhQhSfCPJxI+5HQRJrU1I4L3BhEbXMuku5ndmtOJ4X/Ox/BED/pOH8M8rfm871r3ElL3ubFx5kbMfWob23ZEawdG+/qjrU/fAPKWXg0rCQQbM0OSXLZAE+f88yTC5QbjYiKcHvDExVC08uH/NlPS641s6dWWmgobNcnuF9/d291k7icNBSPf5eag4tytP7weL+x6AW0dbUyVvi9uX+B1LZ9C4bMFSwsC/i1avjA/f6FZ6MV893NvyGXnlkj0NQfWYMuJLVg+e7ki0xUM8Z//+Z94+eWXkZ2djbeo872OmTNnsr8pKCgoKChcTlBEusJXBlkURbES8ZLES1dXazwuEeqi3af2GiSopVcKiiQrEq4mT7BIdixy8zD9M/yj6emCh6b+3sIiCyoOudEnJR6JFDAReKBMP8FKl8WxEih45yT8okU4WQa8U5WJFm8EKlx27PMAIQl2TIsEhvssrNFqicWOd5HLjWUYSlY5se85B4ae2Y2Ia5qA6lHA73/vZz3bTs/FM80vIO4T4IkndGU4b14JJxqfdDBLEYzKwqxEvYycE+rBmnpJhDGN7UgSUG+1yzw/4HbgSE0BKkiZTiPXg+prx7pxajwwK7oIyNuMbLsd2QW2wFiWNxnlpABPgMjepYTeSqZ74/8jkj1GyQy6QMTScL4d+eKWkx/BVHOktq+s7Or2KTdbQ+84EX54n34KtLcDdXVak1HueGAE2iVPZgWblp4Q7BTQ7z78EHjpJY3feFq3VaX3kRJd/L4rKCh8/WBEbnP7if7h/dHc3ozw0HCmSCdLCU66IcIJTJBsyWSlsXjzoPvCaWgEpdhYU06EC+uAnd7TCaTHpRsS9tfOSMOBHy3rphzuZrsWbP2Qe5zQ80ZMDIpSWzHj8G6kHj6Jwpb1WDkUOBAH2GeksQah9P7COBdey/T6PbM50Umq3mmeBIxsrAcGuP3rXPgHm1A7sBZrR4Ygs7MK19X1QcWhShwQ/OPp82WTm7FoSDpmhY/REhMbN2rrSLCMp3Sjdq1diWFb92GObQSGLcsJIEQfb5mO8FOb0DpBq6Ari/DBG9qOyOTRGJWUzp6Tsje7kW1fDlxjUkkgE9vy2kbj4WtssKasBmMPCrnBOE+E0zi2beu+DbP124jo55DJdtonXRe6xYvROA0brgpkdZG7iDXzpe/TgPABmDNmDtJprk1AJPlzHz+HEH0B59sX98EtXwhGRLphAkTAsdpj2O/Zj3Fx47qmK8XOSHT6/tNnFZGuYIRDhw7hJoMmPTExMaijB1QFBQUFBYXLCIpIV/jKIMdJYszyyCPaf5NNOcU6gXGISYAlNARbZclF/pPAQ5lOzI1wINXlQqq3GCRmZ5/nQY9RibQ+NupFRXwyVm4OeO+1D+chHQUY2BQJ9DUhc2UFlBxkya8UnOXmojY1G2W6jcad8U4s2JGHXbDjGW8ulryehzMVBdg3DCiw2gII5qZ1LqRXFCOibyv69dObrAm4bmgVnqnMQ9iN2iSudti6mlfCwaxEogenIXGM1U/uV9cA58u8CC10w2omXBIDTCIJXHa81jcXWRkGp8hux763gQ8P25FCFZ12UqYDx0t9uKP2GaS01HaV95MSUYxhjUzXzcq8zQhyOfA2Km+XEx6yLytXINJnHn1Uu0C4lF7cjnxxm1UkiL76fJ+UuCEC5MgRrXFAWVnXddRLQkQczqlTwIkTwPjxwNKl2pCNKsNpE0Sik1iQKu/NyGwjy3wZPZ2CPXuAmhrgj3/sItKNpklBQeHrByMCULR5KKwoZNYg5AH9YdmHOFpzVFOqGn3BRRI1Lw/rk314cdgOPHTYh7lmdlhyIly479H/T0maAg9bvLtbqRChnbvV3V05LN5/5ZIbef3m6xBXs8+bx4j11NNOdG5Yic7iKgyLBpL+UoQNw5vx+oRBuOOv+bj3VBTsc+5C5Zws5tVOx3sgzoWy8m2YXpWAmyyZ8IiJdosFgwcPA2LDkBAeijjbHQht6sesQPxJDKcTSza44EiZgahlOcCmEtTtL0JtiwcWUqgHO5HSjdqRAvgm9EPEzAzMl85vUWos6o+HIiY1ltqz41TpDsSer0dyUzjGDh4LvLsyYFsByRbZv50T27yRqMl4gq5xvUmEm1WCiYQ+EeryNnqzftNzgIFow//sx/u4yFY7RpCOjc/duoPrmBI9BCGsaej/ZP1P13VtACLJKxoqMCx6WDeVO4dYNXAh1SacYCdiv9pbzRToD2Q84L8X0PdbVL4rKMhISkrC0aNHMXLkyIDff/rpp0ghP08FBQUFBYXLCIpIV/jaQIxVqAEpqdJJWEPCKoqjiXAjEdiKFSYbEPwqmc/2duBe8nK0BAmkgrGBFFBT4y9RtZ6XB+tUC2CVGniKFhyiUk5WMPNg3UiRJcdreWTBUsB4+s4MG9b91o6yZqC40d51KHqgOKYtDO0RbrTeNAuwp6M0ehreywOWNMfDGh6OqTEnMJVIdyupAyWSGnZEk706Iyy6yILjLWNQhAxEwG7o36nFfrrfOVmSFBTgzgQXRojKf+m9L1XZUNEIlH8EYIVG6N+UPwd9xzUBIa1aN0xxLrgSXWy6FiQBYnhuzfzJjUgdmTCRS86plJtvgzItBEqi0DUg29wEg5HPvejHykka3b+fsdsXUBkgHiIXf/ak8qb30PeNdke9CahnAdnsyO4yvSG7zb5e3Ma3rU3bF1WgKCgoXFnkOildSZF8rOYY3OfcOFJ9hJFvu76/y/xGot/DqhLd2H5dFe7dUwZUWYztsHpIhPvXbiq9kddefg/n93iZzKUqLhlGyU6RJNXXeTYH39OPKS8PDQePIaZfAnJiS9BgqWWkaJ/EKrjOAQM/dQHbiplafdKhfkgr3Ackn4M3ZTg+2v4Wom9NZbYw1vA4PNRhBeq8QPho4Be5geuxwwHrtmLkNKQB1do6d66uEnvimnHSlo5ZlPk0ABGjROKLavkbI+JRHJWEQ+er2N+59z0RpC81bUSFrQ6jWz7CAwAS5i8CUbpj+icDzzzTrU9JQCWB2boqLxDBnsvkNa43iXA50SKu0ZQIJ88z/vx1IQp3s+tPfPbjfVy41c4FrN/8u0RJKRQC1hgra+obDHSeovtFIz0hHQ9OfdBUFU7fzWCWLoZWSjrBTuR5a3srQhHKyH1RfW6mrldQ4Pj+97+PH/3oR3j11VdZ1cTp06exfft2/OQnP8GT9MCpoKCgoKBwGUER6QpfSxB5R7wixRdkA0GezoQ33ghCpAtepNxnOzHTrgVyRoGUGRHL8frrwPHjwCjdJkVQvHdr4ClDDvKovJcMoGlg4mfFIF4K5MgmxRMJJEy1IzebOFUbfrfRhonDtE1RXMYasgGIJY/Wtip0+MrhuedZvLcygQ31m+4dmsqbSNk+fUw4DMHvXAhC+1vsqHN3ke7yGIkY98d++psSi4qQeGg9qt5zIU//rCgeGztWO+TFi3UlncuBsuumoyXJDdw2oztrKwes3Ktc9H8182+XtyEH70aBM0/G8AQH7Ycmm2xuSAlmRABw6x8j5aQZLBbUtUTi0yILrnU5mKd/wNjFz9NxHj5sfJ31QEBwdxi67GiTvL9asE3QFDz2GHDmDFBRYW7TzmE0jT2R7aSOJ7E9uw4UFBSuOHAi7onNT6ADHdjr3hv8A/q9d1zazbjeWo7E4ZlAuWRpZaRENwLdj8m7mh4ceCMGvn7ze7hcNRas5MaoUQSB3idXo4kk9S0zMGtBDqYcfQ4bm86gMX0YvG1O4AhgS5mLnJgsWO12WJ+vAhoPoqN/fxwdFYO3Q45gFJGUNEbe9JLPUU8NsdPSEJo1Fw0pwPA5Oaxha8Dc6fPJlMdhxUwdzxT6BQVIiYxEWGUlKhsr4cjQSFKuUB4bNxYRYRFYnKbdtFOHpCJ11Cx0vPMOPEQUU+mT2CzQKFnM59zMj95o4ejtORf3KTWD9T/3iWs0T4Qbjbc363dIC4oPfYhRj7tgXZDT3dZNrmq7iPWbEBMRg3nj5rFz0RFkAafzRImrfn36MYuXlwtfZoT6hfqgmxHi3L7lfOt5pniP6hulEf0KCr3ET3/6U3YN2+12NDU1MZuXfv36MSL9YaMEpoKCgoKCwtcYXymR/swzz+DZZ58N+N348eNx8OBB08/85S9/YZnrEydOYOzYscjLy8OcOXP8f1+7di1rZlJUVISamhrs2bMH1157bcA2VqxYgTfffJN1DW9oaEBtbS1iY2MD3kOlZydPngz43S9/+Uv2IMCxb98+/Mu//AsKCwsxZMgQ9iDwGDFQCpcEPKbiVdSE0NAgol9BkU7kn0YAss6iXe8xsgjpqZEVEdG0UzmQCxL8rCqxIX+LDYssQDZtlkvkiT2UlVLy2PSDf9dtQ4HXhiyKowEkVznxaIcDW4/Zsf2Yrvwr0CeJAlSS8E+fzn4/z1KE6ZGb0e9mIiNg3vhKDrCF5ALtUw/fu0uKXS7YcwQLEX6y9IdhFtN7jMVjouKeCOShWVnw5DzTzW+ESOCq1RYs6oyElRPn3Kuc+9nyHRjBzAff4Fj82+DJGJ7goM/ReRs+HBg2LLD6gAe1lHB57z1tG7L9i9nY3G6cq/SiotKN6rl2rTEuHSN9XpaB96Z6woS55pcdoSdCXLQdJh91slWin55wIfwD56g4t8BdcRQuL6i1W6G3hNzT/3ga7Z3tCA0JDe6/rN97bwgfjYKlJtlyMSkazH+a7sGUBJcbgIvqZD2Z7TpYiNWfuv1jWhVegvxhW7Ao3IJs/uwg96sQLT3kBL1MUtts8B30YZo7FN/a3YjGm6ajfGwiJl0rkNwcgwcjYuESpHxYi/H/uw4N+xoQze3O5PVLhjAmq83WvYpMulEHKI/5fdjnw8At9Rg23ooMSZnc7Zzx7dE+Bw3SGnfqoPNc4vkQGQ3HERnhQ4rsaX8h6zdfLGT1t9HCY5QIp8+ZJcL37euydunNOiuu33WVCC+sQ0dYGRCj+cYbPUfJnv8XtH734GduRHTT3Nf56pjilz4f7HM9+aEHDFGwb6G+B8WeYrgbglyPCgoS6Jp84okn8OijjzKLl8bGRqSmpiIqKgperxeRvOeUgoKCgoLCZYCvXJE+adIkbNq0yf/vsDDzIW3btg2LFi1iQfG8efNYQP2Nb3yDBdWTJ09m7zl//jxuuOEGfOtb32JlZEagTPidd97Jfn72s5+Z7u/nP/95wDai6WFcx7lz53D77bfj1ltvZcF/cXEx7r//fhbUP/AAFb4qXCoQ+UZ2k2QZTYpmkQcPiJ85CUql2zI5LgfTXL0lNwUV3vrNOBsGUHnusWPAK68Ac+cCL7xgGPzIqlxOYI6tdyLb7dD8Msjrmsp8zUrSpUBOjutyrA6EHyxA+hjgz3E2TXXPd0yeo3RsOjM50LkZ0YcKgDFZXcGmmYL4QphQs9iPb1QP5gf5LHhmR57f4oW/l/gIv2UIP7DZs7tv3OlE23MODDzuQk2UF1YeeKelwZWWhb1VFlw7xs3UfL0OwAO6l9q0BAk1OOJEhZSMYaCAnM4bkfxm1QcciYnG+zEpOQ+dm8VscyZRQoKT8CLrLfvKiqQRoRdl6Lwyg7+aQbZsJ5E9TR9ZLAV7r1mFvRn4ddAbbkHh6w21div0BtnXZmNt6VosmLggoIliQ3MD82n2E33yvdcIsvpaWr85Mbj4YAXCae0WG4DLa66+LUecK8AT2pC85As671fRA6kr22PQcba9W4Y5J0OQ5MsA5ugq6dd18p3WDkoIJSYyv/G2gw0YtL0E5UOHI/X66/3EcEm6Be9+mhdIfPZkc8MhzW+A8vi0U3s9exaxnX0xLzrDT/KL7yOlM80PO2/i+k3PN0IinM4BDu9GQ30TzpTuQIp+w6cGpacmRGL4xEyk+EwS+/KcGvUn4eIBbpPHQc9+VIlAfnj0rNZTIpwSAHxOxMVJToQbrd9Zc3E2tArWjkTtvUaJ+Z76o/TwvNWTn7kR0f3IhkdQerYUA/sN7PY5mTg380MPtg/R7kf5oStcDPr27csIdEJzczN+85vf4Pnnn0cl9QZSUFBQUFC4TPCVE+kUfFMDkt7gd7/7HQugKZtNWL58OT788EO8+OKLLCAm3HfffeyVVG9m+Nd//Vf2uoUsG4KAgm+zsa1evRotLS3M640eCohU2Lt3L3sgUMH4pQXFGpyv4TGIUT8wf6BIxKhssSEr2bh6S1SkI/Ct0/q7MTGphdgb44EJwVVjvhtlHjuSaDwOB55ItuBwmRvX96eAX/djpX1NnBhYNi4HVUJgJcdfGY/Z4UoEEkp9yK/PQiwWAQ53YLCpk9KbMRsUXg6rssAqBYXdePNe2IL41cxy2bsJaZ2Sl4cUb4HW3JVidP0Y8/Nt8H7iRBsF2qt15RYFtHIDLYcDc0IKsNOShtbpWcCkrpLwozuAT/YCpa1AajwwV47/+AFSYE/zztXsvDEc/zdBJy+M7IEYeBO6wYM1mTYnefhxkwovLk7zQOXl6kbzycdE5A4FC3PnwvrCC0w1yOP2ezMXgbVbItJELEMXGtNWHvdiw6vANdeANYg1zih1gWKVWbO6nAHMIF4TdErWrOnde82s5nvCxXxG4esFtXYr9AYr7lrBfghEwBHeKH6D+abX++q7yGpOglJ1j5gIN1ojZWKTk+I6MZgamoipfQd13a+DEPQ3no/HiNJIJCRaupOXfD88EU7NI0aP7r7+yf7gkj0GHWPJt4GD6/LR5jkGq6iSFtdVff1OuGshznR2MsIZnHCmKrVP87oTn2brt0h60xzLa1tvSGupYi8gybC0oMsvTFy/9eanm8dNQVV8E8bOX+********************************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", "text/plain": ["<Figure size 1500x500 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "=== ALIGNMENT SUMMARY ===\n", "Original drone points: 13,848\n", "IFC points: 14,460\n", "Aligned drone points: 13,848\n", "\n", "GCP Quality Metrics:\n", "  Number of GCP pairs: 5\n", "  Mean residual: 4.200m\n", "  Max residual: 7.204m\n", "  RMS residual: 4.524m\n"]}], "source": ["def visualize_gcp_alignment(drone_points, drone_aligned, ifc_points, gcp_pairs=None, sample_size=5000):\n", "    \"\"\"Visualize the GCP alignment results\"\"\"\n", "    import matplotlib.pyplot as plt\n", "    from mpl_toolkits.mplot3d import Axes3D\n", "    \n", "    # Sample points for visualization\n", "    if len(drone_points) > sample_size:\n", "        indices = np.random.choice(len(drone_points), sample_size, replace=False)\n", "        drone_sample = drone_points[indices]\n", "        drone_aligned_sample = drone_aligned[indices]\n", "    else:\n", "        drone_sample = drone_points\n", "        drone_aligned_sample = drone_aligned\n", "    \n", "    if len(ifc_points) > sample_size:\n", "        indices = np.random.choice(len(ifc_points), sample_size, replace=False)\n", "        ifc_sample = ifc_points[indices]\n", "    else:\n", "        ifc_sample = ifc_points\n", "    \n", "    # Create visualization\n", "    fig = plt.figure(figsize=(15, 5))\n", "    \n", "    # Before alignment (XY view)\n", "    ax1 = fig.add_subplot(131)\n", "    ax1.scatter(drone_sample[:, 0], drone_sample[:, 1], c='blue', alpha=0.5, s=1, label='Drone (Original)')\n", "    ax1.scatter(ifc_sample[:, 0], ifc_sample[:, 1], c='red', alpha=0.5, s=1, label='IFC')\n", "    ax1.set_xlabel('X (m)')\n", "    ax1.set_ylabel('Y (m)')\n", "    ax1.set_title('Before GCP Alignment (XY)')\n", "    ax1.legend()\n", "    ax1.grid(True, alpha=0.3)\n", "    \n", "    # After alignment (XY view)\n", "    ax2 = fig.add_subplot(132)\n", "    ax2.scatter(drone_aligned_sample[:, 0], drone_aligned_sample[:, 1], c='green', alpha=0.5, s=1, label='Drone (GCP Aligned)')\n", "    ax2.scatter(ifc_sample[:, 0], ifc_sample[:, 1], c='red', alpha=0.5, s=1, label='IFC')\n", "    ax2.set_xlabel('X (m)')\n", "    ax2.set_ylabel('Y (m)')\n", "    ax2.set_title('After GCP Alignment (XY)')\n", "    ax2.legend()\n", "    ax2.grid(True, alpha=0.3)\n", "    \n", "    # GCP residuals\n", "    ax3 = fig.add_subplot(133)\n", "    if gcp_pairs is not None and len(gcp_pairs) > 0:\n", "        residuals = gcp_pairs\n", "        ax3.bar(range(len(residuals)), residuals, color='orange', alpha=0.7)\n", "        ax3.set_xlabel('GCP Pair Index')\n", "        ax3.set_ylabel('Residual (m)')\n", "        ax3.set_title('GCP Alignment Residuals')\n", "        ax3.grid(True, alpha=0.3)\n", "        \n", "        # Add statistics\n", "        mean_residual = np.mean(residuals)\n", "        max_residual = np.max(residuals)\n", "        ax3.axhline(mean_residual, color='red', linestyle='--', label=f'Mean: {mean_residual:.2f}m')\n", "        ax3.legend()\n", "    else:\n", "        ax3.text(0.5, 0.5, 'No GCP residuals available', ha='center', va='center', transform=ax3.transAxes)\n", "        ax3.set_title('GCP Residuals')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    # Print summary statistics\n", "    print(f\"\\n=== ALIGNMENT SUMMARY ===\")\n", "    print(f\"Original drone points: {len(drone_points):,}\")\n", "    print(f\"IFC points: {len(ifc_points):,}\")\n", "    print(f\"Aligned drone points: {len(drone_aligned):,}\")\n", "    \n", "    if gcp_pairs is not None and len(gcp_pairs) > 0:\n", "        print(f\"\\nGCP Quality Metrics:\")\n", "        print(f\"  Number of GCP pairs: {len(gcp_pairs)}\")\n", "        print(f\"  Mean residual: {np.mean(gcp_pairs):.3f}m\")\n", "        print(f\"  Max residual: {np.max(gcp_pairs):.3f}m\")\n", "        print(f\"  RMS residual: {np.sqrt(np.mean(np.array(gcp_pairs)**2)):.3f}m\")\n", "\n", "# Create visualization if alignment was successful\n", "if 'transform_params' in locals() and 'drone_aligned' in locals():\n", "    print(\"\\n=== CREATING VISUALIZATION ===\")\n", "    visualize_gcp_alignment(\n", "        drone_points, \n", "        drone_aligned, \n", "        ifc_points, \n", "        gcp_pairs=transform_params['gcp_residuals']\n", "    )\n", "else:\n", "    print(\"\\nSkipping visualization - transformation failed\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Summary"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== GCP-BASED ALIGNMENT COMPLETE ===\n", "\n", "Alignment successful with 5 GCP pairs\n", "RMS Error: 4.524m\n", "Scale Factor: 0.999576\n", "\n", "Results saved to: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/gcp_alignment/csf/\n", "  - Aligned point cloud: trino_enel_gcp_aligned.ply\n", "  - Transformation params: trino_enel_gcp_transform_params.json\n", "  - GCP residuals: trino_enel_gcp_residuals.csv\n"]}], "source": ["print(\"=== GCP-BASED ALIGNMENT COMPLETE ===\")\n", "\n", "if 'transform_params' in locals():\n", "    print(f\"\\nAlignment successful with {len(transform_params['gcp_residuals'])} GCP pairs\")\n", "    print(f\"RMS Error: {transform_params['rms_error']:.3f}m\")\n", "    print(f\"Scale Factor: {transform_params['scale']:.6f}\")\n", "    \n", "    if save_results:\n", "        print(f\"\\nResults saved to: {output_dir}/{ground_method}/\")\n", "        print(f\"  - Aligned point cloud: {site_name}_gcp_aligned.ply\")\n", "        print(f\"  - Transformation params: {site_name}_gcp_transform_params.json\")\n", "        print(f\"  - GCP residuals: {site_name}_gcp_residuals.csv\")\n", "    \n", "else:\n", "    print(f\"\\nAlignment failed - insufficient GCP pairs or other error\")\n", "    print(f\"Check GCP coordinates and search parameters\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**Next steps**:\n", "  1. Use aligned point cloud for pile detection\n", "  2. Compare with other alignment methods\n", "  3. Validate against ground truth data"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}