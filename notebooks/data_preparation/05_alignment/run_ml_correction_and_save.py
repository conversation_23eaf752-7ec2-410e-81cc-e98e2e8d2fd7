#!/usr/bin/env python3
"""
Quick script to run ML correction and save the PLY file
This ensures the ML-corrected point cloud is saved for visualization
"""

import sys
from pathlib import Path
import numpy as np
import pandas as pd
import open3d as o3d
from scipy.spatial import cKDTree
from sklearn.ensemble import RandomForestRegressor
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_squared_error, r2_score
import joblib
import json
from datetime import datetime

# Add project root to path
project_root = Path(__file__).resolve().parents[3]
sys.path.append(str(project_root))

# Parameters
site_name = "trino_enel"
ground_method = "ransac_pmf"

def main():
    print("=== RUNNING ML CORRECTION AND SAVING PLY ===")
    print(f"Site: {site_name}")
    print(f"Ground method: {ground_method}")
    
    # Setup paths
    notebooks_root = Path(__file__).resolve().parents[2]
    sys.path.insert(0, str(notebooks_root))
    
    from shared.config import get_processed_data_path
    
    output_dir = get_processed_data_path(site_name, "ml_local_alignment")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Load data
    print("\n1. Loading data...")
    
    # Load drone point cloud
    drone_file = project_root / f"data/processed/{site_name}/ground_segmentation/{ground_method}/{site_name}_nonground.ply"
    drone_pcd = o3d.io.read_point_cloud(str(drone_file))
    drone_points = np.asarray(drone_pcd.points)
    
    # Load IFC metadata
    ifc_metadata_file = project_root / f"data/processed/{site_name}/ifc_metadata/GRE.EEC.S.00.IT.P.14353.00.265_enhanced_metadata.csv"
    ifc_df = pd.read_csv(ifc_metadata_file)
    
    # Extract pile coordinates
    pile_mask = ifc_df['Name'].str.contains('Pile', case=False, na=False)
    pile_coords = ifc_df[pile_mask][['X', 'Y', 'Z']].dropna().values
    
    print(f"   Loaded drone points: {len(drone_points):,}")
    print(f"   Loaded pile coordinates: {len(pile_coords):,}")
    
    # Create training data
    print("\n2. Creating training data...")
    
    def create_training_data(drone_pts, pile_pts, max_distance=5.0):
        drone_tree = cKDTree(drone_pts[:, :2])
        training_features = []
        training_targets = []
        
        for pile_pt in pile_pts:
            distances, indices = drone_tree.query(pile_pt[:2], k=1, distance_upper_bound=max_distance)
            
            if distances < np.inf:
                drone_pt = drone_pts[indices]
                features = drone_pt
                z_correction = pile_pt[2] - drone_pt[2]
                training_features.append(features)
                training_targets.append(z_correction)
        
        return np.array(training_features), np.array(training_targets)
    
    X_train_raw, y_train = create_training_data(drone_points, pile_coords)
    print(f"   Training samples: {len(X_train_raw)}")
    print(f"   Z correction range: [{y_train.min():.2f}, {y_train.max():.2f}]m")
    
    # Feature engineering
    print("\n3. Engineering features...")
    
    def engineer_features(points):
        features = []
        feature_names = []
        
        # Basic coordinates
        features.extend([points[:, 0], points[:, 1], points[:, 2]])
        feature_names.extend(['x', 'y', 'z'])
        
        # Spatial features
        centroid = np.mean(points, axis=0)
        dist_from_center = np.linalg.norm(points - centroid, axis=1)
        features.append(dist_from_center)
        feature_names.append('dist_from_center')
        
        # Normalized coordinates
        points_norm = (points - centroid) / np.std(points, axis=0)
        features.extend([points_norm[:, 0], points_norm[:, 1]])
        feature_names.extend(['x_norm', 'y_norm'])
        
        # Polar coordinates
        angles = np.arctan2(points[:, 1] - centroid[1], points[:, 0] - centroid[0])
        features.extend([np.cos(angles), np.sin(angles)])
        feature_names.extend(['cos_angle', 'sin_angle'])
        
        # Geometric features
        features.extend([points[:, 0]**2, points[:, 1]**2, points[:, 0]*points[:, 1]])
        feature_names.extend(['x_squared', 'y_squared', 'xy_product'])
        
        # RBF features
        rbf_centers = np.percentile(points[:, :2], [25, 50, 75], axis=0)
        for i, center in enumerate(rbf_centers):
            rbf_dist = np.linalg.norm(points[:, :2] - center, axis=1)
            features.append(np.exp(-rbf_dist**2 / (2 * 100**2)))
            feature_names.append(f'rbf_{i}')
        
        # Contextual features
        tree = cKDTree(points[:, :2])
        neighbor_counts = tree.query_ball_point(points[:, :2], r=50.0, return_length=True)
        features.append(neighbor_counts)
        feature_names.append('local_density')
        
        local_z_std = []
        for pt in points:
            neighbors = tree.query_ball_point(pt[:2], r=25.0)
            if len(neighbors) > 1:
                local_z_std.append(np.std(points[neighbors, 2]))
            else:
                local_z_std.append(0.0)
        features.append(local_z_std)
        feature_names.append('local_z_std')
        
        return np.column_stack(features), feature_names
    
    X_train_engineered, feature_names = engineer_features(X_train_raw)
    X_all_engineered, _ = engineer_features(drone_points)
    
    print(f"   Engineered features: {len(feature_names)}")
    
    # Normalize features
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train_engineered)
    X_all_scaled = scaler.transform(X_all_engineered)
    
    # Train Random Forest
    print("\n4. Training Random Forest...")
    
    rf_model = RandomForestRegressor(
        n_estimators=100,
        max_depth=10,
        min_samples_split=5,
        min_samples_leaf=2,
        random_state=42,
        n_jobs=-1
    )
    
    # Split for validation
    X_train_split, X_val, y_train_split, y_val = train_test_split(
        X_train_scaled, y_train, test_size=0.2, random_state=42
    )
    
    # Train model
    rf_model.fit(X_train_split, y_train_split)
    
    # Validate
    val_predictions = rf_model.predict(X_val)
    val_rmse = np.sqrt(mean_squared_error(y_val, val_predictions))
    val_r2 = r2_score(y_val, val_predictions)
    
    print(f"   Validation RMSE: {val_rmse:.3f}m")
    print(f"   Validation R²: {val_r2:.3f}")
    
    # Make predictions for all points
    rf_predictions = rf_model.predict(X_all_scaled)
    print(f"   Predictions range: [{rf_predictions.min():.2f}, {rf_predictions.max():.2f}]m")
    
    # Apply corrections
    print("\n5. Applying corrections...")
    
    drone_ml_corrected = drone_points.copy()
    drone_ml_corrected[:, 2] += rf_predictions
    
    print(f"   Original Z range: [{drone_points[:, 2].min():.2f}, {drone_points[:, 2].max():.2f}]m")
    print(f"   Corrected Z range: [{drone_ml_corrected[:, 2].min():.2f}, {drone_ml_corrected[:, 2].max():.2f}]m")
    
    # Validate corrections
    print("\n6. Validating corrections...")
    
    def validate_corrections(drone_corrected, pile_coords, max_distance=2.0):
        drone_tree = cKDTree(drone_corrected[:, :2])
        validation_errors = []
        
        for pile_pt in pile_coords:
            distances, indices = drone_tree.query(pile_pt[:2], k=1, distance_upper_bound=max_distance)
            
            if distances < np.inf:
                drone_z = drone_corrected[indices, 2]
                pile_z = pile_pt[2]
                error = abs(drone_z - pile_z)
                validation_errors.append(error)
        
        if len(validation_errors) > 0:
            errors = np.array(validation_errors)
            return {
                'rmse': np.sqrt(np.mean(errors**2)),
                'mean_error': np.mean(errors),
                'median_error': np.median(errors),
                'max_error': np.max(errors),
                'num_validated': len(errors),
                'sub_meter_pct': np.sum(errors < 1.0) / len(errors) * 100,
                'sub_half_meter_pct': np.sum(errors < 0.5) / len(errors) * 100
            }
        return None
    
    ml_validation = validate_corrections(drone_ml_corrected, pile_coords)
    
    if ml_validation:
        print(f"   RMSE: {ml_validation['rmse']:.3f}m")
        print(f"   Sub-meter accuracy: {ml_validation['sub_meter_pct']:.1f}%")
        print(f"   Sub-half-meter accuracy: {ml_validation['sub_half_meter_pct']:.1f}%")
    
    # Save results
    print("\n7. Saving results...")
    
    # Save ML-corrected point cloud
    corrected_pcd = o3d.geometry.PointCloud()
    corrected_pcd.points = o3d.utility.Vector3dVector(drone_ml_corrected)
    
    # Copy colors if they exist
    if drone_pcd.has_colors():
        corrected_pcd.colors = drone_pcd.colors
    
    ml_corrected_file = output_dir / f"{site_name}_ml_corrected.ply"
    success = o3d.io.write_point_cloud(str(ml_corrected_file), corrected_pcd)
    
    if success:
        print(f"   ✅ Saved PLY: {ml_corrected_file}")
        print(f"      File size: {ml_corrected_file.stat().st_size / (1024*1024):.1f} MB")
    else:
        print(f"   ❌ Failed to save PLY")
    
    # Save numpy array
    numpy_file = output_dir / f"{site_name}_ml_corrected_points.npy"
    np.save(numpy_file, drone_ml_corrected)
    print(f"   ✅ Saved numpy: {numpy_file}")
    
    # Save model and scaler
    model_file = output_dir / f"{site_name}_rf_model.joblib"
    scaler_file = output_dir / f"{site_name}_feature_scaler.joblib"
    joblib.dump(rf_model, model_file)
    joblib.dump(scaler, scaler_file)
    print(f"   ✅ Saved model: {model_file}")
    print(f"   ✅ Saved scaler: {scaler_file}")
    
    # Save metadata
    metadata = {
        'site_name': site_name,
        'ground_method': ground_method,
        'ml_method': 'random_forest',
        'num_points': len(drone_ml_corrected),
        'num_features': len(feature_names),
        'feature_names': feature_names,
        'training_samples': len(X_train_raw),
        'validation_rmse': float(val_rmse),
        'validation_r2': float(val_r2),
        'final_rmse': float(ml_validation['rmse']) if ml_validation else None,
        'sub_meter_accuracy': float(ml_validation['sub_meter_pct']) if ml_validation else None,
        'timestamp': datetime.now().isoformat(),
        'original_z_range': [float(drone_points[:, 2].min()), float(drone_points[:, 2].max())],
        'corrected_z_range': [float(drone_ml_corrected[:, 2].min()), float(drone_ml_corrected[:, 2].max())],
        'correction_range': [float(rf_predictions.min()), float(rf_predictions.max())]
    }
    
    metadata_file = output_dir / f"{site_name}_ml_correction_metadata.json"
    with open(metadata_file, 'w') as f:
        json.dump(metadata, f, indent=2)
    print(f"   ✅ Saved metadata: {metadata_file}")
    
    print(f"\n🎯 SUCCESS! ML-corrected point cloud saved.")
    print(f"   📁 Output directory: {output_dir}")
    print(f"   📄 Main PLY file: {ml_corrected_file.name}")
    print(f"   📊 RMSE: {ml_validation['rmse']:.3f}m")
    print(f"   📈 Sub-meter accuracy: {ml_validation['sub_meter_pct']:.1f}%")

if __name__ == "__main__":
    main()
