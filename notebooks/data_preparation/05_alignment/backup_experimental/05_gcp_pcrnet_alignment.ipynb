{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# GCP-Anchored PCRNet Point Cloud Registration\n", "\n", "This notebook explores using Ground Control Points (GCPs) as anchors for deep learning-based point cloud registration using PCRNet architecture.\n", "\n", "## Approach\n", "1. Extract GCP correspondences from drone and IFC data\n", "2. Use GCPs to initialize and constrain PCRNet training\n", "3. Apply learned transformation for full point cloud alignment\n", "4. Validate results against coordinate-only baseline"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# Parameters (Papermill)\n", "ground_method = \"ransac_pmf\"  # Ground segmentation method\n", "site_name = \"trino_enel\"\n", "gcp_search_radius = 5.0  # Radius for GCP matching\n", "pcrnet_epochs = 100\n", "learning_rate = 0.001\n", "batch_size = 1\n", "\n", "output_dir = \"../../../data/output_runs/gcp_pcrnet_alignment\"\n", "enable_visualization = True\n", "save_results = True"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["GCP-Anchored PCRNet Point Cloud Registration\n", "Site: trino_enel\n", "Device: cpu\n", "Output: ../../../data/output_runs/gcp_pcrnet_alignment/ransac_pmf\n"]}], "source": ["# Imports\n", "import numpy as np\n", "import open3d as o3d\n", "import torch\n", "import torch.nn as nn\n", "import torch.optim as optim\n", "import matplotlib.pyplot as plt\n", "import pandas as pd\n", "from pathlib import Path\n", "from datetime import datetime\n", "from scipy.spatial import cKDTree\n", "from sklearn.neighbors import NearestNeighbors\n", "import json\n", "\n", "# Setup\n", "device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "output_path = Path(output_dir) / ground_method\n", "output_path.mkdir(parents=True, exist_ok=True)\n", "\n", "print(\"GCP-Anchored PCRNet Point Cloud Registration\")\n", "print(f\"Site: {site_name}\")\n", "print(f\"Device: {device}\")\n", "print(f\"Output: {output_path}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Data Loading and Preprocessing"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[1;33m[Open3D WARNING] Read PLY failed: unable to open file: ../../../data/processed/trino_enel/ground_segmentation/ransac_pmf/trino_enel_nonground.ply\u001b[0;m\n", "\u001b[1;33m[Open3D WARNING] Read PLY failed: unable to open file: ../../../data/processed/trino_enel/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply\u001b[0;m\n", "Loaded drone points: 0\n", "Loaded IFC points: 0\n", "Loaded 12 ground control points\n", "   Point_ID     Easting     Northing Location\n", "0         1  435267.277  5011787.189       NE\n", "1         2  435280.920  5011792.074       NE\n", "2         3  435267.220  5011921.754       NE\n", "3         4  435280.438  5011925.654       NE\n", "4         5  436224.793  5012459.896       NW\n", "5         6  436226.921  5012459.942       NW\n", "6      4_sw  436719.919  5011825.557       SW\n", "7     4_sw2  436718.628  5011831.735       SW\n", "8      5_SM  436302.667  5011417.757       SM\n", "9     5_SM2  436305.828  5011410.901       SM\n", "10     6_SE  436112.667  5010904.990       SE\n", "11    6_SE2  436118.344  5010917.949       SE\n", "\n", "Coordinate Scale Analysis:\n"]}, {"name": "stderr", "output_type": "stream", "text": ["RPly: Unable to open file\n", "RPly: Unable to open file\n"]}, {"ename": "ValueError", "evalue": "zero-size array to reduction operation minimum which has no identity", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m                                <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[3]\u001b[39m\u001b[32m, line 57\u001b[39m\n\u001b[32m     55\u001b[39m \u001b[38;5;66;03m# CRITICAL: Check coordinate scales and ranges\u001b[39;00m\n\u001b[32m     56\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[33mCoordinate Scale Analysis:\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m---> \u001b[39m\u001b[32m57\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mDrone points range: X[\u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[43mnp\u001b[49m\u001b[43m.\u001b[49m\u001b[43mmin\u001b[49m\u001b[43m(\u001b[49m\u001b[43mdrone_points\u001b[49m\u001b[43m[\u001b[49m\u001b[43m:\u001b[49m\u001b[43m,\u001b[49m\u001b[32;43m0\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;132;01m:\u001b[39;00m\u001b[33m.1f\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m, \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mnp.max(drone_points[:,\u001b[32m0\u001b[39m])\u001b[38;5;132;01m:\u001b[39;00m\u001b[33m.1f\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m] Y[\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mnp.min(drone_points[:,\u001b[32m1\u001b[39m])\u001b[38;5;132;01m:\u001b[39;00m\u001b[33m.1f\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m, \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mnp.max(drone_points[:,\u001b[32m1\u001b[39m])\u001b[38;5;132;01m:\u001b[39;00m\u001b[33m.1f\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m] Z[\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mnp.min(drone_points[:,\u001b[32m2\u001b[39m])\u001b[38;5;132;01m:\u001b[39;00m\u001b[33m.1f\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m, \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mnp.max(drone_points[:,\u001b[32m2\u001b[39m])\u001b[38;5;132;01m:\u001b[39;00m\u001b[33m.1f\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m]\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m     58\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mIFC points range: X[\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mnp.min(ifc_points[:,\u001b[32m0\u001b[39m])\u001b[38;5;132;01m:\u001b[39;00m\u001b[33m.1f\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m, \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mnp.max(ifc_points[:,\u001b[32m0\u001b[39m])\u001b[38;5;132;01m:\u001b[39;00m\u001b[33m.1f\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m] Y[\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mnp.min(ifc_points[:,\u001b[32m1\u001b[39m])\u001b[38;5;132;01m:\u001b[39;00m\u001b[33m.1f\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m, \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mnp.max(ifc_points[:,\u001b[32m1\u001b[39m])\u001b[38;5;132;01m:\u001b[39;00m\u001b[33m.1f\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m] Z[\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mnp.min(ifc_points[:,\u001b[32m2\u001b[39m])\u001b[38;5;132;01m:\u001b[39;00m\u001b[33m.1f\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m, \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mnp.max(ifc_points[:,\u001b[32m2\u001b[39m])\u001b[38;5;132;01m:\u001b[39;00m\u001b[33m.1f\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m]\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m     59\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mGCP range: X[\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mnp.min(ifc_gcp_coords[:,\u001b[32m0\u001b[39m])\u001b[38;5;132;01m:\u001b[39;00m\u001b[33m.1f\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m, \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mnp.max(ifc_gcp_coords[:,\u001b[32m0\u001b[39m])\u001b[38;5;132;01m:\u001b[39;00m\u001b[33m.1f\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m] Y[\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mnp.min(ifc_gcp_coords[:,\u001b[32m1\u001b[39m])\u001b[38;5;132;01m:\u001b[39;00m\u001b[33m.1f\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m, \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mnp.max(ifc_gcp_coords[:,\u001b[32m1\u001b[39m])\u001b[38;5;132;01m:\u001b[39;00m\u001b[33m.1f\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m]\u001b[39m\u001b[33m\"\u001b[39m)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/numpy/core/fromnumeric.py:2953\u001b[39m, in \u001b[36mmin\u001b[39m\u001b[34m(a, axis, out, keepdims, initial, where)\u001b[39m\n\u001b[32m   2836\u001b[39m \u001b[38;5;129m@array_function_dispatch\u001b[39m(_min_dispatcher)\n\u001b[32m   2837\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mmin\u001b[39m(a, axis=\u001b[38;5;28;01mNone\u001b[39;00m, out=\u001b[38;5;28;01mNone\u001b[39;00m, keepdims=np._NoValue, initial=np._NoValue,\n\u001b[32m   2838\u001b[39m         where=np._NoValue):\n\u001b[32m   2839\u001b[39m \u001b[38;5;250m    \u001b[39m\u001b[33;03m\"\"\"\u001b[39;00m\n\u001b[32m   2840\u001b[39m \u001b[33;03m    Return the minimum of an array or minimum along an axis.\u001b[39;00m\n\u001b[32m   2841\u001b[39m \n\u001b[32m   (...)\u001b[39m\u001b[32m   2951\u001b[39m \u001b[33;03m    6\u001b[39;00m\n\u001b[32m   2952\u001b[39m \u001b[33;03m    \"\"\"\u001b[39;00m\n\u001b[32m-> \u001b[39m\u001b[32m2953\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43m_wrapreduction\u001b[49m\u001b[43m(\u001b[49m\u001b[43ma\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mnp\u001b[49m\u001b[43m.\u001b[49m\u001b[43mminimum\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[33;43m'\u001b[39;49m\u001b[33;43mmin\u001b[39;49m\u001b[33;43m'\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43maxis\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mout\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2954\u001b[39m \u001b[43m                          \u001b[49m\u001b[43mkeepdims\u001b[49m\u001b[43m=\u001b[49m\u001b[43mkeepdims\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43minitial\u001b[49m\u001b[43m=\u001b[49m\u001b[43minitial\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mwhere\u001b[49m\u001b[43m=\u001b[49m\u001b[43mwhere\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/numpy/core/fromnumeric.py:88\u001b[39m, in \u001b[36m_wrapreduction\u001b[39m\u001b[34m(obj, ufunc, method, axis, dtype, out, **kwargs)\u001b[39m\n\u001b[32m     85\u001b[39m         \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m     86\u001b[39m             \u001b[38;5;28;01mreturn\u001b[39;00m reduction(axis=axis, out=out, **passkwargs)\n\u001b[32m---> \u001b[39m\u001b[32m88\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mufunc\u001b[49m\u001b[43m.\u001b[49m\u001b[43mreduce\u001b[49m\u001b[43m(\u001b[49m\u001b[43mobj\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43maxis\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mdtype\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mout\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mpasskwargs\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[31mValueError\u001b[39m: zero-size array to reduction operation minimum which has no identity"]}], "source": ["# Define file paths\n", "drone_file = f\"../../../data/processed/{site_name}/ground_segmentation/{ground_method}/{site_name}_nonground.ply\"\n", "ifc_pointcloud_file = f\"../../../data/processed/{site_name}/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply\"\n", "def load_point_clouds():\n", "    \"\"\"Load drone and IFC point clouds\"\"\"\n", "    drone_pcd = o3d.io.read_point_cloud(drone_file)\n", "    ifc_pcd = o3d.io.read_point_cloud(ifc_pointcloud_file)\n", "    \n", "    drone_points = np.asarray(drone_pcd.points)\n", "    ifc_points = np.asarray(ifc_pcd.points)\n", "    \n", "    print(f\"Loaded drone points: {len(drone_points):,}\")\n", "    print(f\"Loaded IFC points: {len(ifc_points):,}\")\n", "    \n", "    return drone_points, ifc_points\n", "\n", "def load_ground_control_points():\n", "    \"\"\"Load the ground control points from project data\"\"\"\n", "    gcp_data = {\n", "        'Point_ID': ['1', '2', '3', '4', '5', '6'],\n", "        'Easting': [435267.277, 435280.92, 435267.22, 435280.438, 436224.793, 436226.921],\n", "        'Northing': [5011787.189, 5011792.074, 5011921.754, 5011925.654, 5012459.896, 5012459.942],\n", "        'Location': ['NE', 'NE', 'NE', 'NE', 'NW', 'NW']\n", "    }\n", "    \n", "    # Additional points from project data\n", "    additional_points = {\n", "        'Point_ID': ['4_sw', '4_sw2', '5_SM', '5_SM2', '6_SE', '6_SE2'],\n", "        'Easting': [436719.919, 436718.628, 436302.667, 436305.828, 436112.667, 436118.344],\n", "        'Northing': [5011825.557, 5011831.735, 5011417.757, 5011410.901, 5010904.99, 5010917.949],\n", "        'Location': ['SW', 'SW', 'SM', 'SM', 'SE', 'SE']\n", "    }\n", "    \n", "    # <PERSON><PERSON><PERSON> all points\n", "    all_data = {}\n", "    for key in gcp_data:\n", "        all_data[key] = gcp_data[key] + additional_points[key]\n", "    \n", "    df = pd.DataFrame(all_data)\n", "    \n", "    # Convert to 3D coordinates (assuming Z=0 for ground level)\n", "    ifc_gcp_coords = np.column_stack([\n", "        df['Easting'].values,\n", "        df['Northing'].values,\n", "        np.zeros(len(df))  # Assume ground level Z=0\n", "    ])\n", "    \n", "    return df, ifc_gcp_coords\n", "\n", "drone_points, ifc_points = load_point_clouds()\n", "gcp_df, ifc_gcp_coords = load_ground_control_points()\n", "print(f\"Loaded {len(ifc_gcp_coords)} ground control points\")\n", "print(gcp_df)\n", "\n", "# CRITICAL: Check coordinate scales and ranges\n", "print(f\"\\nCoordinate Scale Analysis:\")\n", "print(f\"Drone points range: X[{np.min(drone_points[:,0]):.1f}, {np.max(drone_points[:,0]):.1f}] Y[{np.min(drone_points[:,1]):.1f}, {np.max(drone_points[:,1]):.1f}] Z[{np.min(drone_points[:,2]):.1f}, {np.max(drone_points[:,2]):.1f}]\")\n", "print(f\"IFC points range: X[{np.min(ifc_points[:,0]):.1f}, {np.max(ifc_points[:,0]):.1f}] Y[{np.min(ifc_points[:,1]):.1f}, {np.max(ifc_points[:,1]):.1f}] Z[{np.min(ifc_points[:,2]):.1f}, {np.max(ifc_points[:,2]):.1f}]\")\n", "print(f\"GCP range: X[{np.min(ifc_gcp_coords[:,0]):.1f}, {np.max(ifc_gcp_coords[:,0]):.1f}] Y[{np.min(ifc_gcp_coords[:,1]):.1f}, {np.max(ifc_gcp_coords[:,1]):.1f}]\")\n", "\n", "# Check if coordinate systems are compatible\n", "drone_center = np.mean(drone_points, axis=0)\n", "ifc_center = np.mean(ifc_points, axis=0)\n", "gcp_center = np.mean(ifc_gcp_coords, axis=0)\n", "print(f\"\\nCentroid Analysis:\")\n", "print(f\"Drone centroid: [{drone_center[0]:.1f}, {drone_center[1]:.1f}, {drone_center[2]:.1f}]\")\n", "print(f\"IFC centroid: [{ifc_center[0]:.1f}, {ifc_center[1]:.1f}, {ifc_center[2]:.1f}]\")\n", "print(f\"GCP centroid: [{gcp_center[0]:.1f}, {gcp_center[1]:.1f}, {gcp_center[2]:.1f}]\")\n", "\n", "# Check for major coordinate system mismatch\n", "drone_to_gcp_dist = np.linalg.norm(drone_center[:2] - gcp_center[:2])\n", "ifc_to_gcp_dist = np.linalg.norm(ifc_center[:2] - gcp_center[:2])\n", "print(f\"\\nCoordinate System Compatibility:\")\n", "print(f\"Drone to GCP distance: {drone_to_gcp_dist:.1f}m\")\n", "print(f\"IFC to GCP distance: {ifc_to_gcp_dist:.1f}m\")\n", "\n", "if drone_to_gcp_dist > 100000 or ifc_to_gcp_dist > 100000:\n", "    print(\"WARNING: Major coordinate system mismatch detected!\")\n", "    print(\"Point clouds appear to be in local coordinates while GCPs are in UTM.\")\n", "    print(\"This will cause PCRNet training instability.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. GCP Correspondence Extraction"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Searching for correspondences within 5.0m of 12 GCPs...\n", "  GCP 1: No points found within 5.0m radius\n", "  GCP 2: No points found within 5.0m radius\n", "  GCP 3: No points found within 5.0m radius\n", "  GCP 4: No points found within 5.0m radius\n", "  GCP 5: No points found within 5.0m radius\n", "  GCP 6: No points found within 5.0m radius\n", "  GCP 4_sw: No points found within 5.0m radius\n", "  GCP 4_sw2: No points found within 5.0m radius\n", "  GCP 5_SM: No points found within 5.0m radius\n", "  GCP 5_SM2: No points found within 5.0m radius\n", "  GCP 6_SE: No points found within 5.0m radius\n", "  GCP 6_SE2: No points found within 5.0m radius\n", "No GCP correspondences found, using centroid fallback\n"]}], "source": ["def normalize_coordinates_for_training(drone_pts, ifc_pts, gcp_coords):\n", "    \"\"\"Normalize coordinates to prevent numerical instability in neural network training\"\"\"\n", "    # Combine all coordinates to find global scale\n", "    all_coords = np.vstack([drone_pts, ifc_pts, gcp_coords])\n", "    \n", "    # Find global centroid and scale\n", "    global_center = np.mean(all_coords, axis=0)\n", "    global_scale = np.std(all_coords)\n", "    \n", "    print(f\"Normalization parameters:\")\n", "    print(f\"  Global center: [{global_center[0]:.1f}, {global_center[1]:.1f}, {global_center[2]:.1f}]\")\n", "    print(f\"  Global scale: {global_scale:.1f}\")\n", "    \n", "    # Normalize all coordinate sets\n", "    drone_norm = (drone_pts - global_center) / global_scale\n", "    ifc_norm = (ifc_pts - global_center) / global_scale\n", "    gcp_norm = (gcp_coords - global_center) / global_scale\n", "    \n", "    # Verify normalization\n", "    print(f\"After normalization:\")\n", "    print(f\"  Drone range: [{np.min(drone_norm):.3f}, {np.max(drone_norm):.3f}]\")\n", "    print(f\"  IFC range: [{np.min(ifc_norm):.3f}, {np.max(ifc_norm):.3f}]\")\n", "    print(f\"  GCP range: [{np.min(gcp_norm):.3f}, {np.max(gcp_norm):.3f}]\")\n", "    \n", "    return drone_norm, ifc_norm, gcp_norm, global_center, global_scale\n", "\n", "def denormalize_transformation(transform_norm, global_center, global_scale):\n", "    \"\"\"Convert normalized transformation back to original coordinate system\"\"\"\n", "    # Extract rotation and translation from normalized transform\n", "    R_norm = transform_norm[:3, :3]\n", "    t_norm = transform_norm[:3, 3]\n", "    \n", "    # Scale translation back to original coordinates\n", "    t_original = t_norm * global_scale\n", "    \n", "    # Rotation matrix stays the same (scale-invariant)\n", "    transform_original = np.eye(4)\n", "    transform_original[:3, :3] = R_norm\n", "    transform_original[:3, 3] = t_original\n", "    \n", "    return transform_original\n", "\n", "def extract_gcp_correspondences(drone_pts, ifc_pts, ifc_gcp_coords, search_radius=5.0):\n", "    \"\"\"Extract corresponding points near GCP locations\"\"\"\n", "    drone_correspondences = []\n", "    ifc_correspondences = []\n", "    \n", "    # Build KD-trees for efficient nearest neighbor search\n", "    drone_tree = cKDTree(drone_pts)\n", "    ifc_tree = cKDTree(ifc_pts)\n", "    \n", "    print(f\"Searching for correspondences within {search_radius}m of {len(ifc_gcp_coords)} GCPs...\")\n", "    \n", "    for i, gcp in enumerate(ifc_gcp_coords):\n", "        # Find drone points near this GCP\n", "        drone_indices = drone_tree.query_ball_point(gcp, search_radius)\n", "        \n", "        # Find IFC points near this GCP\n", "        ifc_indices = ifc_tree.query_ball_point(gcp, search_radius)\n", "        \n", "        if len(drone_indices) > 0 and len(ifc_indices) > 0:\n", "            # Use centroid of nearby points as correspondence\n", "            drone_centroid = np.mean(drone_pts[drone_indices], axis=0)\n", "            ifc_centroid = np.mean(ifc_pts[ifc_indices], axis=0)\n", "            \n", "            drone_correspondences.append(drone_centroid)\n", "            ifc_correspondences.append(ifc_centroid)\n", "            \n", "            gcp_id = gcp_df.iloc[i]['Point_ID']\n", "            location = gcp_df.iloc[i]['Location']\n", "            print(f\"  GCP {gcp_id} ({location}): Found {len(drone_indices)} drone + {len(ifc_indices)} IFC points\")\n", "        else:\n", "            gcp_id = gcp_df.iloc[i]['Point_ID']\n", "            print(f\"  GCP {gcp_id}: No points found within {search_radius}m radius\")\n", "    \n", "    if len(drone_correspondences) == 0:\n", "        print(\"No GCP correspondences found, using centroid fallback\")\n", "        drone_center = np.mean(drone_pts, axis=0).reshape(1, 3)\n", "        ifc_center = np.mean(ifc_pts, axis=0).reshape(1, 3)\n", "        return drone_center, ifc_center\n", "    \n", "    drone_correspondences = np.array(drone_correspondences)\n", "    ifc_correspondences = np.array(ifc_correspondences)\n", "    \n", "    print(f\"Successfully extracted {len(drone_correspondences)} GCP correspondences\")\n", "    return drone_correspondences, ifc_correspondences\n", "\n", "drone_gcps, ifc_gcps = extract_gcp_correspondences(drone_points, ifc_points, ifc_gcp_coords, gcp_search_radius)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Simplified PCRNet Architecture"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Model initialized with 1324172 parameters\n"]}], "source": ["class SimplePCRNet(nn.Module):\n", "    \"\"\"Simplified PCRNet for point cloud registration\"\"\"\n", "    \n", "    def __init__(self, feature_dim=1024):\n", "        super(SimplePCRNet, self).__init__()\n", "        \n", "        # Point feature extraction (simplified PointNet)\n", "        self.conv1 = nn.Conv1d(3, 64, 1)\n", "        self.conv2 = nn.Conv1d(64, 128, 1)\n", "        self.conv3 = nn.Conv1d(128, feature_dim, 1)\n", "        \n", "        # Transformation prediction\n", "        self.fc1 = nn.Linear(feature_dim * 2, 512)\n", "        self.fc2 = nn.Linear(512, 256)\n", "        self.fc3 = nn.Linear(256, 12)  # 3x4 transformation matrix (rotation + translation)\n", "        \n", "        self.relu = nn.ReLU()\n", "        self.dropout = nn.Dropout(0.3)\n", "        \n", "    def extract_features(self, x):\n", "        \"\"\"Extract global features from point cloud\"\"\"\n", "        # x: (batch_size, 3, num_points)\n", "        x = self.relu(self.conv1(x))\n", "        x = self.relu(self.conv2(x))\n", "        x = self.conv3(x)\n", "        \n", "        # Global max pooling\n", "        x = torch.max(x, 2)[0]  # (batch_size, feature_dim)\n", "        return x\n", "    \n", "    def forward(self, source, target):\n", "        \"\"\"Predict transformation from source to target\"\"\"\n", "        # Extract features\n", "        source_feat = self.extract_features(source)\n", "        target_feat = self.extract_features(target)\n", "        \n", "        # Concatenate features\n", "        combined = torch.cat([source_feat, target_feat], dim=1)\n", "        \n", "        # Predict transformation\n", "        x = self.relu(self.fc1(combined))\n", "        x = self.dropout(x)\n", "        x = self.relu(self.fc2(x))\n", "        x = self.dropout(x)\n", "        x = self.fc3(x)\n", "        \n", "        # Reshape to 3x4 transformation matrix\n", "        transform = x.view(-1, 3, 4)\n", "        \n", "        return transform\n", "\n", "# Initialize model\n", "model = SimplePCRNet().to(device)\n", "optimizer = optim.Adam(model.parameters(), lr=learning_rate)\n", "\n", "print(f\"Model initialized with {sum(p.numel() for p in model.parameters())} parameters\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. <PERSON><PERSON>-Anchored Training"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Training PCRNet for 100 epochs...\n", "  Epoch 20/100, Loss: 30621324946380213452800.000000\n", "  Epoch 40/100, Loss: 42522717465654581002240.000000\n", "  Epoch 60/100, Loss: 9710286591867284881408.000000\n", "  Epoch 80/100, Loss: 35766869916435913637888.000000\n", "  Epoch 100/100, Loss: 81079420984258627371008.000000\n", "Training completed. Final loss: 81079420984258627371008.000000\n"]}], "source": ["def prepare_training_data(drone_pts, ifc_pts, num_points=1024):\n", "    \"\"\"Prepare point clouds for training\"\"\"\n", "    # Downsample for training efficiency\n", "    if len(drone_pts) > num_points:\n", "        drone_indices = np.random.choice(len(drone_pts), num_points, replace=False)\n", "        drone_sample = drone_pts[drone_indices]\n", "    else:\n", "        drone_sample = drone_pts\n", "    \n", "    if len(ifc_pts) > num_points:\n", "        ifc_indices = np.random.choice(len(ifc_pts), num_points, replace=False)\n", "        ifc_sample = ifc_pts[ifc_indices]\n", "    else:\n", "        ifc_sample = ifc_pts\n", "    \n", "    # Convert to tensors and transpose for conv1d (batch_size, channels, num_points)\n", "    drone_tensor = torch.FloatTensor(drone_sample).unsqueeze(0).transpose(1, 2).to(device)\n", "    ifc_tensor = torch.FloatTensor(ifc_sample).unsqueeze(0).transpose(1, 2).to(device)\n", "    \n", "    return drone_tensor, ifc_tensor\n", "\n", "def gcp_loss(predicted_transform, drone_gcps, ifc_gcps):\n", "    \"\"\"Compute loss based on GCP correspondences\"\"\"\n", "    # Convert GCPs to tensors\n", "    drone_gcp_tensor = torch.FloatTensor(drone_gcps).to(device)\n", "    ifc_gcp_tensor = torch.FloatTensor(ifc_gcps).to(device)\n", "    \n", "    # Apply predicted transformation to drone GCPs\n", "    # Add homogeneous coordinate\n", "    drone_homo = torch.cat([drone_gcp_tensor, torch.ones(len(drone_gcps), 1).to(device)], dim=1)\n", "    \n", "    # Apply transformation\n", "    transformed_drone = torch.matmul(predicted_transform.squeeze(0), drone_homo.T).T\n", "    \n", "    # Compute MSE loss with target GCPs\n", "    loss = nn.MSELoss()(transformed_drone, ifc_gcp_tensor)\n", "    \n", "    return loss\n", "\n", "def train_pcrnet(model, drone_pts, ifc_pts, drone_gcps, ifc_gcps, epochs=100):\n", "    \"\"\"Train PCRNet with GCP anchoring\"\"\"\n", "    model.train()\n", "    losses = []\n", "    \n", "    print(f\"Training PCRNet for {epochs} epochs...\")\n", "    \n", "    for epoch in range(epochs):\n", "        # Prepare training data with random sampling\n", "        drone_tensor, ifc_tensor = prepare_training_data(drone_pts, ifc_pts)\n", "        \n", "        # Forward pass\n", "        optimizer.zero_grad()\n", "        predicted_transform = model(drone_tensor, ifc_tensor)\n", "        \n", "        # Compute GCP-anchored loss\n", "        loss = gcp_loss(predicted_transform, drone_gcps, ifc_gcps)\n", "        \n", "        # Backward pass\n", "        loss.backward()\n", "        optimizer.step()\n", "        \n", "        losses.append(loss.item())\n", "        \n", "        if (epoch + 1) % 20 == 0:\n", "            print(f\"  Epoch {epoch+1}/{epochs}, Loss: {loss.item():.6f}\")\n", "    \n", "    print(f\"Training completed. Final loss: {losses[-1]:.6f}\")\n", "    return losses\n", "\n", "# Train the model\n", "training_losses = train_pcrnet(model, drone_points, ifc_points, drone_gcps, ifc_gcps, pcrnet_epochs)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Apply Learned Transformation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Learned transformation matrix:\n", "[[ 2.69957559e+04 -1.92833613e+04  2.44445969e+05  2.38546406e+05]\n", " [ 3.97123555e+04  1.60991807e+04 -5.20797375e+05 -3.93420125e+05]\n", " [-5.64584062e+04 -4.86203278e+02  1.17949766e+05  1.69780352e+04]]\n", "GCP-PCRNet Alignment Results:\n", "  RMSE: 132431437775.84m\n", "  Median distance: 132431010267.73m\n", "  Good points (<2m): 0.0%\n", "  Centroid error: 132431473522.219m\n"]}], "source": ["def apply_pcrnet_transformation(model, drone_pts, ifc_pts):\n", "    \"\"\"Apply learned transformation to full point cloud\"\"\"\n", "    model.eval()\n", "    \n", "    with torch.no_grad():\n", "        # Prepare data\n", "        drone_tensor, ifc_tensor = prepare_training_data(drone_pts, ifc_pts, num_points=2048)\n", "        \n", "        # Predict transformation\n", "        predicted_transform = model(drone_tensor, ifc_tensor)\n", "        transform_matrix = predicted_transform.squeeze(0).cpu().numpy()\n", "        \n", "        print(f\"Learned transformation matrix:\")\n", "        print(transform_matrix)\n", "        \n", "        # Apply to full drone point cloud\n", "        drone_homo = np.column_stack([drone_pts, np.ones(len(drone_pts))])\n", "        transformed_drone = (transform_matrix @ drone_homo.T).T\n", "        \n", "        return transformed_drone, transform_matrix\n", "\n", "def evaluate_alignment(transformed_drone, ifc_pts, method_name=\"PCRNet\"):\n", "    \"\"\"Evaluate alignment quality\"\"\"\n", "    # Sample points for evaluation\n", "    sample_size = min(5000, len(transformed_drone), len(ifc_pts))\n", "    \n", "    drone_sample = transformed_drone[np.random.choice(len(transformed_drone), sample_size, replace=False)]\n", "    ifc_sample = ifc_pts[np.random.choice(len(ifc_pts), sample_size, replace=False)]\n", "    \n", "    # Compute nearest neighbor distances\n", "    tree = cKDTree(ifc_sample)\n", "    distances, _ = tree.query(drone_sample)\n", "    \n", "    # Metrics\n", "    rmse = np.sqrt(np.mean(distances**2))\n", "    median_dist = np.median(distances)\n", "    good_points = np.sum(distances < 2.0) / len(distances) * 100\n", "    \n", "    # Centroid alignment\n", "    drone_center = np.mean(transformed_drone, axis=0)\n", "    ifc_center = np.mean(ifc_pts, axis=0)\n", "    centroid_error = np.linalg.norm(drone_center - ifc_center)\n", "    \n", "    results = {\n", "        'method': method_name,\n", "        'rmse': rmse,\n", "        'median_distance': median_dist,\n", "        'good_points_pct': good_points,\n", "        'centroid_error': centroid_error,\n", "        'sample_size': sample_size\n", "    }\n", "    \n", "    print(f\"{method_name} Alignment Results:\")\n", "    print(f\"  RMSE: {rmse:.2f}m\")\n", "    print(f\"  Median distance: {median_dist:.2f}m\")\n", "    print(f\"  Good points (<2m): {good_points:.1f}%\")\n", "    print(f\"  Centroid error: {centroid_error:.3f}m\")\n", "    \n", "    return results\n", "\n", "# Apply learned transformation\n", "transformed_drone, learned_transform = apply_pcrnet_transformation(model, drone_points, ifc_points)\n", "pcrnet_results = evaluate_alignment(transformed_drone, ifc_points, \"GCP-PCRNet\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. <PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Baseline Comparisons:\n", "Coordinate-Only Alignment Results:\n", "  RMSE: 30.89m\n", "  Median distance: 10.39m\n", "  Good points (<2m): 1.5%\n", "  Centroid error: 0.000m\n", "Insufficient GCPs for rigid transformation, using coordinate baseline\n", "GCP-Rigid Alignment Results:\n", "  RMSE: 29.77m\n", "  Median distance: 10.52m\n", "  Good points (<2m): 1.4%\n", "  Centroid error: 0.000m\n"]}], "source": ["def coordinate_baseline_alignment(drone_pts, ifc_pts):\n", "    \"\"\"Simple coordinate-based alignment for comparison\"\"\"\n", "    drone_center = np.mean(drone_pts, axis=0)\n", "    ifc_center = np.mean(ifc_pts, axis=0)\n", "    offset = ifc_center - drone_center\n", "    \n", "    aligned_drone = drone_pts + offset\n", "    return aligned_drone, offset\n", "\n", "def gcp_baseline_alignment(drone_pts, ifc_pts, drone_gcps, ifc_gcps):\n", "    \"\"\"GCP-based rigid transformation using Procrustes analysis\"\"\"\n", "    if len(drone_gcps) < 3:\n", "        print(\"Insufficient GCPs for rigid transformation, using coordinate baseline\")\n", "        return coordinate_baseline_alignment(drone_pts, ifc_pts)\n", "    \n", "    # Center the GCP sets\n", "    drone_gcp_centered = drone_gcps - np.mean(drone_gcps, axis=0)\n", "    ifc_gcp_centered = ifc_gcps - np.mean(ifc_gcps, axis=0)\n", "    \n", "    # Compute optimal rotation using SVD\n", "    H = drone_gcp_centered.T @ ifc_gcp_centered\n", "    U, S, Vt = np.linalg.svd(H)\n", "    R = Vt.T @ U.T\n", "    \n", "    # Ensure proper rotation (det(R) = 1)\n", "    if np.linalg.det(R) < 0:\n", "        Vt[-1, :] *= -1\n", "        R = Vt.T @ U.T\n", "    \n", "    # Compute translation\n", "    t = np.mean(ifc_gcps, axis=0) - R @ np.mean(drone_gcps, axis=0)\n", "    \n", "    # Apply transformation\n", "    aligned_drone = (R @ drone_pts.T).T + t\n", "    \n", "    return aligned_drone, (R, t)\n", "\n", "# Compare with baselines\n", "print(\"\\nBaseline Comparisons:\")\n", "\n", "# Coordinate-only baseline\n", "coord_aligned, coord_offset = coordinate_baseline_alignment(drone_points, ifc_points)\n", "coord_results = evaluate_alignment(coord_aligned, ifc_points, \"Coordinate-Only\")\n", "\n", "# GCP-based rigid transformation baseline\n", "gcp_aligned, gcp_transform = gcp_baseline_alignment(drone_points, ifc_points, drone_gcps, ifc_gcps)\n", "gcp_results = evaluate_alignment(gcp_aligned, ifc_points, \"GCP-Rigid\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Results Analysis and Visualization"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAA90AAAJOCAYAAACqS2TfAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjMsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvZiW1igAAAAlwSFlzAAAPYQAAD2EBqD+naQABAABJREFUeJzsnQmcHFW1/8/sk5lksu8JBAiQhJAEEpYEZZGwhQfigog+4aHgc0ERXBENIk9xA1FBERER//JYfIrvPfPYdwhEEsIeICQh+57MZPb1/zm361Tfqr5VXdVd3VU98/vyGTLd00vV7arq+7vnnN8p6+vr6yMAAAAAAAAAAABETnn0LwkAAAAAAAAAAAAGohsAAAAAAAAAACgQEN0AAAAAAAAAAECBgOgGAAAAAAAAAAAKBEQ3AAAAAAAAAABQICC6AQAAAAAAAACAAgHRDQAAAAAAAAAAFAiIbgAAAAAAAAAAoEBAdAMAAAAAAAAAAAUCohsAUFCmTJlC//Zv/5bTc0888UT1AwAAID7Kysroe9/7nn37jjvuUPetW7cu1u0C/vBndOmllxb8fXA8BIfHiMeKxwwMLCC6wYBCvhjkp7a2lg455BD1pbRt27aMx/N9X/va12jatGlUV1dH9fX1NHfuXPqP//gP2rt3r/04Fob66w4aNIhmzZpFN954I/X29hovuPzzX//1XxnvyRMb/tvOnTtD799zzz2nnq9vm4knnnjCsb1+PwN5seBf/uVf4t4MAAAoKL/+9a/Vtf6YY46Je1NKZrzCCCb5Lr344ouNf7/qqqvsxxTyez9JrFy5kv71X/+VJk+eTDU1NTRixAhauHAh/eEPf6Cenp64Nw+AglBZmJcFINl8//vfpwMOOIDa29vpmWeeod/85je0ZMkSeu2115S4Zv75z3/SokWLqLm5WX05sNhmXnzxRfrRj35ETz31FD300EP2a06aNImuu+469Tt/cd511110+eWX044dO+gHP/iB53Z8+MMfjkzc8pfvNddcoyLLw4YN83zc9OnT6U9/+pPjviuvvJIGDx6sJgBR8tZbb1F5eW7re/r4AgAAiJ4///nPapFx2bJltHr1apo6dWrW53zqU5+ij3/840owDUTRPWrUqFAZXLzAz4vs/Nzq6mrH3/7zP/9T/Z3nI4X83k8Kt912G33uc5+jsWPHquPo4IMPpn379tGjjz5Kn/nMZ2jLli307W9/m/or+++/P7W1tVFVVVXcmwKKDEQ3GJCcccYZNG/ePPU7rz6PHDmSbrjhBvr73/9O559/vlox/tCHPkQVFRX00ksvqUi3Dovo3/3ud477hg4dqsS5wF8q/Lxf/epXSlzza+nMmTNHrfb+7W9/U8K7mPCXnb6tDC8k8ETCfb8OR+07OzvVBCEo+UzK3JMTAAAA0bF27Vol2v7617/Sv//7vysBfvXVV2d9Hn+fub/TgDenn346/fd//zf93//9H33wgx+07+ex58/gIx/5iDHzrb/x/PPPq7nR/PnzVaBjyJAh9t++8pWvqKAGBz/6I93d3WoOxfOaMHMo0H9AejkARPSBD3xA/ctffsxvf/tb2rRpkxLibsEtovU73/mO72vyRfWoo45SK7jbt2/P+DtHCTi1nQV5X19f1m184YUX1Bc3i3uOxp9wwgn07LPP2n/n9LKvf/3r6neO4ku6Wj41VlIPxhOxww47TAnoBx54QP3tZz/7GS1YsEAtWHA6PWcC/OUvf8la0y0p/rztV1xxBY0ePVql7fMiB2cF+NV0S1r8vffeqxY+OLuAx/nkk09WERo3N998Mx144IFq+44++mh6+umnI60T5y/Ra6+9lg466CA1NryvvELf0dHheBxPJE477TS1qMHbwp/Ppz/9acdj7r77bjWGPAlpaGigww8/nH7xi19Esp0AAGCCr+3Dhw+nM888kz760Y+q27nW8LKg4O+hCRMmqO+ok046id544428vgOkzIev/bxQztdPvjbybYYXC/g2fw/w9ZMXyd2sWrVK7RunMPPj+HVYAJv2J9s28fa8/vrr9OSTT9rfsUG+TyZOnEjHH3+8yoBzjz9v/8yZMwv6vX///fer9+DvKf4ul+9xHR47Dkjw9w9nvfH3KotkN7z/PGfiz4K/g7nczl1G5wVH5Hn7eL91wS3wZ6MfKy0tLfTVr37VTkM/9NBD1dzDPWeSucp9991HM2bMUNvGwv7VV1+153ScwcGfP39e7vHh+3h8li9fruY18j19yy23OB7HQYfFixerY40/Ez5G3v/+99Pjjz9uLCPkbeUyQ5kj8PlgquneunUrXXTRRWo8+XHjx49XizPu7eRMCZmL8Xn2xS9+MaOsQPaF34vPQT5u+Pj7yU9+EugzAoUDkW4AiOjdd99V/7KAZPgLmS+6/EWdD3JxNaV8cZSAhfsFF1yQNdr92GOPqS9DvtBzFILTtbn2ib/4WEiyoOTnv/322ypV7ec//7kSeAxPHvKB35tFLn+h8WvypINhQXj22WfTJz/5SfVFxKLx3HPPpf/93/9VE7hsfOlLX1KTPd4fHif+YuL3uOeee7I+l6PyPAZcb9/Y2Ki+THg7eIIicMkAvx5/IXKaP7/HOeeco96Tv9iigLMk/vjHP6rjhCcG/P5cYvDmm2+qz5ThBZdTTz1VfQ7f+ta31LHA28KTReHhhx9WGRY8yfnxj3+s7uPX4MnVZZddFsm2AgCAGxY//N3B0Te+BvF1k0ureME4LFyixNfis846Sy0yvvzyy+pfr7TpoN8BvKD6iU98QkXiOROLhQy/BwsiXuT8whe+oB7H196PfexjjpImFojHHXecEh18/WWRxN9n/F3AkWUW1WG2iW/zY/RSLF6EDwLvA1/PuWSNn8+LtiwSWeSbxiiq730uoePvGx4nFrq//OUvVWR9/fr19pyHx4m/K1lwf+Mb31CpzyxUWcDxAoPU+7M4ZCHH2y7jeeutt6r5UjZaW1tVCjkvPuy3335ZH8/CmucYLGg57ZyzAx988EG1yMBBEd5fHR4TnruxEJXjgRdseH9YrPL+79mzRx2jvOjN46vDf+OSQj6G+Fzg4+Tzn/+8OjdkkbypqUmlx/PfL7nkEhVU+f3vf6+Ocy7P4G3U4c+LP9vPfvazdu26aYGCPw/+DPjY4jkWzxt4XsCfkcy5eIGFFy249p23i49zOV95rqCnq/O+8GINHx+8PxwQ+eY3v6kWePiYAjHRB8AA4g9/+AMvj/Y98sgjfTt27OjbsGFD39133903cuTIvkGDBvVt3LhRPW748OF9s2fPDvy6J5xwQt+0adPUa/LPqlWr+r7+9a+r9zrzzDMdj127dq26/6c//Wlfd3d338EHH6zeq7e3V/396quvVn/n12H4fn7MaaedZj+GaW1t7TvggAP6TjnlFPs+fk1+Lr9HWA477DC1Hzr8WuXl5X2vv/56xuP5/XU6Ozv7Zs6c2feBD3zAcf/+++/fd+GFF2Z8BgsXLnTsz+WXX95XUVHRt3fvXvs+3h59mx5//HH13OnTp/d1dHTY9//iF79Q97/66qvqNv+NP9Ojjjqqr6ury37cHXfcoR7n3k8TvN3uz05n5cqV6rUuvvhix/1f+9rX1P2PPfaYuv23v/1N3f7nP//p+VqXXXZZX0NDgzoeAACgGLz44ovq2vTwww+r23w9njRpkroeueHH8XeT+zou3zVbt27tq6ys7DvnnHMcz/ve976nHpfrdwBfh/mxzz33nH3fgw8+qO7j7+z33nvPvv+3v/2tup+/J4STTz657/DDD+9rb2+37+P3XLBggfpezWWbTN+VfvDrfvGLX+zbvXt3X3V1dd+f/vQndf8//vGPvrKysr5169YV7Huf7+f3XL16tX3fyy+/rO7/1a9+Zd/Hnxs/7t1337Xv27x5c9+QIUP6jj/+ePu+r3zlK+q5L7zwgn3f9u3b+4YOHZp17iHvazq+TNx///3q8f/xH//huP+jH/2oGjd9n/hxNTU1jveX42HcuHF9TU1N9v1XXnllxrby58n3XX/99fZ9PI+YM2dO35gxY9T8huHvaH3uwezZs6dv7NixfZ/+9Kcz5nn8vc7joyN/42NOni9zQi/4NfjzOfXUU/t6enrs+2+66Sb13Ntvvz1jX+68807HvvA4fOQjH/F8D1B4kF4OBiS8UsgrwZyyxGnevOrMkUleDZfVTFPqkx+cwsavyT+ckv7Tn/5UrdL6uZxKtJsjApz+ZYLrvt955x21Sr5r1y5l0sY/nHbFkVE2dAua2pULnM7G6Vpu9JVtXlXliDOvlK9YsSLQ6/LKr24gx89l19L33nsv63M5DUuv9+bnMmvWrLHTuXmseCW6sjKd0MPRcI5iRAHXozEcpdDhiDfzj3/8Q/0rWQ6cAdDV1WV8LX4Mf568sg0AAMWKcnOUliOXDF+PzzvvPJW1FNZBmiOYHP2UqLPAkbt8vwP4+4dThQWJunLEV4+Yyv3yPbB7924VzeRIH0ck5buTvxs4MsnfqxwxzWWbcoG/ezj6yFFphlPNOZWZjbUK+b3P8x1Obxa4swpHtGWceP/YtJSj/1yOJXCKM78/R8p5TiTfe8cee6yKsgs85+Hv1mzIawSdW/F78Rzpy1/+csZ3LOtsro/X4XGRqLB+PHAUWX9P93Ei8FyBsykEnmPwbY46c9o5w9sjcw8efz7G+LjntHjT3IffO1u2Ic+l+DW5ZILnUiYeeeQRlVHIde+6MS3PcfizlPmGwHNa3Z+HX58/M/c+g+IyoEU3X7Q4RYnrIvgi6yV6vOAThGsu+MLEKTacVuKuh+KUHj4ZeVItj3G7RoPiw7W+LHA4bYnrXvhCxF/CAl/E+Es6DHyx59fk9CdOZWIBz7Vg2Qwz+MuKa428arv5i5e58MILbVEvP5zmxPXDLHgLBdc1mWARyV++vH+cMsXbw6lOQbfFnV4mYtjrSyfMc2WC5Hbh5S9V/Us5H/g9+MvP/R7jxo1T57tsAy9a8Bcvp4Vx6h9fMzjlTK/75okq1/dz2henvnMqm6nmDoCBTL7f2ZzmyfWinGLJ1wIWGW7YOZmFBp+PfH7zJLc/wkKLxTULbvYy4RRu/mFBwq0yWUSHweuay98NXgudQb8D3I/jWlqGF81N98vzeX/4O/W73/1uxnenmMW5/Vby+V4KAh9bkjbMxy/fNhHl974plZv3S/aJ5ymc+s310qZOJywuN2zYYH/O7DbuxvRcNzyvYoLOrfi9+Fx3i3TeJvl7FMeJwO/F83Qdvg4wem01l5TxwgXPfTg9nz8TFr2mz8Nr/qTDaedcVsaLCLwIxun3nALPqfz6WJjGmcU0L5S4x4LnEe6uOPpnDuJhQNd084rh7Nmz1QQ3F/dodp3kE4/rJPhEYRHC9bl8Qkt/X/7C4bofjnzyycGP4SjdmDFjHCIPFBde8RP3chP8efFKM68sBnXQ5os1rygLXEd25JFHqpozrqHKFu3mySC7p7uR1WyOnLvrhfRVzUJhqtXi2imO4vOXAy8w8MIT1xOxmHQbxXjh5XwbxFQun+dGTbZ2b/x3rqdiQ5r/+Z//UYsyfM25/vrr1X382fH1gI83/ht/8fIPjyVfT/gLHgCQ/3c2C02+nnHkzMspmsUMT6L5muyuGe1PcASYFxhYePOPGw4gsBdFIQl6Hfd6XLbny3cne394zbfciwSF/m7h700WWSym+VjjKLyJKL/3k/J9yWPNi11ibhY1uR4nYfh//+//qbkaL9hxbTl/d/Prc/24eAPpBKl1Z3hxjxcUeSGG5wG8UMSvyefpEUccEXo7k/KZAycDWnRzVMnPUIAviCyYORWI3QHZDZBXo8Sp0t1HkA0yOEWHo9siut2ulvwYnkRzug5Ed3Lhi9/SpUvVxIwNM3KBF2Q4vYfNSPhL3884hB/HDqAcDeUvZR1JC+NVYl3Um4iq33c2eFx4lZe/HPSWYCwUk4Ck63GkQ1InGU4D4xVr/myieA+eGHFEQlbeGY4S8fXCnTLIWQH8w67rvDDBGQ482WUzNoYXd/i44x9+XY5+87HDX75B+uYC0N/J9zubF0Y5G4dh4yG36y/DmTDSNeD222+n/gqLahYMnPXlhucwXG7FRmVBRYN+zdWje5waHVd0TVKleUE423dnGPL5nuXxZMHG4o2PZTE+c1PM731eZGKHazbmMpXNccaHRIv5c5YovI7puW74PbgkgIUkR87dEWg3/F6cVs2RcT3azdskf4+SzZs3q4U9PdrNJnWMZMjxAjofV3yO6OMepM1eNvgz59R5/uEx5sUWXpznY0X2lcdZLwHgwBBnqkR5fIPCMaDTy7PBjpUsvHhi/MorryhnZq7HMV1wBE4v4ei21woTp2zxScMRQpBcuI8kR2/54icXXR1OSWORnA12zeQ6Xm495odEuzna6W5lws6lfDFmx1Z2PXWjtzORLwvTZDJKeHv5C0ev+2MxGzbds1BwFgOnfXEvdRba+kQzqgkgu5yKm62OfNbi4M7v515dlsiFpJjzxFSHJzmyMOBuPwYAiO47eyDS1tZmBwe484L7h8eRhY77u8gPrqflKKYsagg33XQTxQUvKvCCCy9eclTfjbs9WVD4ezaf71hehGeRxguqXhTze5+/zzmrgTPt9DRqXkDmBeL3ve99dmo4f+9xhhY7devbErTVHO83fx9+6lOfMu4X105Ldhe/F88x3McQZ6Dw/CNqF26eK/Cxogtavs2LEvx56BFk/Tudu5bwdSdXOLXf7V7Pnz0vNMj3P4tqXpjnrEn9vdk5nXVHkI4xIH4GdKTbD6634agd/8t1HnKh5DpLvv+HP/xhxnO4vQBb9+snLcMnBNf38snDJyyn455yyilF2xcQHq594ZV+vuizQOJItFx02SyDIym6sYsXbADDr8E1WPwFK+05THDkk3s+s/B2CzB+Pn/BcH9GLk/g44kNYLgmnb8MOW2ZkW3kaA8bxPEKP0dO3XVK+cIXeBaXPKHlmjRehOCICUdkebIbN/zlxO012MSHV9Y5hY8nE2xqx19mQSMDHLUxLa5wuhePAacIcrsUnuxw7TZPRHjCwJEMibDzbT7nuTUNvzdPZnkxgD83Ee4c7WZDFt5WrsXi+qxf/epX6tjTo+gAgOi+swcqLKb5OuTOqhI4I4eFBgspNlYLApfYcSYfR+b4dfm7gQ1CuVSGo7nFysJyw99LLBq5jp9NpzhKyGKSRdLGjRvVNoaFv2d5cYG/G/g7j8U9X7uDwiUS/ONHsb/3eV+41pzHirOseAGF57I8b9X7O3MggX2J+PPlz1tahnEkNsh3PxvH8WfC78FlfCy+uUacj0f2SeJjU75zeR/4e5T3i7+/ecw4m5QXBzgdWzeHiwK+bnBmDL8X13Jzmziej/H+STsuXqjiBSv+Puc5AEeZOSOE53qmRYQgcGCHF614nsKvw2PP808+TvnzZPh85JZ8nA3JY8/nGAfweG7B7f100zSQXCC6PeCaE15hExMFgS9AJuHEF0G+KPJkmi+QOrxaxScun5Ac6Wa3Y77wu1PPQbJgQ5nXXntN1VSxSQZ/0fAXIYsg7k/J0YAgcN0PP59FFAtBL/hCy9FuPo7c8LHCkwQW5bzqy8cSG3bxNupum3zx5cfwlwBPNjlNmb8UohbdPMHgFVbul81ffpxOKF9WSRDdDH8+vCLMk0CefPMXNn+hcz1nNnM7gb/UTNEI7hnKX7g8KeJzmcU8f0nyZ8JfjHqqmYhxjr7xlyh7PrCnAE9oJQ2TvzD5i52/QFnA8+vwZJePF92pFAAQzXf2QIavPXwN9Fr852sOX9/4cZyFE3T8+DuAU4h5HsRpwbwwzSKJhVzQa27UsIjhbhYsVvg6zfvDIpkXThcvXpzTa/LzeGGUxSiLRb7GhxHdQSnm9z7PW9mrhb+/uJaYX4Pfh1Obxe2b4QxAnu/ygjZ///OxwZmBLFj5ezEIvO28zfzdfOedd6pIOdenswcOL5CJgOTjkL+zebxZAPPfOM2b52TSJSTqYAsvkvO+8THMC0k87rxYI3A9Nxuc8YIEl9fx8cVjxP3WedEgFzjNnssYWR/wPJPngrwgwYE8NmEVeD7A4pu36fLLL1dZtey2zwuKeo9ukFzKuG9Y3BuRBHgVlifN4mbKJzhHHrlZvduQgC8OfOETnnzySTvyxydANjiqxfUsfMICAIoHTyT4S4tNmPhLFQAw8L6zZfLMC1x+JTEsejjbxF1CAoLDY8xihqOXHLEEIInwuc4t2TjQAkChQKTbA14F5VVzTpuVHsAmeGWL0014hTeI4JaJP+o0ASgsXCPFJm96WiOvqnMaN7JMABiY39mgsLXibuM1WbDANRcAMNAZ0KKbU3W4ZlPgdBxOA+eUDU5R41VzbtnDKTD8hc4pMJz+wQZHHNnmFBsW3FzXwikg0lOP60nFTI3TdNjUiWtPWGgvWbJEpY+4zUYAANHCZi+cgsVmSpwCx7X4nBLPjsZ8HwBgYH1nM2+88YYySOLFN04NFg8NvS2T3Mfvx6/Bt/l7nVNJgTecbcAp3OxVwdkF3KWF/U/YpItbaAIAwICmbwDz+OOPc2p9xs+FF16o/t7Z2dm3ePHivilTpvRVVVX1jR8/vu9DH/pQ3yuvvKL+zo8zPf+EE06w3+Oqq67qmzp1al9tbW3f8OHD++bPn9939913x7bPAAwU1q5d23fWWWf1jR07Vp2//O9FF13Ut23btrg3DQAQw3c2s//++xtfQ8f0d34e8Gf58uV9J598ct/IkSPV+E+aNKnvsssu69u3b1/cmwaALzxvP+yww+LeDNDPQU03AAAAAAAAAABQIGCLCwAAAAAAAAAAFAiIbgAAAAAAAAAAoEAMOCM1dg7fvHmz6p2tuxoDAAAASYCrvtjki3vfDvQ+7fjOBgAA0B++swec6OYvb25EDwAAACSZDRs20KRJk2ggg+9sAAAA/eE7e8CJbl4tl4FpaGjI+/W6urrooYceUi0xqqqqItjCgQHGLTcwbrmBccsdjF3xx62pqUkJTfm+GshE/Z3d37MCuMXZ6NGjB3yGBPAGxwkIAo6T6L+zB5zolvQ0/vKOSnTX1dWp18KENDgYt9zAuOUGxi13MHbxjRvSqaP/zu7vk+T29nY1TpgkAy9wnIAg4DiJ/jsbowgAAAAAAAAAABQIiG4AAAAAAAAAAKBAQHQDAAAAAAAAAAAFAqIbAAAAAAAAAAAoEBDdAAAAAAAAAABAgYDoBgAAAAAAAAAACgRENwAAAAAAAAAAUCAgugEAAAAAAAAAgAIB0Q0AAAAAAAAAABQIiG4AAAAAAAAAAKBAQHQDAAAAAAAAAAAFAqIbAAAAAAAAAAAoEBDdAAAAAAAAAABAgYDoBgAAAAAAAAAACgRENwAAAAAAAAAAUCAgugEAAAAAAAAAgAIB0Q1yYndLJy1/b0/cmwEAAAAAAAAAiQaiG+TE5fespI/85jl6c0tT3JsCAAAAAAAAAIkFohvkxLamdvXvVutfAAAAAAAAAACZVBruAyArPb196t9e618AAAAAAAAGEmXXlFF/pJzKaW7DXFretJx6qZf6I31XF1fDININcqK3r88hvgEAAAAAAAAAZALRDXJCtLaIbwAAAAAAAAAAmUB0g5yQCHdP/8w4AQAAAAAAAIBIgOgG+aWXI9INAAAAAAAAAJ5AdIOcEAM1GKkBAAAAAAAAgDcQ3SAnJMINIzUAAAAAAAAA8AaiG+SEaG2klwMAAAAAAACANxDdICeQXg4AAAAAAAAA2YHoBjkBIzUAAAAAAAAAyA5EN8gJqeVGpBsAAAAAAAAAvIHoBvnVdEN0AwAAAAAAAIAnEN0gz/TyuLcEAAAAAAAAAJILRDfICaSXAwAAAAAAAEB2ILpBTsBIDQAAAAAAAACyA9ENcgI13QAAAAAAAACQHYhukBNILwcAAAAAAACA7EB0g9DoQhvp5QAAAAAAAADgDUQ3yLmeW/2OSDcAAAAAAAAAeALRDUKjR7cR6QYAAAAAAAAAbyC6QWh6e9O/92i/AwAAAAAAAABwAtEN8kov70OkGwAAAAAAAAA8gegG+aWXo6YbAAAAAAAAADyB6Aah6dPTyxHpBgAAAAAAAABPILpBaHShDfdyAAAAAAAAAPAGohuERk8pR6QbAAAAAAAAALyB6Aah0c3T4F4OAAAAAAAAAN5AdIPQIL0cAAAAAAAAAIIB0Q1Cg/RyAAAAAAAAAAgGRDcIja6zEekGAAAAAAAAAG8gukFoEOkGAAAAAAAAgGBAdIPQ6EJbF+AAAAAAAAAAAJxAdIO83Mt7EekGAAAAAAAAAE8gukFo9DZhiHQDAAAAAAAAgDcQ3SC/mm706QYAAAAAAAAATyC6QWj0lHKklwMAwMDgqaeeorPOOosmTJhAZWVldP/99/s+/q9//SudcsopNHr0aGpoaKD58+fTgw8+WLTtBQAAAJICRDcIjS60kV4OAAADg5aWFpo9ezbdfPPNgUU6i+4lS5bQ8uXL6aSTTlKi/aWXXir4tgIAAABJojLuDQClhy60EekGAICBwRlnnKF+gnLjjTc6bv/whz+kv//97/Q///M/dMQRRxRgCwEAAIBkgkg3CI0e3EakGwAAQBB6e3tp3759NGLEiLg3BQAAABg4ke7rrrtO1XytWrWKBg0aRAsWLKAf//jHdOihh/o+77777qPvfve7tG7dOjr44IPVcxYtWlS07R7oIL0cAABAWH72s59Rc3MzfexjH/N8TEdHh/oRmpqabMHOP8AbHh9u6YlxAn7gOImW8n4av+T9KqOyfrt/TFTnQNDXiVV0P/nkk/TFL36RjjrqKOru7qZvf/vbdOqpp9Ibb7xB9fX1xuc899xzdP755yvB/i//8i9011130TnnnEMrVqygmTNnFn0fBiK9SC8HAAAQAv6uvuaaa1R6+ZgxYzwfx9/t/Dg3O3bsoPb29gJvZWnDE7/GxkYlqMrL++9EGeQHjpNomdswl/ojLLin1k1Vv/dR/5zrb9++PZLX4QyuxIvuBx54wHH7jjvuUF/GbLhy/PHHG5/zi1/8gk4//XT6+te/rm5fe+219PDDD9NNN91Et9xyS1G2e6DTg0g3AACAgNx999108cUXqyy1hQsX+j72yiuvpCuuuMIR6Z48ebLtgA78xRS7yvNYQUwBL3CcRMvypuXUH5EI94qmFdRL/TMrYozPAnAYamtrS89IjVfeGL96r6VLlzq+kJnTTjsta+sSEB16FkUPNDcAAAAP/vM//5M+/elPK+F95plnZn18TU2N+nHD4gACITsspjBWIBs4TqKjvwpSiXDz/vXXfSyP6PgP+jqVSVp5+8pXvkLHHXecb5r41q1baezYsY77+DbfH6Y+rKurS/3ki7xGFK9VKnR2p/e1p6c3p30fiOMWBRi33MC45Q7GrvjjltSx5nrs1atX27fXrl1LK1euVAvl++23n4pSb9q0ie688047pfzCCy9UGWrHHHOM/T3NHi5Dhw6NbT8AAACAYpMY0c213a+99ho988wzkb6uV33YQw89RHV1dZG9D6e4DxTe2FNGRBXq972NjaoHa64MpHGLEoxbbmDccgdjV7xxa21tpSTy4osvql7bgmSdsbDm8rAtW7bQ+vXr7b/feuutyq+Fv9/5R5DHAwAAAAOFRIjuSy+9lP73f/+XnnrqKZo0aZLvY8eNG0fbtm1z3Me3+f4w9WFs2BZFfRhHJHhSdcopp1BVVRUNBGrf2kG06iX1++DBQ2jRogWhX2MgjlsUYNxyA+OWOxi74o+bZGQljRNPPFGZL3nhFtJPPPFEEbYKAAAASD6xim7+8v7Sl75Ef/vb39SX8wEHHJD1OfPnz6dHH31UpaILPLHh+8PUh/EkKMoJZNSvl2TKy1NRboarPPLZ74E0blGCccsNjFvuYOyKN24YZwAAAKB/Eavo5nQzrvniFiJDhgyx67241otrvpgLLriAJk6cqNLEmcsuu4xOOOEEuv7665UpC5uzcMobp7GB4qA7lsO9HAAAAAAAAAC8idW28De/+Y1yLOeUtfHjx9s/99xzj/0Yrg/jOjFhwYIFSqizyJ49ezb95S9/Uc7l6NFdPPTe3NDcAAAAAAAAAJDg9PJsmGrCzj33XPUD4hfdiHQDAAAAAAAAgDdo0AdCg/RyAAAAAAAAAAgGRDfIM70cohsAAAAAAAAAvIDoBqHpZctyC0S6AQAAAAAAAMAbiG4Qmh5EugEAAAAAAAAgEBDdIDS9qOkGAAAAAAAAgEBAdIPQ6DobohsAAAAAAAAAvIHoBnmml8e6KQAAAAAAAACQaCC6QV791RHpBgAAAAAAAABvILpBfn26YaQGAAAAAAAAAJ5AdIO8RLduqgYAKD7tXT20p6Uz7s0AAAAAAAAeQHSD0OjBbUS6AYiXC36/jI778WPU2NoV96YAAAAAAAADEN0gNLrQ5l/1Gm8AQHF5Z/s+au3soc2NbXFvCgAAAAAAMADRDULjNk+DmRoA8SHnH85DAAAAAIBkAtENQuOObCPFHID4EK3dDdENAAAAAJBIILpBaHp6nbd7XbcBAMUDkW4AAAAAgGQD0Q1C445sI9INQHzI+QfRDQAAAACQTCC6Qf7p5ZjsAxAb0ravGyknAAAAAACJBKIbhMYtsuFeDkB8ININAAAAAJBsILpB/unlmOwDEAu84CWnI85DAAAAAIBkAtENQuMObKOmG4B40IU2RDcAAAAAQDKB6AahcU/uUUoKQDzoC15oGQYAAAAAkEwgukFoeuFeDkAi0Be8EOkGAAAAAEgmEN0gZ7dkr9sAgOKASDcAAAAAQPKB6AahgZEaAMlAP/ew+AUAAAAAkEwgukFo3HN7pJcDEA+60EakGwAAAAAgmUB0g9AgvRyAZKAvePXA0RAAAAAAIJFAdIPQuNPJEekGIB4Q6QYAAAAASD4Q3SD/9HJM9gGIBX3BCxknAAAAAADJBKIb5N0yDFmtAMSDvuCFSDcAAAAAQDKB6AahQXo5AMkAfboBAAAAAJIPRDfIO9KNyT4A8dCtqW5EugEAAAAAkglEN8g/vRyRbgBiQT/3sPgFAAAAAJBMILpB/unlmOwDEAs9SC8HAAAAAEg8EN0gNO65PVyTAYgHGKkBAAAAACQfiG4QGrfIhpEaAElIL0cbAQAAAACAJALRDULjFtlIawUgHvRzT081BwAAAAAAyQGiG+SfXo5INwCxL4Ah0g0AAAAAkEwgukH+6eWY6wMQ+7mImm4AAAAAgGQC0Q1Cgz7dACQxvRznIQAAAABAEoHoBqFxT+6RXg5AEtLLcR4CAAAAACQRiG4QGkS6AUgGehk3zkMAAAAAgGQC0Q1CAyM1AJIX6UZNNwAAAABAMoHoBqFBejkAyTNSQ6QbAAAAACCZQHSDCNLLY9sUAAY0MFIDAAAAAEg+EN0gZ9FdUV6Wuo3JPgCxACM1AAAAAIDkA9ENQiOR7UpLdOsTfwBAXH26kXICAAAAAJBEILpBzhP96orU4YMIGwDxgEg3AAAAAEDygegGOaeXV1ZY6eWIdAMQC7rQhns5AAAAAEAygegGOUfXqhDpBiBW9AUvnIcAAAAAAMkEohvknF4O0Q1AvOidA3AeAgAAAAAkE4huEBqZ21chvRyABBmp4TwEheWpp56is846iyZMmEBlZWV0//33Z33OE088QUceeSTV1NTQ1KlT6Y477ijKtgIAAABJAqIbhKYnI9Id8wYBMECBkRooJi0tLTR79my6+eabAz1+7dq1dOaZZ9JJJ51EK1eupK985St08cUX04MPPljwbQUAAACSRGXcGwBKD4lsi+hGpBuAeNCFNkQ3KDRnnHGG+gnKLbfcQgcccABdf/316vb06dPpmWeeoZ///Od02mmnFXBLAQAAgGQB0Q3yEN1Wn25M9gGIBRipgSSzdOlSWrhwoeM+Ftsc8faio6ND/QhNTU3q397eXvUDvOHx6evrwzgBX3CcREt5P00a5v0qo7J+u39MVOdA0NeB6AahkXTyShipARAraBkGkszWrVtp7Nixjvv4NgvptrY2GjRoUMZzrrvuOrrmmmsy7t+xYwe1t7cXdHtLHZ74NTY2KkFVXt5/J8ogP3CcRMvchrnUH2HBPbVuqvq9j/rn/GL79u2RvM6+ffsCPQ6iG4SGL9QMjNQASFJ6OaIWoPS58sor6YorrrBvs0CfPHkyjR49mhoaGmLdtlIQU2xwx2MFMQW8wHESLcubllN/RCLcK5pWUC/1z/nFmDFjInmd2traQI+D6AahQZ9uAJIB0stBkhk3bhxt27bNcR/fZvFsinIz7HLOP25YHEAgZIfFFMYKZAPHSXT0V0EqEW7ev/66j+URHf9BXwdnG8jfvRyRbgBiAX26QZKZP38+Pfroo477Hn74YXU/AAAAMJCA6AahEY1tp5djsg9A7JFu1HSDQtPc3Kxaf/GPtATj39evX2+nhl9wwQX24z/3uc/RmjVr6Bvf+AatWrWKfv3rX9O9995Ll19+eWz7AAAAAMQBRDcIjUTU0kZqMW8QAAMUtAwDxeTFF1+kI444Qv0wXHvNvy9evFjd3rJliy3AGW4X9o9//ENFt7m/N7cOu+2229AuDAAAwIADNd0gNJJOXo0+3QDECkQ3KCYnnniibaRp4o477jA+56WXXirwlgEAAADJBpFukLd7OSb7AMQDjNQAAAAAAJIPRDfIP70ckW4AYgF9ugEAAAAAkg9ENwgd5Za5vZ1ejsk+ALGgL3gh0g0AAAAAkEwgukEo9KC27V6OSDcAsaAveEF0AwAAAAAkE4hukHNkDe7lAMQL+nQDAAAAACQfiG4QCn1iXwX3cgAS1Kcbq18AAAAAAEkEohvknl5eDvdyAOJEP/f4V792TgAAAAAAIB4gukHO6eVVlXAvByBO3OceFsAAAAAAAJIHRDfIOZ3VTi/HRB+AWOjpcZ57aBsGAAAAAJA8ILpBKHSBLe7liK4BEA+IdAMAAAAAJB+IbhAKGKkBkBzcWSaIdAMAAAAAJA+IbhAKfU5fASM1AGIFkW4AAAAAgOQD0Q1CIVFtFtwVZZboxjwfgFhwi2yIbgAAAACA5AHRDUIhk3oOckukG0ZqAMSDu7QDohsAAAAAIHlAdIOcJvnlZWVUjvRyAGLFfe519/bGti0AAAAAAMAMRDcIhczpnenlEN0AxEGPS2NjAQwAAAAAIHlAdINQ9GiRbsu8HOnlAMQE0ssBAAAAAJIPRDfIMb08JbwZRLoBiAcYqQEAAAAAJB+IbhAKiWpzermIbkS6AUhGpBt9ugEAAAAAkkesovupp56is846iyZMmEBlZWV0//33+z7+iSeeUI9z/2zdurVo2zzQcaaXI9INQJwg0g0AAAAAkHxiFd0tLS00e/Zsuvnmm0M976233qItW7bYP2PGjCnYNgKzkRo7l6fdy+PdJgAGKhDdAAAAAADJpzLONz/jjDPUT1hYZA8bNqwg2wSCpbOyc7m4lyO9HIB4QHo5AAAAAEDyKcma7jlz5tD48ePplFNOoWeffTbuzRlQSCRNGalZRw/SywGIB0S6AQAAAACST6yR7rCw0L7lllto3rx51NHRQbfddhudeOKJ9MILL9CRRx5pfA4/jn+EpqYm9W9XV5f6yRd5jSheqxTotPaTRXdfb4/6vaenN/T+D7RxiwqMW27013HrlnoPi47OaK5rA2HsCk0+44axBgAAAPoXJSW6Dz30UPUjLFiwgN599136+c9/Tn/605+Mz7nuuuvommuuybj/oYceorq6usi27eGHH6aBwNp9/P9Kamtro2XPP69+39fcQkuWLMnp9QbKuEUNxi03+tu47dlTQUSpMg9m6fMv0O5VhYl297exKxa5jFtra2tBtgUAAAAA8VBSotvE0UcfTc8884zn36+88kq64oorHJHuyZMn06mnnkoNDQ15vz9HJHhSxanuVVVV1N958b09RK/9k4YMrqfjjptJv3h9GdUOGkSLFh0f6nUG2rhFBcYtN/rruN2ydilRi1oJU8w96ig6/uBRkb5Hfx27QpPPuElGFgAAAAD6ByUvuleuXKnSzr2oqalRP254EhTlBDLq10sqZeUVdp/uGmt/+6gs530fKOMWNRi33Ohv45YR0y4rL9j+9bexKxa5jBvGGQAAAOhfxCq6m5ubafXq1fbttWvXKhE9YsQI2m+//VSUetOmTXTnnXeqv9944410wAEH0GGHHUbt7e2qpvuxxx5TqeKgOIhTuaNPN8ybAIgF97kH93IAAAAAgOQRq+h+8cUX6aSTTrJvSxr4hRdeSHfccYfqwb1+/Xr7752dnfTVr35VCXGux541axY98sgjjtcAhUXm9Cy4WXgzcC8HIB7c5x7a9wEAAAAAJI9YRTc7j/f5CDYW3jrf+MY31A+If5KvR7ox0QcgHuTcq64op86eXkS6AQAAAAASSEn26QYJSC8v52h36j5EugGIBzn3qipQ6gEAAAAAkFQgukEoeq1JfkWZll6OiT4AsSBtuqsrU5dyRLoBAAAAAJIHRDcIhQjs8nKklwOQlPNRRDfORQAAAACA5AHRDXKKdHOUG0ZqAMSLnHuIdAMAAAAAJBeIbpCbe7nDSC3ebQJgoCKR7SrLYKEHJyMAAAAAQOKA6AY5ppen2oap+xDpBiDeSLcluhHpBgAAAABIHhDdIP/0ckz0AYgFOfdqrPRynIsAAAAAAMkDohvk5l6uGamp+zHZByC+Pt0Q3QAAAAAAiQWiG4SixyoZ5Sg313Xb9yPFHIAY+3QjvRwAAAAAIKlAdIOcImsc5Oa6bgERNgDi79ON8xAAAAAAIHlAdINo0ssR6Qag6MBIDQAAAAAg+UB0g5wm+WWakZq6H5N9AIqOnHcS6Ya3AgAAAABA8oDoBjn36dZFN9oDA1BcdIEtohuRbgAAAACA5AHRDXKa6LvTy2GkBkBx0c+5dMswrH4BAAAAACQNiG6QUzorB7k1zY30cgCKjH7OoaYbAAAAACC5QHSDnI3UUnXdzvsBAMVBP+dQ0w0AAAAAkFwgukFuotuq55YUc0S6AYgx0o2abgAAAACAxALRDULRY5WMcpSbETM1iG4Aiot+zlVZ6eU4DwEAAAAAkgdEN8gxvZwcke6408shNsBAA5FuAAAAAIDSAKIb5Oxerv5NQKT7rys20qzvPUjPvLMztm0AIC73cj4Fq8pR0w0AAAAAkFQgukGOE30rvdyOdMe3TUvf3UUtnT20/L098W0EAEVGuoPxwpech4h0AwAAAAAkD4huEAqZ07uN1OJML+/oTqkP9CgGA3EBjAV3JQwNAQAAAAASC0Q3yCu9PAlGah3dPepfRPnAgDwXy8rs87EbC08AAAAAAIkDohvkXEeqG6rFK7ol0g3RDQYOcrxXOCLdMW8U6PfcfPPNNGXKFKqtraVjjjmGli1b5vv4G2+8kQ499FAaNGgQTZ48mS6//HJqb28v2vYCAAAASQCiG+TXp7ssAenlXSml0dUD0Q0GYHp5WdpbASUWoJDcc889dMUVV9DVV19NK1asoNmzZ9Npp51G27dvNz7+rrvuom9961vq8W+++Sb9/ve/V6/x7W9/u+jbDgAAAMQJRDfIL728PDnp5RAcYKCeixLpRokFKCQ33HADXXLJJXTRRRfRjBkz6JZbbqG6ujq6/fbbjY9/7rnn6LjjjqNPfOITKjp+6qmn0vnnn581Og4AAAD0NyC6QSgkfbUsgUZqEBxgIEa6+RyU8xAlFqBQdHZ20vLly2nhwoX2feXl5er20qVLjc9ZsGCBeo6I7DVr1tCSJUto0aJFRdtuAAAAIAlUxr0BoETTy63lmnSf7vi2CTXdYCAixzubGVZafbpxDoBCsXPnTurp6aGxY8c67ufbq1atMj6HI9z8vPe9733U19dH3d3d9LnPfc43vbyjo0P9CE1NTerf3t5e9QO84fHhccY4AT9wnERLeT+NX/J+lVFZv90/JqpzIOjrQHSDnES3uJYnKb28UJHufe1dVF1ZTjWVFQV5fQDy6tOtIt2p3yG6QZJ44okn6Ic//CH9+te/VqZrq1evpssuu4yuvfZa+u53v2t8znXXXUfXXHNNxv07duyAAVuAiV9jY6MSVJyFAIAJHCfRMrdhLvVHWHBPrZuqfu+j/jm32O7hRxKWffv2BXocRDfIS3QnyUitEIKjtbObjv/J47TfiDr6+6Xvi/z1AcjfSI1Fd2rihBIL4J5cP/nkk/T000/Te++9R62trTR69Gg64ogjVFo4u4kHZdSoUVRRUUHbtm1z3M+3x40bZ3wOC+tPfepTdPHFF6vbhx9+OLW0tNBnP/tZuuqqq4wT/iuvvFKZtemRbt5O3u6GhoYQez8wP28u/eKxgpgCXuA4iZblTcupPyIR7hVNK6iX+mdWxJgxYyJ5He7mEQSIbhAKSSNPlpGauJdHf1HY3tRBe1q7qKUz2CoWAPG2DIPoBkRtbW10/fXX029+8xvavXs3zZkzhyZMmKDadnG0+f7771eGaGxstnjxYjr22GOzvmZ1dTXNnTuXHn30UTrnnHPsyTvfvvTSS43PYZHvntSzcGc4ymaipqZG/bjh14FAyA6LKYwVyAaOk+jor4JUIty8f/11H8sjOv6Dvg5EN8jJMdma46fTWvuS4F4e/TZ0Wzm83WiADBLrr5A2UkOkGzCHHHIIzZ8/n373u9/RKaecQlVVVRmP4cg3t/T6+Mc/rqLOLMKzwRHoCy+8kObNm0dHH3206sHNkWt2M2cuuOACmjhxokoRZ8466yzleM6RdUkv5+g33y/iGwAAABgIQHSD3NLLy13p5TFN9jla0llA93J5Tf6H91H2G4DkGKmRHemO6zwEyeKhhx6i6dOn+z5m//33V6ncX/va12j9+vWBXve8885TtdUcHd+6dauKoD/wwAO2uRq/jr7i/53vfEdF1PjfTZs2qXRWFtw/+MEP8txDAAAAoLSA6Aa5tSlKiJEai2J564JEunvSr9nV20s15YjOgOT16ZbzUDIzwMAmm+DW4Sj4QQcdFPjxnErulU7Oxmk6lZWVdPXVV6sfAAAAYCAD0Q1yTC9PhpGa1HMXOtKtfu/poxqcMSCBRmqo6QZ+sOv3K6+8opxa3a1Nzj777Ni2CwAAABgoQEKAUMicXiJrIr7jKnnu6ErVcxeq7rpHm6DqUW8AkmSkhppu4AWnf3OtNffLdsOp39x7GwAAAACFBbaFIMf08tTt8piN1Aoe6XallwOQRCO1SutERE03cPOlL32Jzj33XNqyZYuKcus/ENwAAABAcYDoBrmll4uRWswGTrroLkRqrf6aiHSDJCGJHZxtIotfiHQDN9xHm13HxewMAAAAAMUHohvk5l5e5k4vj0t0a+nlBdiGLu01C9EHHIBo+nSnLuWo6QZuPvrRj2YYnAEAAACguKCmG4RCdKdEuOXf2NLLu/RId4FruiFoQBLTy8tQ0w28uemmm1R6+dNPP02HH354Rs/uL3/5y7FtGwAAADBQgOgGOUa6KRF9uh013QVI/9ZfsxBGbQDk3ae7HH26gTf/+Z//qfp219bWqog3m6cJ/DtENwAAAFB4ILpBfunlcUe6C5xerqfrdqGmGyTUSA2RbuDFVVddRddccw1961vfonIp/gcAAABAUcE3MMi5jjQRkW5Henlha7q74V4Okhjp1tLLUdMN3HR2dtJ5550HwQ0AAADECL6FQSgkoC2Rbtu9PKa5vrNlWGFrumGkBpJrpCaRbhyjwMmFF15I99xzT9ybAQAAAAxokF4OcqwjdaWXJ8C9vKfANd1ILwdJN1Lj07Cvr89RtwsGNtyL+yc/+Qk9+OCDNGvWrAwjtRtuuCG2bQMAAAAGChDdIBRSuy1p5RVlTgEQb6S7AKIbfbpB0vt0azXdqfv7qFJOTDDgefXVV+mII45Qv7/22muOv2FxBgAAACgOEN0gFBxFY2SOH3uku6uwRmr6a3YhdRckdAFMF918zFZWxLhhIFE8/vjjcW8CAAAAMOBBTTfIK71cIt7xuZfrLcMKUNOtvSYi3SBJ9DpqutOXcpipAQAAAAAkC4huEIoeLyO1BPTp7il4ejki3SCZC2CO9PKYFsBAcvjc5z5HGzduDPRYNln785//XPBtAgAAAAYyOaWXb9iwQdWCTZo0Sd1etmwZ3XXXXTRjxgz67Gc/G/U2ggSml1eUu9PLqV/26Xaml0PMgCQaqaUXvwplKAhKi9GjR9Nhhx1Gxx13HJ111lk0b948mjBhAtXW1tKePXvojTfeoGeeeYbuvvtudf+tt94a9yYDAAAA/ZqcIt2f+MQn7DqxrVu30imnnKKE91VXXUXf//73o95GkNDewIlILy9wn279NRHpBkmNdGuauyCLT6C0uPbaa+ntt99WovvXv/41HXvssbTffvvRmDFj6NBDD6ULLriA1qxZo8T2888/r1zNAQAAAJCwSDc7oB599NHq93vvvZdmzpxJzz77LD300EMqrW3x4sVRbydIquhOUHo5i42o2yXpddyo6QZJNVLjY557dfM5gJpuwIwdO1YthPMPR7fXr19PbW1tNGrUKDrooIPgXA4AAAAkXXR3dXVRTU2N+v2RRx6hs88+W/0+bdo02rJlS7RbCBKFBLRFbJfHbqSWTi8vRLukHs2xHO7lIKlGavIvi+5uHKfAxfDhw9UPAAAAAEoovZxrxW655RZ6+umn6eGHH6bTTz9d3b9582YaOXJk1NsIEoSI63SkmxIT6S5Eaq1ex41IN0gSep9uhiPdDDQ3AAAAAEA/EN0//vGP6be//S2deOKJdP7559Ps2bPV/f/93/9tp52D/omI6+T06XYqjKi3Q3+9LtR0g4Sml+vnIiLdheG6JW/ShbcvQ/o+AAAAAIqTXs5ie+fOndTU1ORIWWPn8rq6ulxeEpSaY3Ji+nT3FDTS7ajpxmQbJDi9XCLdEIWF4a4X1tO+jm5au7OFpo4ZHPfmAAAAAKC/R7rZjKWjo8MW3O+99x7deOON9NZbbyl3VDAA0su1OtIkpZdHLTj0qCHcy0GSkEWgdKlH6nKOxaHC0Gmd/8gkAAAAAEBRRPcHP/hBuvPOO9Xve/fupWOOOYauv/56Ouecc+g3v/lNLi8JSgSZb8pEP34jNVdNd8TC2NGnGzXdIJFZJ6nbiHQXFrkWlIq3w/bt233/3t3drVp9AgAAACChonvFihX0/ve/X/3+l7/8RbUm4Wg3C/Ff/vKXUW8jSOJE39UyLK4gcKHTy3sc6eWIcIFk9ul2noulIQpLCW5FKONaKt4O48ePdwjvww8/nDZs2GDf3rVrF82fPz+mrQMAAAAGFjmJ7tbWVhoyZIj6nXtzf/jDH6by8nI69thjlfgGA2GiTw5Dtd5+aqSmi/hSiXCBgYEc6+4FMKSXR4+e5VIq48sLBTrr1q1T7T79HgMAAACABInuqVOn0v33369WzR988EE69dRT1f28qt7Q0BD1NoIEIfNNO728PGHp5QWs6UZ6OUiyqSHSywtHTz9dfCuzruMAAAAASKDoXrx4MX3ta1+jKVOmqBZhkqLGUe8jjjgi6m0EJeBeHp+RmjO9vCfiFHBHpBvp5SCJkW6klxecLt1QEdcBAAAAABSjZdhHP/pRet/73kdbtmyxe3QzJ598Mn3oQx/K5SVBqaWXu2u6ExLpjjoardd0I9INSsNfAcdp1DhaB5bIdYCj2Pv27aPa2lqVRs63m5ubVatPRv4FAAAAQEJFNzNu3Dj1s3HjRnV70qRJKuoNBsZEX2q5bffyAVHTjQgXSL6RGiKx0aOf+6VipMZC+5BDDnHc1jPRRIgDAAAAIKGiu7e3l/7jP/5DtQnjlXOGjdW++tWv0lVXXaVM1UD/pNcjpVXEeFy9c6srytXvhazpLhUDJTAwEO2Hmu7C0+UoMymN8X388cfj3gQAAAAA5CO6WVj//ve/px/96Ed03HHHqfueeeYZ+t73vkft7e30gx/8IJeXBSWApJFnGKnFMBHl6JO8b11NBXW28u1oo1D6fpVKhAsMDJBeXjxKMdJ9wgknxL0JAAAAAMhHdP/xj3+k2267jc4++2z7vlmzZtHEiRPpC1/4AkT3QHAvdxup9cVbz11fXUl7W7sir7csxVpOMDBAn+6YWoaVyHWgu7ubenp6qKamxr5v27ZtdMstt1BLS4v6/mZvFgAAAAAkVHTv3r2bpk2blnE/38d/A4WhpaNbRbeG1FbFn15uR9ec98clugdVVxSophuuxSDZWScVVlku+nQXjlK8DlxyySVUXV1Nv/3tb9VtNlU76qijVDba+PHj6ec//zn9/e9/p0WLFsW9qQAAAEC/J6fia3Ysv+mmmzLu5/s44g2ih01vFv3yaVp4w5Oxpjem08tdRmox1HRLuzCu5+Yfd+1lFOgCBu7l4fjtk+/SxX/8Z8mk45a6v0Kl5aWBSHf06NHtUrkOPPvss/SRj3zEvn3nnXeqyPc777xDL7/8Ml1xxRX005/+NNZtBAAAAAYKOUW6f/KTn9CZZ55JjzzyiN2je+nSpbRhwwZasmRJ1NsIrKjue7ta1e/72rtpRH11LMJftHUSUlrFubymspwqrXBfIWu6SyXClRRuf3YtbWvqoFVb9tHhk4bGvTn9jv6YXv74qu1qP44/ZDQlCX3hqFS6GGzatIkOPvhg+/ajjz6qRPjQoalz8cILL6Q//OEPMW4hAAAAMHAoz9Wg5e2331Y9uffu3at+PvzhD9Prr79Of/rTn6LfSmC7dDNxRQ71ubzbvCkO93JJL6+pKk+n1hawprtUIlxJQT6fzp5URgKIlv5mpNba2U3//qfl9Nk/vUidWulIEnC0DiyR8eX+3G1tbfbt559/no455hjH36X7CAAAAAAS2qd7woQJGYZpnLLGrua33nprFNsGPMVfPBNSfTJvu5fH2Kdb0strKisK1i7JUctZIhGupCDCSa+9B8Xo010aotDNrubO1OJiD1FzRzeNqCx+No8X+jW3VBbf5syZoxbBr7vuOnr66aeVidoHPvAB++/vvvuu+h4HAAAAQIJFN4gzvTGeSZ8ezZZW7HakOwZdZUe6K7VIdwFruktVzMQtuktFpJQaMqwS6U4vPJXmIkdjW5fDNDKOEppAZSYlsvi2ePFiOuOMM+jee++lLVu20L/9278pAzXhb3/7m93yEwAAAACFBaK7RNDTLeOqLdZFt4jcWI3UrJru6spyqrKM1LoL2qcb4jHMuMkiRRci3UUxUiv19HJddHOkO7GZRiUyvlwGtnz5cnrooYdo3LhxdO6552ZEwo8++ujYtg8AAAAYSEB0l2Cku7O7LzHp5bEaqUl6eVVFUWq6SyXClbRFIt2PABQuvVwi3aWakbG3tctR353U628pZRJMnz5d/Zj47Gc/W/TtAQAAAAYqoUQ3m6X5wYZqoDDoUdb4It1kEN0Uv5Eau5cXo6a7BMTMa5saVc/yg0YPTozoRsuwQvfptrJO+lWkO1nme44ykxLJeHnqqacCPe74448v+LYAAAAAA51Qoltajfj9/YILLsh3m0BCjXwkndWYXh6rkVrharqd6eXJFo9cB/vh3zxHQ2oq6YVvn0yVsiISAx2aY3nSnKj7X3o59Y9Id1un41hOEkm4/oblxBNPpDLr+sztHk3w37l3NwAAAAASJLrR0zM+9BTduNKcHUZqqblcQvp0s3t5eYEi3aUT4drb1qUE7q7uTnpnezNNH98Q27Ygvbx4ke501klhzoFi0dhaGjXdcWUahWX48OE0ZMgQZaD2qU99ikaNGhX3JgEAAAADlvhCYSAUuhlVXJEWmeTzHF8iKJLampg+3VGL7hKabOuLMa9s3Juc9HJEuotipFaoEou43MuThH7ul0qkmx3Lf/zjH9PSpUvp8MMPp8985jP03HPPUUNDg8pKkx8AAAAAFB6I7hJBn+h1xVXTbb2tCO2460j19PLKCjFS6x2wk209BXblhsZYt0WPbid93Eo+0t1P3MudRmo9yfXUKJHMjerqajrvvPPowQcfpFWrVtGsWbPo0ksvpcmTJ9NVV11F3d3JWtgAAAAA+jMQ3SVCkvp0Szqro093DJvkTC8vfE130ifbuqt9kiLdSC8vDD2uRbBCZXsUi2S3DCstQ0U3++23n+rb/cgjj9AhhxxCP/rRj6ipqSnuzQIAAAAGDOVxu6ueddZZNGHCBJWufP/992d9zhNPPEFHHnkk1dTU0NSpU+mOO+6ggUASarrTLYrS98VrpJZ2Ly9UPas+wU56f159YeatrfuovasnGaIb6eVFTi8vzfFmT4LkppeXjqGim46ODrrrrrto4cKFNHPmTFXb/Y9//INGjBgR96YBAAAAA4ZYRXdLSwvNnj2bbr755kCPX7t2LZ155pl00kkn0cqVK+krX/kKXXzxxSp9rr/jcM/tTV6kO94+3emWYVFGoXif9FL1pEe63e3N3tgSXyQLLcPiMFKTc5FKksbWzsRGup3p5clefBOWLVtGn//852ncuHH005/+lM4++2zasGED3XvvvXT66afHvXkAAADAgCKUe3nUnHHGGeonKLfccgsdcMABdP3116vb06dPp2eeeYZ+/vOf02mnnUYDRnTHFDkUPeuo6S5LQp/uCjvVPMoon9s4jfefo4tSQ5vk9HLmlQ176cj9hseyLR3a8YpId3Ei3WnRXZrjnWgjNUd6eWmM77HHHqvSyr/85S/T3Llz1X38femGxTgAAAAA+rHoDgu7sHKKnA6LbY54+6XW8Y8gdWxdXV3qJ1/kNaJ4LT/atEloR0TbHpaOztR7cia3vH+v1eOVo8JhtimKcWvrTI1JZVkfVZSlBEhHV09kY9Nuvb5Oa0enSmePC79xa+tMRwqZlev3UNfRk4q2bY5taU9vX0dXdyzHaxznaTER8dfXmzrmy6yFr87u6M6BYo0dLyq2aOZpze3xHzM6fAwLQcc3n3GLat/Xr19P1157reff0acbAAAAKA4lJbq3bt1KY8eOddzHt1lIt7W10aBBgzKec91119E111yTcf9DDz1EdXV1kW3bww8/TIXkpa0cxapI/f7yq1S/7RUqNlta+f+V1N3VRUuWLFH3bWpJ3dfa3m7fV6xxW/Mei99yWvPOW7Svi8ennFa/u4aWLFlNUdDanXmKLPm/B6gm9THEimncXtuTPkaYpW9vpiVLNlAcrNiZ3pZ3175HS5aspSRQ6PO0mDS38PiW0fNLn6OtrxG9uzE15uveW09LlqwrqbHb1+U81zZt25nT9aRQvLU+da1htm7fEWrbchm31lZ1sc2L3hKJyAMAAAADgZIS3blw5ZVX0hVXXGHfZoHOLVNOPfVU1a80XzgiwZOqU045haqqqqhQ7Fj6HtHat9Tv02YcRouO3Y+KDZtz0ctLqbamhhYtOlHd9862ZvrJK89RVVU1LVp0UlHHbcl/riTauZ3mHH4YbdvXQY9uXkuT959CixZNoyjY3dJJ9M8nHPd9YOEpNHRQ4T7nfMat8o1tRKtepgNG1tHaXa20ra2M3v+BU2hIbfG3t+OlzUTvvKZ+HztxEi1aNJPipFjnaTH50RtPEXW00/vfdxwdPnEobXhqLf1jwzs0YVK0412MsXt3RwvRi8/at6vqBtOiRcdRUnj1wbeJNqUWMoYOH0mLFh1V0HErlrO412I1AAAAAAaw6GZDmG3btjnu49ssnr0mDuxyzj9ueBIU5QQy6tdz00vpOuLevrJYhENZRYVdOyrvX11dZaeX57JN+YybVcZNdTXVVN2WSpHkBNuoxqasIvWaXMJul6yXVyRCtJnGrdeKxI0bOog6e/po0942WrWtlRZMHVX07evRjlcuh03CmBXjPC0m4qNQbe1TdVXq/Owr0PWhkGPX0uX0I2jt7E3U58TX3PTv4Y7nXMat0PvOJVc33XSTMljjDLIwsPGpPI+NUH/1q1/R0Ucf7fn4vXv3qr7gf/3rX2n37t20//7704033kiLFi2KYE8AAACA0qCk+nTPnz+fHn30Ucd9HEng+/s7untuV0xpg/K2ienTbXAvj9JFXV6rqpxbkpUl3rlYzPaqKstp9uSh6veXNzbGsi1oGVbEPt22kVp5yfaRbmxL+RHUVVck0r3c0RmgROzhWVhzpte8efNowYIFdkvOP/zhD8qQlIXv5ZdfHuo177nnHpU5dvXVV9OKFSuU6GZfle3btxsf39nZqSL969ato7/85S/01ltv0e9+9zuaOHFiJPsIAAAAlAqxiu7m5mbV+ot/pCUY/87mLwxPGC644AL78Z/73OdozZo19I1vfINWrVpFv/71r1X7k7ATh1J3L49L+ElkTSb56vc4+3R3aX26K6IXxfJavL8i6pPc/kq2rbqijGZNGqZ+f2Xj3li2BS3Din8+FmLhqVjsbU0Zh00YlspYajWYGCZm0TPBC286ixcvpt/85jc0ZcoUJXrPPfdc+uxnP6u6fdxwww3qvm9+85uhXpOfd8kll9BFF11EM2bMUB1F2Bvl9ttvNz6e7+foNgv+4447Tm3LCSecoMQ6AAAAMJCIVXS/+OKLdMQRR6gfhlfQ+XeeLDBbtmyxBTjDq/P/+Mc/VHSbv7S5ddhtt93W79uFZYru3nj7AmtHjfwed8swjkZHHeWT12IxU1WR/CiiiIHK8nKabYvumCLdesswiO6CIOI6s093co/RbO3CJlqim49lyWRJAqXYMuy+++6jO++8U0WY2TiUXcq7u7vp5Zdfpo9//ONUYZULBYWj1suXL3d0ECkvL1e3ubOIif/+7/9WmWhf/OIXlenpzJkz6Yc//CEc0wEAAAw4Yq3pPvHEE6nPR6zdcccdxue89NJLNNDQoytcrxsH8lmZ08v74ksvr9TSvyNNL09NrisrylStuHr9BAtIPb388ElDVS0613XvbO6gUYMzfQ2KsSDCIL28uH26k7wwlD3SXWvf19LRoxbUkoA+pkkuMdHZuHGj3Z+bxS57m3BWGLcJy4WdO3cqsWzqIMKZZyY4M+2xxx6jT37yk8rxffXq1fSFL3xBmcxxinqYNp/sxg5Hdn94fPh7GuME/MBxEi3lpVWpG2q/yqis3+4fE9U5EPR1SspIbSCjC5fYIt1SQ1qWkPRyiXRzTbeVXi5COcqJttTKJj211BbdFWU0uKaSDho9mFZvb1Yp5h+Y5pwoFxqklxcv86QiI9LdW7KR7hH11VRbVU7tXb3U0tGtbicB/RiOy1MjLCyQq6vT41dZWUmDBw8u6jbwRGTMmDF06623qsg6LwJs2rRJGbF5iW6vNp87duyg9vb2Imx16cLj3djYqAQVZyEAYALHSbTMbUgtbvY3WHBPrZuqfu+zQ0/9i+0efiRh2bdvX6DHQXSXYnp5TJEsO51Vq+mW3/lPfAHPNYqSX013RUGMzuS1OL1cdivJqaWyIFBtpcLPmjRUie6VGxpjFt3982IdN+nzkRw13aUY6RbRze34eMGovauTWhJU161fV0ol0s3X43/7t3+zu3ewYGVflPr6esfj2FU8CKNGjVLC2dRBhDuLmBg/frxyYtdT2adPn66czzldXV8UyNbmc/To0ZG0+ezvYoq/g3msIKaAFzhOomV503Lqj0iEe0XTCuql5M5984EXhaOgtjadpecHRHeJ4Ii0xBQ5TKeXp+/To94817cCzkVPLy+EiVQ60l1mC5skC0g5LiTqz3Xdf12xKRYztU6tZhPp5YWhxyO9PI5Sj3zZ25pyLx82qJrqayppZ3OninQnBX2xLcnXAJ0LL7zQcftf//Vf83o9FsgcqeYOIuecc449eefbl156qfE5bJ521113qcfJ5P7tt99WYtwkuP3afPLzIRCyw2IKYwWygeMkOvqrIJUIN+9ff93H8oiO/6CvA9FdIjjdc2M2UtOEth71ZhGgO5sX00iNzcOYrgLUdHO6tuxzSdR0W5HumRNTbcPe2hos7SVKkF5e/PRyOQdKJRJrinQ3DKqi+urU11JzR08ir79JznbR4dZgUcMRaBbz3IaMe3Nz27GWlhblZs5wtxFuB8Yp4sznP/951Q/8sssuoy996Uv0zjvvKCO1L3/5y5FvGwAAAJBkILpLBN0BOq5JtTuy5v692BG2zgLXdMtEm/exFEyq3Onlw+uqYut57OjTDdFdkKwTOd1k4cv62EvSvXyvJbqH1aXSy5mkRrpLcVEjKs477zxVW80dRjhFfM6cOfTAAw/Y5mrcbURf8ee08AcffFAZuM2aNUsJchbgYVuVAQAAAKUORHeJ0KVHDmOaVNuTfIORWrEn+xxxFgHscC+PcEIs+8MRRHn9JEdtRehKpHtQdaqOsr2rJ96WYUgvjxz9XEsbqSW/rZ0XTVpNd11NRWyLRV7o15UkXwOKAaeSe6WTP/HEExn3ccuw559/vghbBgAAACQXFHOUCIno0200UtP+XsRIty7qUunlha3p5hTzpEe5JBonUf+6qko7Al5soTDQ0sv5nPRrfxg1+rkm52NlidZ087hJyzCOdHNNN9OaJNGttwwrwUUNAAAAAMQLRHeJkKSa7govI7UiTkbFuZypVpHu6KN8ek13pRU9TnI9Z1d3nyPSXVudPr3bihzt1vt0l4rxVD71yPN/9Bh99d6Xi/ae+mGY0ae7xMa7pbPHPm/ZSG2wVdPN9ycFfaGTF/aKucACAAAAgNIHortE0CO7cYmYtHu5uaa7mOnlIupYEPM2pGu6+wpS0y2R7iQLSFmMkZpu/lc+nvYiCxhHTXc/Ty9fvX0f7djXQc++uzOWSHdmn+7kHqN+Jmp8vHKPbol0Jym93H3eI9oNAAAAgDBAdMfIqxsb6ZePvmO3vgrepzuu9HLKSC/nthOiwYuZXp5uF1bhSK2NMgtAr+mW6HGSU6VlYUYWCPizqbOihq3FFt16TXeRU6+LjSwAtRVxjHVhLSUeabO/5B6jfu3ChtZVqWN2sFXTnVQjNXU7wYtvAAAAAEgeEN0x8pMHV9END79NT729M2Sf7pjcyw19uvVIWzHn+ul2YeUFi/JJNIuj6CLqkzzZlm2TVHimtqoilvRyd3S7P0cGZV/bixjR10s50i3DSjvSzSZqTF0CI93u876rxBY2AAAAABAvEN0x0tSemlTusSI9Qep14zRSk2iluxe3RL6LGunucopu6VHcU4Ca7lT6enniJ9vu9HJmkFXXHbfo7s8p5rJv/G+xBK8jvdxV013M8zAKGsVEzRLdaSO15NR0u8/7JC++AQAAACB5QHTHSIclhIK0dEpEpLs3s6bbGemOIb3ciuQWoo+2jHNlibiX2+nllenPZ5A1PkWv6XYtDCU5LT/KfQ1SKhIFcq7xqccp2Q7RneBj1C/Szc7ljJ1e3pncSHdcC58AAAAAKE0guhMwWQ9Sb+s0UutNluiOIa3VnV5eiNRaeS12RpdIepLFo2yb1J8zg2Kq6dbd5QdKpLuYdd3pTgKZpoallsq/1xLdDRLprk5eerl7obOrxMYYAAAAAPEC0R0jIkyCCCKnkVpc7uVkTC+P10jNEt22u3h04k7GOdUyLPmCxq7p1pqnD6qKKb3c9Tm4b/dX0V2sum57AUw7FwtRYlEM7B7dg6rVv4Ot9PJkG6n13+MZAAAAANED0R0jIkSCpZfHX9OdNlIzR7rj6NOddi+PXnDIOKuWYdIHvKcEaroN6eVx13QnudVavugLCsWKdIsGNEW6S66m28NIrSVBNd0ZRmr9+HgGAAAAQPRAdCegprs1QO1iEmq6e7O4lxc30m2JbiuSW4jU2nTLsHSkO8mT7U5r2/T0cmkZVsx2VgPVSC3oAloUyLkmZRWlXdPdmfiabncGTam1ZQMAAABAvEB0x4gIx7Dp5XHVFUsk29O9PA4jtQLWdKdbhqX7dCd5sm2q6Y6jZRi73Lt7hie5Fj6q87iootuYXp78EoggRmr1iUwvTxvXqdsltrABAAAAgHiB6I4JXZiETi+PaVJtmujH36fb7V7eW5hIdwn06U6L7rLMlmFFjHTr6dYioAZMTbfLQK7QWSf6AlgchobZuOPZtfTBm5+lPS2dWWu6bSM165jha16x3OCzXatlTKVcoz8vIgEAAAAgeiC6Y4InlJKNnS3SzRM+fSIdW6Tb2gRP9/IY+3RLdDdKwSHj7OjTnWDR3e2TXl6sCKxbhNZb79+v08v1mu5iR7q1c7EygTXd9y3fSC9v2EvPr9kVwEjN6V6elLpufZFTRHepZRMAAAAAIF4guhMwUc8mut0iOz7RLW2KnPeLWXYc6eXVlugW4Z9azOiLPNJt9+lOcHp5p096eTFbhukCW5yo+3NkMJaabrvUw1zmEdU5kC8yHrt8It1NLiM1PpdrLa+GJKSY69ktcj715+MZAAAAANED0R2ziVqQiXqGiU9PMtPLiznR9+rTzUSl/fWa7nSf7mSImcB9umOo6RbxX6mJp/4sUhx9uos0zukFsMxId5JSzCXdfreH6OZuAPssYT2sLtUyzNE2LAFmal3aQtugaivSneDrAAAAAACSB0R3AsyXske6+zLEYByRLK/08niM1MS93Krp1sLvUUWjTe7liW4Z1m2o6Y6hT7eIUM5CkAWAfp1eru2bvphW7AUwvb47KenPcp56ie6m9rSobqhNp5UnyUxNF9iyyJeURQ0AAAAAlAYQ3UmIjoVML49rUm2Krum3i1vTbXYvV9sR0djoNd3p9PLkTra7epPRMswouvtxZDCOmm6TkZpkY+h/jxs5T71E997W1P1Daipt3wSm3jpum5NQ0219vjzUcjz358wNAAAAAEQPRHciIt3+0RxTlDCOSZ9nenl5nO7lzpruKIWx07082ZNtznwwtgyz0mGLKbrls6muKLdr7iUK3x+Jw71cDkN9ASyJke727iyiW+q5rXZhGenlCYh0dzlaByZ/8Q0AAAAAyQOiOyb0VjjZomMipqQ+NnVffJFul+a2082LGul2tQyr0qJ8UdVbOvt0J7tlWMo8i2yxm4Sa7poqPdLdOyAW0NoSkl7ek4DjlLdRrlNeRmqNLhM1oa4mddw2J0B0S6S7qgQW3wAAAACQTCC6ExId6/WJnMjEVVKF46otlm3UJ/f6bb99KNSiBQs7ER8S9Iuqprvb0DIsqe7l+iKM1J8zdVakO46WYalIt7jKJ3PcokBfUCjWOJtKPfTTMgmRWH1h0atPd6O0C3NFuqWmuzUBolvOLWWo6LH4tq+9i77/P2/QTx5YFcs2AgAAACDZQHQnIDqWLUImgoVFjNQuxzGplkh2IozUXH26HX2KeyOOdKsIV7olWRLRHZaT0jKsurLCjrr3byO14J0IChnpLitLH6dJqOnWU+05vdxk/ugV6R5sLTC2FPG49UIW2jjbpcpj8Y0N4W5/di3d9vTaWLYRAAAAAMkGojsmOkOIbrv/cmXaRTuOyKGXe7kEVuNML1fbIQsSPRHXdKv08oRHurXjyeleHkN6udFILZnjVrI13baRmvN++xxIQKRbX4Dgz9+UKr7XinQPHZRuF6ZHupORXi6Lb+Wei29yDOiLgAAAAAAAAmYICUi9zGZ0lW4FVW7XLsdS022nl1Ny0ssdke7ywkW67cWO+MWMCdkuFtwc8XT3FW7vjKGmm49X20gtmeMWuXt5kcbZPhfdC2CS7ZGA49SdzbOnJSWwTZFud3r5YKumOxFGatJ3Xo90uxaR9IUmAAAAAAA3mCEkZELql/4rgkqll8fYL9pOaXWnl8dppKaZy9ljU4iabkvQJ7VPty0MNEM5vaa7taunaL3ddQFip5f3xJ8mXJRIt2sxLa5OAknIyHCn2u9q6ch4zN62Tg8jtQRFurVWfOlrjDnSDdENAAAAABOYIZRQTXdq0hdjpFvSyz0m+vHUdKfTy6Oud9dbhiW9VVD6GHF+NlLTrTtJF1V0S6Q7AZHXYojuokW6DUZqTLJqup1jYWobZhupDfIyUutJUHq57l7uEt3WohJENwAAAABMYIaQmEh3d/aa7ooyO3IYRyTLa6Jvp5f3xZteHnVNt94yLM7FjiDY2RCuSb/UdBezrrtDM/6TRYD+baSmR7qL26c7cwFMrg/xH6fu+nZT2zBPIzVJL/e5LhYLudY6Wwf2GhcB9XZ9AAAAAAACZghJMVLrDBrpjtNILUuf7iJukslILeqabkek2xb0yRSP+jGiwyJcop/FisI608sr+r+Rmt4yrEhj3OO5AEaJ6Sfv9q0wtQ3bK6Lb3TKsOkHp5Zpfgn39dV1jZKFJL3cBAAAAABAwQ0iKkZpPFFImfbqAiiPimq2OtLhGapmT3Kidm7uMfbrjFzN+wk/v0R2Xg7nDvVz6dPfjSHdHDDXdaVNDd3p5tAtPUUa6jenlYqTmci8fbKWXJ8pIzcfbQe9NDwAAAADgBjOEmJB0xCBGaragUrXFMumLo6bbHF2Lw0jN1KIn6j7d8jqOCFdCI7bpaFzmKV1rmanFE+kuT/S4lWpNd3Yjtb7ELSy608t54WBva6fRvVyM1FqSUNNtZ7yk08vd1xgYqQEAAADAD8wQYsKdbhs0vVxEVVccNd2edaRUfCM1u6ZbSy+P2NldJttcJyut2pKQtut3jJgibUWPdIupVEXaSK2/ppezI7wjvbyr2OnllFgjNffCoju9fEdzh8rY4YWCMUNqElvTrbcM8/J2SIvu9PUIAAAAAECA6E7IhNTXvVz6dHN6eUV86boy0S+P2UhNd+J2GqlFmwLucC2OuB1Z1KTN9jJPaWkbZlrYWb+rNfIotJ6FINvT2U/7dPOxph/27pTqYqeXy4JYMReHvFrRSaq9LAS5I90b97Spf8c11Npi1u1enoT0cvs6wJ4aHi3ZpMQA6eUAAAAAMIEZQky4+xYH7dNtR1xjSB9NT/Sd95fZRmrFbUmV0ac74vTydKQ73TIsse7l3d413dI2zL2w8891u+n4nz5Oi//+eqTbMpDSy92GiLz4UYzzwGsBLOpzIBv/tXwjzb7mIXp+za6Mv0nUf9zQWmNN98Y9rerficMHZTxXRDefb+409WIjArvKr2WYZN7ASA0AAAAABjBDSEqkO2DLsFxri7l28rK7X6Jn3tlJ+buXuyLdRRbd+iRcjyxFXc/aY7cK8jZQSgqyz1Uh0svf2rpP/fvGlqZIt0WOV7VIJOnl/dRIzbRfxUgx94p0p8+B4oz3M6t3UlN7Ny1buzvjbxL1H+8hujftTUW6Jw0ziG7LvTwJdd0isFPp5eYSFjnmaxDpBgAAAIABzBBiQtIRh9RWZk8vN9R0h00f/euKTfT3lZvptmfW5LzN8pZxp5fL2KXSvk2R7mhrullwe7UKKoWa7nR6uXNhp6k95Ry9u6Uj0m3pcES6k21Aly8itvRTohii28tIrdg13VKyYMrUkcWxiZao5vZf+oLZJiu9fJIh0s3XlForahx3irkIbEefbhipAQAAACAEmCHEhEzSxLXXP71cF925iZiXN+5NvU8eUSPPOtIi9+mWLAG9nptJZwH0ZQiUHfvCC0tZ2Eillyc70i3HkxwfQdzLm9pSYmZ3c2dBtkWJbusz6reiW6tfl+OxGIZ1dulDWbw13bKvpoUGiXSPHlJjXzP2tKQWevSablN6uaNtWMxmanYWiSO93KNlGEQ3AAAAAAxghhATEvEZXlcdwL1c79Mt7uXhJtWvbGzMWxCk08ud90twtXiRbqmfrAjUo/ib//UKHf3DR+x06qDI66T680oEsbj9yMOnwPqll/caI90tnT2RRmcdfbqt7dF7WfcndAMtqZ0vhpma3b7PI9JdrFIPuZ60GoSxHFM8LnKd26VlVUh6+cRhdcbXToqZmn5u2ZFu16IGjNQAAAAA4AdmCDGnpQ4dVJVVDOtRzFzaYjW2ddHanS3q93zEVbbewMWr6TZHur1qullss0Z5dVNq4SEoUhertwqKq11b0G31TS93ffZ8XAhuZ+nIaroHiJEat4qSxY3ipJf7l3oUy2hR9tW9oKOfpyy6R9ZXO+q62fF8U5ZId71V190cc023lKukrr/lvu7lMFIDAAAAgAnMEGJCUqSHWRGgoOnlIqrCpI++akW5o4t0m9PLix7pdqeXe9R0y+N3Nnfk2DIsHeHS7y+V9HI70u2u6dZFd8ixCZ9enrwxiwLbQKuSI93lxTNSsyPdZMz2KHZNd5tvpLuchtdXOUT3ntYu+1o0YVjKaM0zvTwpkW72drCuMRnu5fZCE/p0AwAAACCTtEUsKCoSGRlu1XS3BRTdEumWSV6Yeu58U18leFbhZaRWrEi3XdNdESjKJ2O9M2Rdt94yTMSMuj+BAlJEgMm93KtlGLtOFyLSrWciyCJRf3cvry5yTXdPNvfyItd0m/ZZrjWpSHeNQ3RLu7AxQ2oyzmOhrqYiEaJbz3ixvR1cC3uo6QYAAACAH5ghxG2kZqWXm2oiTX267fTGEJPqVzTR3VGA9HLbSK3I7uXuVE6ZELvT3EWkh410y+tw9FiPICcxvdxemDFM+gfZRmrO7d6nRbqjNFMz1XT3+/TyItd02+diWbylHrKvfu7lvBgxwpVeni21PEk13XKt1Rc9M/t0Q3QDAAAAwBvMEGJCJqRDrfRyv4m63qebHXTD9uEVE7XCGakV2b3cI71ctsM9IU6nl4cTljLG/LplZWVFjyKGQWr85fgw13SbW4a5Da6iE6IVtgjpt5HuntSxxfvp1Q+9mEZqsdV0m0S3Fuke7hbd0qN7uNlEjRls1XSz0V8y0svTGS9uTw2vaxIAAAAAAIMZQilEuqVel93L7chhsEn19qZ22tLYbt/myXiuba+yTfSL3ae7OnBNd2/eNd366ycxatsZJL1cEy9sZCUtwwpmpKYi3eHLIUoJPcJZzJpur0i3fQ4U4VzkY8g3vdwSojwubiM1u12Y1cPbL9LN/b0TkV6uWgeiTzcAAAAAwoMZQtw13fVh+3SH6xf9shXl3m9EOqLUnmPU0Wuin+7TnfCa7hCimwWFXtPNpOs5+0orvdwQgeUx0YVwodLLpaY7iQsVUaC3ipI0/qKI7iwLYD1FGG9e+JNz3hTplnHg81TSy2VxJ1uPbmZwQmq69ZZhXuU9uqEeAAAAAIAbzBBinqwPHVRt3/YyItNruiXSElTESD33vCnD7fv8TNv8kM3LrCOlIrcM83Avt8amR5sQ8+KEbBdH2YJuo/4wGfNc2rUVC31hxju9vNfYLqxQRmp8vErkj8czieMWaaTbWgQqint5NiO1IpyL+iKOMb3cbhmWGelOp5d7i+46u6Y73vRyOW4rfcp79Np+AAAAAAA3mCHEnV5uuZf71YKma7q5ZY0VOQw4qZZI95zJw/JOf01P9J33i5t5sdPLg/Tplsemti896c+GPqmW17XHPsnu5eXBWobp7cIiTy+3FkV0IzV9G/sTeip9rYdhXRx9uouxAKZfR8zu5elIt9R077GN1FLu5ZMCpJfHHemW60lVebmnrwPSywEAAADgB2YIMcDiVSbrQ62abr8U83QUsyxUtJVTpCXSPWvSMFt85Sq6JaU1I728yI7JadHtTC+3TY40wayL7jAp5vqkWl43Xc/ZW1Lp5bYY1D533UQt8j7dWqqtLro7B0qk21p0iKdPd/FquvXoNgtTt1leumVYOtK9p7WTGlu77HZ1gdLLffwuioGcW3zttT01PHwjvNqfAQAAAGBgA9EdA7r4YJOrbGJYb1kj6YtBHLQ37G6jva1dSixOHz8k75ZGnunlxY50SwTN1TKs0hjp7slNdGuvYUe6PdoFJT29PB3pTn/uYqI2pDYVTQyaARAEEV8p0Z0+Vvqjg7m9r6qm2+rT3VnEPt0ZkW6rbV4RjlF3dNt92xTp5s1+fUsq+2Z4XRXVWQ7lJuqrE2KkptV02wtviHQDAAAAIASYIcSAHn1lYSIGTNkj3ek+sUGihi9bUe7p4xvUxNcW3TlG4rzqSIsd6ZbJvYhJocJQ0y2ma2FFt74vIuY5vZRJYm2yHCPVmsjNqOnW08utSPeBo+rtYy8KschjI0PHAoRbrfVnMzVTpNu90FMI7KyTjJpuiqWm2+SOn67prlDXLlngedUqefFrF8YMTkx6ebodn1eJiVdHBQAAAAAABjOEGJBJOQepWNCJePRqG6b36fZyzzWRTi0f6tk6Kif3ci/H5CJpKlmckMUKv0i3e4Fh576ANd3WzvBLyv7aqf2JdC9PR+P83MtZDOk13eOHDrJFcRS9uvXFIBEgYc3/SrWmW47HYkS67QWwjJZh5cWr6Xbtp9sdXxAvCUkxf2VTY9Z2YUkyUtPPLa8SE/s4gJEaAAAAAAxghhADevotRwLT7tJZIt2cruvhnutnosb13EzeRmp2TTfFml4uoiYj0m0QHLlGukVYi4jRf0+iePRLL5eabt4lEQfiXs6eAiMHO52l80FPIRcBIuI7yenltz29hi68fVnoc0N3ra7Js3wj0gWwYtR0u8ZKXzTUzzupc5a2YRLp9qvnTlJNt4y136KnlLwg0g0AAAAAE5ghxIDeUonJFiHr6tZbhgVz0OaJ4mtWRGm2iG7b6CnXmm5zdC2u9HJZrBCqsriXMztCppdLdFu9vkc9ZxLQzfbc6IsT7VZdtxhZNQyqzOihnA8iQvmjEIEix2ySjdTueG4dPfn2Dnp5Qyo7JCh6WrGpH3qx+3TbRmoxpJfrCxaSzcObI8fkiPoa9e/63ZZzeRbRPciq6S5G5kAgI7Vy7h5hztpAn24AAAAA+IEZQgxIFEgiY+n08uA13dmire/uaFavx8J06pjBqfexRKo7LTTv6Jp1sxjRNX2cJF3e3g6Ds3umkVrQlmGZokbEYyLdy7WFGTe83SJ8Wru6HenlDbVVadEdcGz8MNW2Bl0oihMRdl7nYKCWYXlmkkSRXi7nZjEWhtxiWB+7tHN5hcrmYUbUpzs1BEkvr7WOIT6mZH/jwM564T7d9jXAbKQG0Q0AAAAAE5ghxIA7KpItvVyv6bYnfVkm1Q++tlX9e8R+w2zhaIuCHI2eRFN79QYu1sRYJvtu52NTlC8jvXxf0JZhEt1K72uS3cvlGDHVdJvq+cVIrWFQFY0anIpA7o6wplsX/zUlkF7emqvoNkS6iyG65RB0L4Clz4HCj7V7P3URLtcYXYRKpFvIll6uL6q5M1aKSbehZSNfY8QfwW0eCAAAAADgBjOEGHDX/2VNL9cj3ZLe6DOpZvF73/KN6vcPHzHJvr82oj7dbl2XlPRyqek2pZfXW48NW9Mtr5n0SLftsGxILzct7EjLMK7pjjLSnRahFYZId/LGTc4XGZew9cN6TXe+Lflyi3STcQEsFvdyPb1ci3QLYqQmZHMv159bjIUML2SRja8F0sFAv99kHtifufnmm2nKlClUW1tLxxxzDC1btizQ8+6++26V9XDOOecUfBsBAACApNH/ZwiJjnRLenlllvRyK3WYjdQqs0e6X1i7W9VNcsudRYePN0Q7e/NLL4+5T7cYNrnTy42RbiviJlE1rlsOEpHXzZPcr5/ESLdfermzV7c70l2Ymm49wllVGbzNXRzomR+tIdtT6ftrn1/FiHR7tO+TY7QY56L7OmKKdOvnqPTqZobUVKoFHz943+T8yzU7J/KWYdr1QO7Xs2n6u3v5PffcQ1dccQVdffXVtGLFCpo9ezaddtpptH37dt/nrVu3jr72ta/R+9///qJtKwAAAJAk+vcMIek13QHSy3lynRaA6UiLX9Tw3hc3qH/Pmj3B0VYrbaQWbXq53O4penq5O9Kd6ewuke4JVv0ob+Neq545bE13mHZtxUZ3uDfhFoR6TfeoKN3LtRpntxBJanq5LhZbu0qjpturT3dRa7q7/Gq6M9PL9Uh3ttRyoZjZA17IWPL57xTdzkg3Xyu8yjv6CzfccANdcskldNFFF9GMGTPolltuobq6Orr99ts9n9PT00Of/OQn6ZprrqEDDzywqNsLAAAAJAVnUSwoCm6zKVt0G1JbdXGt1xR6iW6OYC55dYv6/WPz0qnlzKDqcs809qff2UHf+q9X6UcfOZzef/DoUNE1O728SFrU073c4C4uqfyDrcgat8riFHOJ7oap6fbq0Zuomm53PzcLdwmDtAzjmm6ptd0VMPXedztczvylkF6ui8XWjvxruvONdHOt8N7WLkdkOHif7uItgGXUdGu3211mkYx+zmUzUdNF97727njTy63zXRmpaenlcp0xHfP9kc7OTlq+fDldeeWV9n3l5eW0cOFCWrp0qefzvv/979OYMWPoM5/5DD399NNZ36ejo0P9CE1NTerf3l421EvmNSQp8Pjw9QPjBPzAcRIt5f00fsn7VUZl/Xb/mKjOgaCvA9EdA509ziiQRHNM6eVO0Z12ofaq2fyflzcrUX/I2ME0Z3KqVZg70u129GYefXM7bdrbRkte3eopuiVl1TXPt2u8i2Wk5ule7lPTzan8HNFVontfBx0ydkhAx2JTn+7kRbpFAJj6dLuzKfjL1m4ZpruXR5he7oh0J9xITReLORupVVTYx6PbvC8sNz++mn720Nt056ePpuMPGR2qZZjpHIjDSE2uMeJA7hbd2dqFCZI9UIyU/aznVnm5WmDkIefhlYU5k2N/f2Tnzp0qaj127FjH/Xx71apVxuc888wz9Pvf/55WrlwZ+H2uu+46FRV3s2PHDmpvb89hywcOPPFrbGxU13heEAHABI6TaJnbMJf6Iyy4p9ZNVb/3UfLmvVGQrTQqKPv27Qv0OIjuJKWXG0V3n0t0+6c43/tiykDtY/Mm2616gqRqSp301sa27H26y+NLL+f3kEmuO9JtrumWiFu5cul+d0dLoF7ddp9ug3u53pIsKcjijNfEX6/pZmEp+8c13R3dhW0ZVl1Kke6QRmod2rjLGHPWAY+v+zwJymubUpG91zY3eovuLDXdxWjfJ0KY950XH0yR7lqvSHdQ0S0lMQbRzYuE5/12KZ03bzJ96eSDqVDINVjOf15848+4S9LLB4jozmUS8qlPfYp+97vf0ahRowI/jyPpXDeuR7onT55Mo0ePpoaGhgJtbf8RU/y9z2MFMQW8wHESLcubllN/RCLcK5pWUC8lc/6WL5yFFQVsLBoEiO4EGKn51XSLUOHJtaoZ9KnpfmvrPnp5w1418T7niIkZf6/1Efct1n1bGr0jCfZE36NlWDEm+vrk290yzFzTnc4qGDWkJnCvbn3chaoiRhHDkm4rl72mW0zU+DhhoShiiP/Gx4buA5DrdpjSyzsTmCHgPh/kPHCzYv0elckxb8oI7/Rybdz4OK2vye3yKg7qLT6mbl4LYPa5WMQ+3VyrzdeNbDXdfJ3j27wwM3GYv3O54Jc98NL6PbRxTxvd/uxa+sJJU3Ne5AjbGYDFN+9qOtLdMyDSy1k4V1RU0LZt2xz38+1x48ZlPP7dd99VBmpnnXVWRgpeZWUlvfXWW3TQQQdlPK+mpkb9uGFxAIGQHRZTGCuQDRwn0dFfBalEuHn/+us+lkd0/Ad9HZxtCYh0+6WXy6ReJnzyr0l0i4Hawulj7d7LOpLqaTJSE9fmzXv9It2pf8u83MuLIEb1MdIn9Nn6dPMCx2hrTHbmGelOYpp0V5aabn1hR28Xxp8l17uLYNiVZ6/u0kwvT4tbk68Cb/enbnuBPvX7ZRkR105NcOnHYz7p0CJmW3zqyz07CcTQMmx4XXXG2EnGgx7p5mNNarkPHF0f6D38ep/LOO1p7aLXNjVSwY3UrC9VdxcD28HeSoXvr1RXV9PcuXPp0UcfdYhovj1//vyMx0+bNo1effVVlVouP2effTaddNJJ6neOXgMAAAADBUS6Y8COjNjp5ZU+6eXOCKaXgzZP/P720ib1+8eOchqoCRKJM01gZYLPtb4cYTNF6XqzGKkVo02RbDtPxt3OzfbYGGu6y2lwjdWre192YWmq6U50n26trVy29PJ0u7AqWwyNHJyKVnKKebb+yWFFdykZqZmELo+XRMD5d11I6u7lPI5cg8yp1fkYf8l7NftFuq2h9Ix0F+EYlX3UMyXcf5OabOH6j82m1dubafr4YGnCImRNC4X6+z319g6a7fKwiAo532XRzX0dMGV39Fc47fvCCy+kefPm0dFHH0033ngjtbS0KDdz5oILLqCJEyequmxOt5s5c6bj+cOGpT4j9/0AAABAfweiOwbcvYz908ud/ZftKItrUv32tn2q5RNHL4/3MEKT+sg2Q6qmpLQyWxrbaOqYId7mTR6R7mJkD4tActdz62PjcC+X9PKqchphReTCRLodLcOK2I4pDLwYIosEnunlWmlBY6u0C0uf/iycWHTn2zbMjvw6It3WMdtdmjXdepq3cjcf4n0usyDPV3S3BUgvt1uGebmXF+EQbXOJbn0cpWuAlNAIR+w3XP0ExS6LcPUET92Xfr8n395RsLput0lh2tvBFekeADXd5513njI0W7x4MW3dupXmzJlDDzzwgG2utn79eqSrAgAAAAYgumPAbTaVTi/3bhkmEz15jlv4yQSdI5ZevWLT9ZGG9HJtArt5b7tRdHu7lxczvbzb6Fyub4e3e3n4mm5nenky3cv1BRgpP3Cjt7NyR7qZqBzMbb+CCkOf7oRGunWBbCrx0CPO+uKUKbLP47yXuvLqK90aINLtZaRWzEi3iF45dvRxbLfTy/MTYGnzR/9I90sb9qrOBLzoGCXs7mtnvVhj6/bVGGhGapdeeqn6MfHEE0/4PveOO+4o0FYBAAAAyWZgzBIShi4Es7uXu9LLNWHJE0L3BFTElQm7T7cxvdwZ6Q6T0mr36S5iHalfpFsXHHr9fNpILbdId1L7dLsd7k3ox1iT9OiuTQsUWZDIt1e3X3p5UkW3M9LtXXph+ru7R7NuWJfv9vi1L0sbqXksPBVhYUgWFvwi3abFsTD4+VDoY8zn63Ord1LU6At4sujmbts4UFqGAQAAACB3MEuIAb3OOHR6uTbL1sWWXuvshYj89gCR7lC9gcVIrRhtinzSy82Rbs29fHC6NZa+YGHClK6d1D7degszL9HtjHRbPboHOdPLmfzTyw2i2/q9qztZ42as6TZkmzR3dKX/7oo+6zXd2SKzQeDjsjVIermHkZrJTLDgRmrGmu5MI7Vc8Gtz2G59bnI54hTzqNEXL+xIt8ujIJ1ent++AgAAAKD/AtGdACM1P/dyd6RbTx/WI652pNun3ZPXBJYn+u6abhNe6eVSwleMib6MkWkyLyn45j7d6fRyFkri4B2qpjuhfbpF+PGmerVNcrQMa/NOLw+Seh+ob7UxvTz36G8h0V23zenl5kg3l1PYi2L2uWxlk/hEqf3g41UO39zSy8tDt+97Y3NTTostessw/bYemc63zlmuZ6aSGLnmSRs3NlPLtpiWT+lGuk+3M5vAdMwDAAAAAOhglpAgIzWecLuFq91/2TKj0iOZesRVjIb8IkuDPCLqLML1uaqpVzdPZuUx3kZqcaeXZ9a761kFPDZDLFf2HVnSqLsNNd3utNKkIMeBV5Tb7ZBv13Q70ssl0l24lmFJjXTr5wNvv9tlXY8467/r6fJ6Tbf7NXN3Ug/QpzvPSDf3uv6XXz1Nl9+zMtR28vUgo2WYts92WUdE6eWm8RRDyBMPHa3O782N7coZPUr0a0mVdX3JcC9HejkAAAAAsoBZQgykawClpjud5utOSxXHZ3dNt/qbNun3E6PuCSxPEnXTM3dKrUl065N4L/OmYhipSTRtkG96uVbT7Yq4Ba3rNrUMcxsoJQX3MZKtnl+i/M5Id03h0st9essnAXd0233b4V6u/c0hul013SI6w2+LLvAD9On28FcIWtP9f69tVZH1sH2u5Rom5o2pbc+MdMs1J1dqAvTpZtF/9AEjCpJiLotvPKwytl7u5RDdAAAAAPACs4QE1HTrDr/uCb87ism9gE2tq4LUdOtCVZ80qzZIGlv2tmWkaep6mrfBaKRWhEi3jM+gqspg6eW2kVqFI6KbTXTLaxgj3Umr6bYWGbycy52tl3qUy7OpZVgU6eXGSLd17Eoabhz87ytb6McvV9C6XS0Zf3Ongrtv77Nq4N0LVLKv+j7mG+luc4l6/T105BB3L4CFjXQ/vmq77Vofpg5d306JdPO2yvvKa9UUsqZbW2g84ZDRBRHdXYbFN4l4p43UokmlBwAAAED/BbOEGOjUekeLiLUn6xmiO7NesNIQOfRrpeXu063eR5tgi5AYbKVet6gUZGf0WzdJ8zRSK4KmCuJebmoZJgsbdtuwff6iWxY7nDXdzrTSpNDZHSK93KNlWDq9PJqWYfrxmjZSi2/c/v7yFtrcWkZPvbMr429ugezO/Mjo020hgpgXO2ThSc7pXI3U+Nzzeu8gRmoVIRbANuxupXe0dOztTcFLC2TM+HMeoi3eyP22kVqeQlTOW7+WYXzNE9G9bO3uvHqke0W6qwzeDgO1ZRgAAAAAwoNZQpzp5Zow8XIwt2u6tShmuqYws6bbz0iNhYG8p7M3cbrH97C6KqOZmiO93GuiX5T08m6f9HLLRMqQAZCOdAfr1S1txxx9uq3fk+Ze7jbbM6Ev6phquiXSzcefqV98UPQWbYJsV5zp5SJmTSLWnV3iXvjSRbgp0q2fx/nXdDu3z8tMzctILUyk+4m3UlFuYbOHgaKJtOAtV5+1XBJk7NKLXflFumU8pe+3YxvsrJcKmjpmME0YWqve9/k1mQsruSLnuh7pdrdlg+gGAAAAQDYwS4jTSE2bkIqIdE+6TYLKJGKC9On2ihxJ7ShHQ8cPHaR+3+JqG6ZHujPcy8viSC8PF+mWCGRadAet6TYtdiQr0p0+RrzTyx0tw6ya7qFayzDOchDxyC3VcsXdQksX4HH26ZbzyiRiM0S26zEO93I90m3YV7906EDb6Sr1MLUw8zNSs2u6Axyjj7/lTMXeavByCOKtYMrUEbfxfFOu7fE0uMHbJTXWNhxvRbufentnQUs3MozUrOOgBu7lAAAAAPAgEbOEm2++maZMmUK1tbV0zDHH0LJlyzwfe8cdd6gJlv7Dzyvlmm7GM71cUli1x6Yjrr2harrdraPcgqS+ukJFi0xRL30OH6uRml96eUUQI7WwNd2mtP6kRbqzp5fXakZq+wzp5XwejYwgxdzUs9heJIrRvVzErEl0uxe63JHvZmu8PCPdhvM41xTnVnequ0ekWxaFtMPTGenOcozy9j33bkqczhjf4Gmg6Pd8fX/dEX75e/59uq1FQus81nEvNC6YOkr9u3z9HooKiWY7rgOujBdEugEAAACQjdhnCffccw9dccUVdPXVV9OKFSto9uzZdNppp9H27c7UR52GhgbasmWL/fPee+9RKffp9ksvt/sAGyLduqGXiPVan/Ryr0icHemuqaTxw2qzRroz60ipeC3DArmXp7aDzeDSCxzh0suNNd12S7KkRrqz13TzRyRrI3p6uZ5iviuPtmFm9/L4jdTS6eWmiKmz7j/TvdzcpzvdhUCPdOdX0633DHdH2XVkgctrASzbubh0zS613+OH1tJJ00YbS0p8t9Mlqt2ZOlGll4sPhWk85bOQ95g1caj6980tTZGVMvhmvFjv4b7GAAAAAAAkTnTfcMMNdMkll9BFF11EM2bMoFtuuYXq6uro9ttv93wOR+XGjRtn/4wdO5ZKuU+3c9IapKY7M6IbNL3cFInTI912erkr6qVP4su90st7404vL08Ly94+JZxls8Oml9s13cZxT1qkOzMbwo3b0IqFolsQ2aI7j/RyEdb6IlG6T3ecots7vVyOfzk23CndzV59ug013aZMklDb6U4v96rp9uzTbfkaZDlGn7Bcy088dIznOe+HLFTIdcs70h1VyzBDTbeWXs7sP7JOmbrx5/L2tn0UqZGawchSrgOIdAMAAAAgG5l9l4pIZ2cnLV++nK688kr7vvLyclq4cCEtXbrU83nNzc20//77U29vLx155JH0wx/+kA477DDjYzs6OtSP0NTUpP7t6upSP/kirxHmtcRsqryv136eCPB9bZ2O1+qwBADP8+R+iWa1daT3QYRDdXmf77bUVKae29yefp+mtk57gjx2cCr6uXlvq2s7Ur/zW3d3u5zNe1OTXyV0A45DLuOm72dNReZz+3rS29XW0eloiyZjPay23BbdfPy525+5HebL+9L7VNaXdiuO4tjJBdO48XHA8Efrt128aCARfG4X5n7sCMtEb8e+tpz3T2p5y8vSY8RjL2Max7hxxoOdXt6eed7LQs6I+irauKct4xxstsZXRLD8jY8xEWRyn2jMVu1xYeDzUqextcP4OiKqe3ucY9prnQOcBeP1/jwej1mi+4SpI6jMup5wq0DTc0zHXHNbh72Yw/cPsnZcxk6Mz8opv3Olqizdgkx/Hd5/EbuV2nvMnNBAS9fsppfX76ZDRtdRvrRb1z39+iuLju2dqc+4vUuu0c4xz/Ual+tzAAAAAJBcYhXdO3fupJ6enoxINd9etWqV8TmHHnqoioLPmjWLGhsb6Wc/+xktWLCAXn/9dZo0aVLG46+77jq65pprMu5/6KGHVEQ9Kh5++OHAj23t4MhMGS195il62ypHb9zFk9ZyWr7yFarf9rL92DfXp+7ftGE9LVmyTt3X1pJ6/rNLX6Bdb6YmpVt3pO577eWXqG+9d5SrdZ/13stepPZ3U497xXqPnZs30nsd69VhsXrzLlqyZIn9vL0dcrj0Oe5ndqkAGUeYujP+FuW4ZdvPlK5KHdJL/u8B6uhN337kwQeUAZw8hiNnf/vf/6Naj8SANWtSY7Jmzbu0ZMk76r63G3m2XUF7G5tC72fU6OO2Ymdqu5r27vbdriqqoC5KKYby7o6MxzZuT+3zi6+8RROb3sxpu/Y0pj6fl/65jPa9nbpvjVrnqqS9Tc2xjBt/5n3WcbB5xx7HNrB27ehO/a2nZa/a/5defZ1G7X7NfszuptQ+Mdt3N9rPf3V3atzbmtPHw9s7Uvdt2Lw1p3191ToXhX++9ArVbU1fD4TOztQ2Pf3Uk7QqFahObZ/KEK+k9s5Oz/ff1ka0YU+lEolNq1+kHdb5u25Het+yHXPLtlvH3J6d6jn2deWFF6nlnT7q6U2N6dOPP0b1ziqGUMj+7Gttd2ybfq4/9dgjJNUmg9pT4/ePpa9R/bZXKF9W7bU+45b0sbt1U+o93li1ipY0v0mbtqRuv/n6q7Rk+yt5X+OY1tbWvLcdAAAAAMkhVtGdC/Pnz1c/Agvu6dOn029/+1u69tprMx7PUXSuGdcj3ZMnT6ZTTz1V1YbnC0ckeFJ1yimnUFVVsNnlFS/wJKyPTjvlZBozJJXS+mT7a7Ry12Y64OBptOj4A+zHvvLAW0Sb3qODDzqAFp1+qLrvd+89T5tbm+jIefPoRMux95ernyVqaaHjFxxDxxwwwvO9/2vnclrdtIumz5xFi46YqO57ackqok3rafohB9I58ybRr954hpq6K+iMM061I8Gb97YRrXiaKisqaNGi0xyvyWmp33/pKaKy8oy/RTlu2faTI9vfWPaI+v3kU06hfdxr/MWnVRbBmWcush/3vZWPqujm3AUnqpRUE8/9/Q2ibRtp2iEH06KTDlL3jV63h25+459UW1dPixa9j+LANG6dKzcTvfMajRszmhYtmuv53B++/iS1Wr2Yx48aRosWHeP4+4an1tITW96hwaMn0qJFh+e0fT9d9TRRWxu9/30L6IjJw9R9r25qpF+8/gJV1Q6iRYuOp2Kzg3uyL3tS/V5eU0eLFr3fmb79/GPq9xkHTqbXl2+iyQdMpUULD7Yf841/8jGViqqWVdfSokUnpH5/bSvRW6/Q6JHDadGio+37/rz6FRoyfCQtWnRU6G1d/o/UuSjsf9ChtOjEAzMe980XH1Huhh/4wIk0eXj6GN6wp5V+sPIZKivPPE+F25/lxbu3af5Bo+hDZ82lPa2d9NNXnqDmrjI6+dTTMxzHTcfcrufXE727ivafOJ4WLZpNf921glY37aRDZ86ikw4bS/RCakzPWnRaXnXdfG35wcqnqJuc15ZdzenP9Owzz7Bd2/te3UqP3fsK7avk4/tYype6t3cQvfkSDR821H69F/7nDVq6fSMdeNDBtOjkqXTX1n8SNe6ho448ghYdPi7va5yekQUAAACA/kGsonvUqFFUUVFB27Ztc9zPt7lWOwg8mTniiCNo9erVxr/X1NSoH9Pzwk6Esm1HkNfjGkFJDa2vrbafU19jCaiePsfr9PSlJpO11ZX2/VI72Efl9n1S8zh4UI3vdgyyDLW6esvSz7VcpYfUVtOkkYNtAdvclUq5ZcorJM0y/TyhuspKL+9LjUMYwn4Osp9DDPtZXqG1NSuvpB5LKLGI0B/LLt2tu9uosaPH873llWq0ca+1PiOu5Yzy2MkFfdx6rSgs17/6bVfKTC0luofWpY894dDxKSOqVdv25bx/Ul9eV5N+/UE1qVpxTm2PY9w6e9Mp27zYom9DV3u6BGHk4Fr7fJDH8Pmqlynoz++xxr22Kn2MDB6U2ld+Ti772uFyeG/TtkVH7Bxqqp2fY2116v25isDr/Z9anepj/YFpY9VjRjdUqnOEt3lPWw9NHpF5vXQfc53W+9fVpO6rr5HrCo9LWrTX19bYgjgXBg/qs4+d8opKu7Smq68r3SfcOr6YI/ZPLcSt2tZMfWUVeddZ8zVW/BLS199K+7zj+/iarY9FFN81cV9fAAAAABAtsTq/VFdX09y5c+nRRx+17+M6bb6tR7P94PT0V199lcaPH0+lgN6rWHe7rfMwUjM5U1fafWL7QrcMMxmpibMzu5fzNo2yWkep6LbLvdw0f5ZuOsUxUuv2dC/Xt43HJt2j2/lYce1u4kh4Ntdi7UWrbPfyZBmpdRraGpnQI45DtXZhwuxJKdH9zvZmo+FYriaBInykTr7Y6MZo7v2y3fCrKpQJl36fydiMz0+uifYy0PJz2w7TMmyIJWLDG6n5mxry/i9bu1v9ftK0MepfzmZhF/MwZmp2twSrlntQVaU9PrLvPC75CG799fWuD37Xu/1G1Cm/gqjM1OQ6IOe++h1GagAAAAAISeyzBE79/t3vfkd//OMf6c0336TPf/7z1NLSotzMmQsuuMBhtPb9739f1WOvWbNGtRj713/9V9Uy7OKLL6ZSQEzUMvr7erYMyxTdMunT2+IEdS+vNbmXd6TdyxmTm7FM4k2TaH3iX+he3X77yeJBFx2mfugO0d3mbVYkwrrC0Kdbd41PArbDcpZJv97bnIWJmzENtUp8sZ57bVNjTttiEiDi7h11f/M1O5oDtW/ThXNbV6/jOa2WCRaff3JMySIUs08zUXMfV0b3co/zOChyLo5uqPEX3R59uuX85L/L4oDOup0t6nNgp/YDRtXb96fP+bbc+nRLH/jOnnS7sAhEqCxipN4z+/WOrwGHW4tHuR7DOnKN1bsYyKKn/A2iGwAAAADZiH2WcN555ykztMWLF9OcOXNo5cqV9MADD9jmauvXr1e9uIU9e/aoFmNcx71o0SJV+/bcc8+pdmOlFOlmcaj32LX7dLsi3SL+9JZhEtEUEcOTa7tvrjX5DdWnW1qGWdG1dNQrM9Lt7gvsvq+QvbpZ0Mt26wLStC0sjMVJ2y26JaKpar496DFFuu3FjmRFutMLM/5RRV2gNBgi3czsSak67Jc3sKlY7se3qU93VL2TmcdWbaMPXP8k/fgBs+GijrsFmC6q9Uh3PVvia8JXF+x6ZoBko3T4Rrpz21d57dFW+zJTxoG+sOUV6faKdssxP8xyqhdCR7pdolf6wPP9IsjdGSa5wIsIMr76QoYdaTdcB2Za/brZSyBf5PorQpupcmUTyDHvvs4AAAAAACTKSO3SSy9VPyaeeOIJx+2f//zn6qdUkUi3e4Imk1fvPt2G9Ebrbzz5F60rk18vTH2E5T1FdEwYlhnplvm79OTW0aPfPBGNYK5tRN9mU3q5jBOPhzPS7XzsECvS7Se6JZrtiHDZ6eXJinTLIoAecc2WXi7RfjezJg+lB17fSq9sDC9YpDe6e1tENHE6Lj8m35Rj5u1tzerf1zZlN5xyR4tZyIqIFvHGizhy7ujnoIjehkGVKr2ZxTS/Hvc0Ny0wyHHZ7jqPw5ZPjLYMFt0LBu6FLfcimHsBzH01kP0ZbC2wCeMs0b01bHq5tb/2dUWll/dG0qNb4Ig5R5P17By/jJfDPUT3W1v30W+fepeuOOUQmqSZz/kh1wER2s5Ityu9vKJAFz4AAAAAlDxYmi8yUpfoTkUUg7Ng6eXWpM9Swnp0PFtKp0yEHTXd1kRcRIcd9dJquu10VoPo1qNtBQx0O8ZGTzt1bIsYLfXoNd2u9PJBqf1sau8KFekWAS7jnhTsfsVZIt2O9HJrDDwj3Rv35uVX4Ix0lxkfkw/N1oLJ9n3ZRaL06HY/V/1NIt1KdMvClx7pFpFaRfUuUW6s6ZbzK8f6dXntMUNqjTXl7gi2ewFDr+s3RbplfyTbQ5BzXvdxCBfpTi8aSoaJ1zkaFlNJjJ+HxayJqWN41ZZ99mfE2UDf+MvL9NcVm+i+FzcGfm8R1s708sxFT9N1BgAAAABAwCyhyHhFX73Sy02RQzvSYr2WTID5MXoapL+RmtORmakX0W1FujcbarpNL1+s9HLdvMkrWuqs6fZKL5dIt7fo7jLUdMtiR9Ii3XY0LuBn7xfplnrYjXvaUm2ZIhHd6d+jSjGXz247twPLgjtFW7+ti0e/SPfgmgqqszJBJPpsqumWMebjJ5fjxE4vl0i3Kb1cj3S7FsH0Gm/daFHY5xHplprurU3tudV0a8LYrumOKOXFVBJjf26GjJfJIwapTAY+HsVM7dnVu+hlK3uj0cfLwY18hvp1Va4xGUZqWc4/AAAAAAxcMEsoMqY6UEd6uWXslBHprtRdtJ2GXnY9d4BIiylqZEe6Jb3cUNPd55dert1XSAdzESR+KfTOmm7zAkdDiJpuZy196nf+U6EN4wqRXq4LFJN7uYjxA0enDLbCppiL+HBvi/67/ph8EPHIn2E2p3A9cp0huh3p5ZklHvJY9juoF1FuRZ9NTu260GzPYV8z0ssNols/xyr8It0G7wGJ8null4eu6bbGTP7l7bdruiOqcZZro0TQ1ftbPcu8DBVnTmxwpJjf/Hi6paSXOZ2/e7nJUwNGagAAAAAIBmYJRcY0UXe4l7trurtNLcOchl62GZRHnbNfejmnXXpFurm+U8SlRLCN6eXahLSQYjSIQ3tk7uXWgoa+b3q0qytBDuamYyRrTbeH6GbmWCnmK0OaqekRPxY+AmclyOcSlQmdvmCyI0u0u9k3vTxdWiGeBnoddYsmukWU25FuQ023fqy5z+Vw6eV+Rmrp393nIw+zjLUpxb3ZcmMX00R3evnO5o5ACyPprBNnpFsZqVnvG12k22Ck5hPpZg63UsxZdK9Yv4eWrkn1JmfCtMOzWwcaPTVSDvGm4wAAAAAAQAezhCJjpzy7otLe6eXeNd3irBu0R7cj0m1tB08YZWIpke6xQ2rU5J0F0s6Wjqzu5fpdhUwv9+vRbephnh7rikjdy9XrJ8jB3NTWKJeWYcIsK8X8lZB13X4RPzlmo4p068I5W123O9KtRzqlL3atR3q5XQPNkW5LqMrrmdKKebHB5JsQBH2hKG2klu4LHsRIjd/f7/iWcXPXdLMxHH9u/NLbAqSYc+s1U023ahkWsZFajSm9XK4FHtc820xtYyP9+vF3HY7toUS3oTNA+hrT61lSAQAAAACgg1lCXOnlFV7p5dlrut0tmNLp5cFFt4h73WSqzvobTyrFyGnL3nZHBNtUSs0Tfbm/oJFuLRXYC7veUjdS86zp7g5V062n7hZLdLPZ2/pdrb6PSbeVC1HT7RPpnj15mJ1ebur17IVfxE/ui8xITRNO25v8I91uMzJJTdddxvX0chbTcm7tM0W6XenlXqUiYUW3vjggolsX4kYjNcP56OdZ4FXTzeewRLuD1HW3u9PL9Ui3nV5eOCO1bNc8Ed1vbGmiR97cRpwQ8O/HHxRadNtGao7rQKZZI4OWYQAAAADwArOE2NLLKwKll5si3fakT2q6Q6WXO6NGkirLE0Y9hXL8MGddt+1e7mFgJhG3ghqpBYjom2u683Av1yJcerSrWOnln/9/y+kD1z9B7+1q8XyMHCNZW4Zpx4c70qkzfXyDOsZ2tXQqQ7Wg+BlKuReKWMwv/vtr9MMlb1Iu6IIym5maRKvLqC8z0m1oGabfr6eX19uR8NR9HR6LDCbjryDIecyH8PC6avt+t0iUrBN+nJ7G7/5smwyLSrYbu+HzH9cQ3MFc72+u/tXq4dsjdvOWjgx6urxd0+1xzRMzNTmPz5g5jmZbGRzharozy0yq9Ei3h48BAAAAAIAOZglFxqu9jEz4pcd0Zp9uQ3qj1HR3ZY8AZ0ThrAlsuke3cxI+wXIzFmMlvz7d+v3FMFLzW1yoMLqX59KnOzO9nAWOLeqLEOnmmnp2XeZtWb091ZfahOkYMSGZDJz26xeFZNE4bfyQ0GZqXiaB6j5r20SksFC+c+l7dOtTazLSv4OgC9FsNd2ysDS4KvO5klnCxxRvt4yhiEqJajvcywNGut3t/8IYBfJxlo6sO8cn3UnA/Hn7ppd7RLqZCZqXQzbckWa7P3mXnl4eTaQ7/doG93KP9+BzVaLdzBdOnGpf4/TShGyks0gMrQN7+jx9DAAAAAAAdCC6i4xXNFCfPOqT9bR7ue4G7ewT6446+WHXm7oieW7BLumtbKzkqOn2mFiKAChkADhQermMjW6k5lrgEFHCAsRrkUDGNtMdWibchY90P/7Wdvt3v6i86RjxEy9e7cLy7dftV9Mt98m26i7Ze1qDt3ASmkLUdMsxPtQKHutiNCNia/0rQt3oXu5T0+2sQQ4nuuU95fi2RaKH6PZaAPNLL/dyLw/rYO42MqurStfDy4JeVOnW0u87aJ9u4Yj9Usfw+w8eRTMnDrWj+zmllzsyjdKtA+FcDgAAAIAgYKZQZLzMvVgMyxxaTzHv6vbu093pinQHiSylI92pyaLbuVwYWZ9SKLuaO4Oll5cVM73cr2VYud0uybtPd/r5XlGvdMsw53PTqaWFj3Q/+qYmutuy159X6U2a/US3Tz13hugO4WBu13T7pJfLY/Ro6p6W1DEWFP5c9bTebOnlcow3VGWml7sXcmyzNCuarYtUyUZhczP/SHem23YQ3Nsiwthdk+5napgt0u1V082MN7QKNMG+DbLvcj2prU7vswjiqN3LHTXd4p7uswB38fsPpK+ecghdf+5sxz6z6A7qVSDp5XrLMNu9nMcBzuUAAAAACABmCgkxUuPURDstVRfdpppud6Q7B/fydPqss0e3MHKwRLo7M+pITYgYL056ufdhq/cwl3RUdyo13xYh7hVBFlGdEel2jX2hYIHx7Oqd9m2/9mamXu4m5u4/XP184uj9sr6/mKlxy6Wgn2kY9/KtmrDb0xpOdLsXSrIZqTW7It3m9PKUIEv36u52pqYr93Lrb9bzRXB5tf/T2/I9/c6OrIsL6eM7tS12CzOv9HKPSHdDkEi3oaZ7/NBg6eV6bXXavTz1enyZaLSOVYlQF9JIze+axzXdXzr5YBpj1aqL6ObhC1pvb4x02/4EfZ6+EQAAAAAAOpgpxGWkZjAZsif8Xd2+9boS0RRh2B7CSE3elyfOLAZEVNS7It2jBluR7gAtw/T75XGFQNoE6YZXXtvhSC83TIiz1XWbWoalbqcn3IXk+TW7HJHSQOnlWYycWIz91+cX0Kffd0DW9586ZrA6HlkIvrvDu548fHp5aty2akJ5d8hItzs9OGik2xbdjvRyZ0q3u22Ynl4eNNLtToe+b/lG+tTvl9F1/+dvGidCv16i7tb7eRqp5VHTPaSmyifS7S+69UVBObd08bvXKhcoaMuwEAuNAn/Gsk6xz+pXng1ZXHMYKtoLjNwyLLUdiHQDAAAAwA/MFIqMV8qzKQrt3afbWVccZgIqj+F5Owt6SV1110lLpDudXh6/kVqQ/ZTJsTJS80lzzeZgLmPrXmRIp5bmFul+bVMjffbOF2n19n2+j3ts1XbH+wdKL4/QPZnfl+tgw6SYiwAxHduS2SHjqveCFpEWFBGT8lnsbunwPe7SNd19mZFuV023bV4mke4OQ6Q7o6bbVSriMv66558b1L9rd3o70Du2JSO93B3pppzSy3mM5D2M7uWW6N7R3OHbTz1dzlJuC3/eFhGekrkQVfTX1IIt3bIs+HtwNtHganPKvheysKmXbuhGll6ZSwAAAAAAOpgpFBlJR6z2ib5KeqajT7f2eC/38jB9upn2zt50dK3GHOl2G6l5pZfLBNvdU7j47uWZE+J8It1uIas7F+cCC7CH3thGf35hvedjOANB6rnfN3VUiEh3tO7Jeq/jvFuGWanv6fTy9pwj3fKZTRpep45H/qh2WcepKVIpx0GDIb3cbQhm13R3+tR0i3t5j3+km1973c4WWv7enozz2oRE0KMyUnOXJMhCQuq1M88h9nHgz45PdT9zOi8TM7ktafRR13S3mWq6Q76HbaYW0MG8yxDptq8BWsswRLoBAAAA4AdmCkUmXQeaOVkUx3BpgcSTa5P4c0e69V7D2eDXkQgZp5iLgHBPwiXSza/NwpzNk/yia1w/GURY5INM9n3dy00twwxprg12NDBcTbed2p9jTbcIzHd3eEc939neTJv2tqnFAu4vnF10Rx/pZvYbURe4b3PW9HK3kZoj0p1bejkbwo2yjlOvFHMRssxQy0jNIbpd547db7ojdczL81Pu5R6R7oya7rTx119f2mTf75etkNoWZ6lHuqbby0iNQkW6RWjyZ2G6/nAkWKLdfnXddo9sl+CVMRQ3+uhEdzTp5X4LGV50G2q609cArWUYRDcAAAAAfMBMocj4Ge+MtgQEp3e621I5Ii1SVyw13a5oXZh0zXQdqTPSzQJDtpFTzMWV3Cu6NqyuKicBFQZ3KrCf6HbWdBvSyz2igRm1nF5Gajmm0Uvq7bs+fbclyr3goJE01jKB8k8vD1bTHZagNb5B+nTLtvG2ciTfEekOnV7eZS+cyEKVV2RWUrN5oare1KfbdUzZwprPDS2yqiLdLjdxr9RiO9Ld2UN/e2mjfX+2BSl3Jke9XUOem5Gae6HG7tFtSC0XRHRv9hPdklnjut7IGMr7RtYyzFo0k0W0XK55gu5gHi69PD3WshDXlSWbBgAAAABAwEyhyHg5HjNjGpyRbl106xN76cfs7tMdNLKkp2umU1orM6JeEkXc1dKpUniDiO5CRrqDpJenRXGv7wKHn9mUb6TbVZscFqlf5ki2Xruv89iqberfD0wbk7X2vJCie8KwQeEi3T7HthyzHBnkHtt6qnCukW4WUGNEdHs4mOuLSrXWYcPbIAIuo9+0GKl1cBZI6rl8CPA5kxnpNptoyWs9s3onbdjdZv+d38uvVro1aHp5jkZqctvULsy90KK7y7vxijLLfouXYmSRbkOfbnd/9fCiO6CRmuXd4Ih0a9eYdKQ7mn0FAAAAQP8EojtBRmqjXamyktqYkV4u0VxXTXfQCaierintj4w1nuJg3twRIL28OidTrKjTy5013d5jbQuTjrA13c56+rDoAnPNzmbj36UG+CQW3Vki8kxXd2FqukV0c9s4XfB44ZdqW6MtVrhTl3Ot6ebPcMyQWt/08mbNKFA/xDlazZ+xbLOIbd1ITRepvAhlR7rFvdxjkUHOr9c3p2rh/2XWePtvfotSIuZlW7yM1LKdi2m/Ao9It6/oHpQ1u8Frkc99/TGVdeSC25iOMyXciyXhRXdAIzW7dEOv6dbSy3160wMAAAAACJgpJMhIzV3TLRFMnlvrE2yZ9MmEr816zbCimyfPXpFuMVay08vFvKk8W3p54SPdfhE0Z023tGfzTi831XTzpN67pjs/9/K9mugy1XU/+fYOlVUwbdwQZRTGdcsMR4d5u0xImUHUke7hdVV2VkS23s1+bt76tnFKrtRzS9JEru7lg2uqMrJD3OiLSqybBln7w0JWRK4puqzODZdIlUg37yefm54tw1zH27lzJ9uLPP6i2xzpDt+nOx0h14+ZlgDp5XZJwd4cjNRcAjjqSLcIbT6vJfMm55rusEZqunu5nV6ePgaQXg4AAAAAPzBTSJCRmqTK7rQERLpHt9lBW4RfmD7djAgpNlJLu5ebIt3W9rR0ZHUvH2aJw71thavpTpteRdGnu9KzVlpvP+VV052LezlH3kVYedV1S6swTi3XFwf0dk/FSi/n6K6dYu6Tbhykplvu48dsswT8ASPrc4x0d9mfYbaabonuuqPHLNzleGLt6u43zYtRIlJFqOnHHT9fDpOMmm4twjtx2CA65oARgYwGOaXdKborckwvT70Xb59uJKc7sXsxZVTqM1m11dux3qtbQkakO+KabhH7etZFWGEv5717IcML0+KbnGf8N4huAAAAAAQBM4VER7r7jJN63T03bMswfXLMfazTfborfdLLuabbP7pmi4oCRrrbQrmXc023X3q52WzKbZKm13I6J9zhI93usXl3R6bofnFdKrX8fQePsgWHpLZ69hQvYK/gCVa68WafyGeQ9HK9Fl5Sl6ePb7A/1yDp64KI0FR6ub97uSxUiIC1o8ed6bpyPh94gUF/HEfI5X3kObxf8lmIIZ5pf3Xx+aEjJipxLOeHX5mAGLdlppe73Mt7zQtCAh8z8jc9k0NKKfxE95xJw9S/63a1erZhs+upXeeh+7wslHu5fG78WYRdaPJayPBCfDOc6eXpbBo5buFeDgAAAAA/MFMoMh0+ZlMiunlyzBNbO4Lpeqy7ZViuNd38vLTRVOZzR9XXpGu6xUgtW3p5QY3UurO7l2uRaN/0cksEmYzUfCPdmnNxWKSVkrDGlV7O0Vo2WGP9N8sSPywG03XdZqEg26I73EfFhGGSbpw90u0X9dP7dEt6+UFjBtvjqYvYbOi11qOlptvDSE2cv+tdQpajvqZWe7aRWmePQ9y7/67X5vull3/oyInq3yCRbmkZlpFe7nYvz9JJgI8Zk5maHen2SS8fWldFU8cMVr+/tH6veTvt643ZQK5QolsW0XLt0S0lCWFEd5dPyzB9oQSiGwAAAAB+YKZQZNLR18wJIwsCSaXkaHenh0GWTABlQujuNZwNeW+OHNmR7hqfSHeLVtPtoevSRmqFSS9n8yiJdPm6l0sWAEe6g6SXZ4l0u2u68zFSk7GRCTobqYkpFvPyhkb17yFjhjiikem6bnP9eVdvYdLLdWOtIOnlfqZSupHaNkt0c/3wsLrUcbOnJfhijYhHzlaQSDefL6aad0kjlnNjsBXp5IUtkxu+PI4XeOz0ci0LRBanZHtZ97oXZvYfmepvzmnlB40e7NvGy7mtznMxVyM1GRu1n9r7iWP3EJ9INzN3v+Hq3xXrU1kXgWu6q5yvG1XKtd3i0O04n5PorghV0y0ZLXrLMH1xSzwDYKQGAAAAAD8wUygyfim4HKESN+Ydze2etbp2yxqr53FYJ195XHu2SLf0Dd+Xrun2mugXumWYTLizu5entq+tM53+HbZlmKSUMm5BpbcLyjXSPX3cEPU6vIigi9mVG1IiZ/bkoUZjLFNqMi+GiNYsxMSfa5ILlV4+rqFWmbWFXayx06S1mm4W/KZMABGyGenlVjaJW7xJJJtrocXhWp6j/m79LpF5HnNJTRcOmzCU/v7F4+jWT80LVX7hLp+o90gvz2Zq6FxU0iLdAdLLmSP3T2VZiIt+xnZ2evTprvY3lMsVWYjkRUY+N3Pt0a1H+d3ZA150GyLduuiWYwSRbgAAAAD4gZlCkfGLvjK2MVRTh2dNt0Rz2bWaHyOT8MB9urW+wbZ7eZZIt0TX3ALD5F7u5bKdD7qJmLgZmxCRrDtTm7IK/NzLbXfo8rKM/bXHPodId6NlMscGdVMsEzHdwVwi3XMmD3duq0+kW98OSeGOkvFWenmQXt1+RmpVupGaFekeN7SWhlsO+btDpZenjdT4mJdFCZOZWjrSnZlenl6s0iLZljh3upenjx9ZnBLHdS+xNXvyMJWqLQwNsCgl7yeLALItLBD1c0rSy/2qCYzp5YZFBBNHWpHuVzY2OhagBK9Is9sXQq4z+aJf19q7e+0FtVwi3fXV3ottJiSLRBfajvRy6zpjusYAAAAAAAgQ3UUmXWfsIbolutzc4RnprrbEFU+IZQIcZhIqESKe5Et00i/Sze7Stouvl+i20sv5cbpjclSk6zjLfSN8EumWbeDNNfWvFtHN0WYZA8GrXZjDOd4gRoJGunmBQtKOxcGcFzVe3rDXI9LtXdMtKd1q2zQxEBW2e/netqyLKX7p5XIfH3PiVq5Hut317n7YtdaWeBzT4N2rW44DOb5t93KtZVidI9KdFrpuI7XU352R7qAp1MFqup2iWLaVh11fdMo5vdz63a+mm+Fjkxcy+Nqyauu+wKJbF8e8bW4TwlzRx5ij3GGNI42R7sBGalY7Pu3c4uuPDL0cX4h0AwAAAMAPzBSKTKeVJu2VCqz3HbZbhlV6R1sl1ZInuSZxaUImq3qrJpN7+XCr3pYjv/JYr4k+i2GZeBairjudeusvGGSiL7WWPGE3Red14eGOdtsppYZ9tZ3jtVrsoIhQ4wWKg8ZIpLvZru9mIcjjeOjYIY7nNQzyTi/XxX/Qzz8X93IWF3qqsu+x7RPp3rinzX4MLz6MsCLdewK2DWPhL1FKEZZpB3PvSLcIWTt6rKWXG43UOtJGavqxIs+XRYKgKf0NWUQ371favbzCFrW2uNNEYjYjNe9It3OxwgsWlUdY0W5TirlXerc+jlFFuRk+f0V466I7t5rudA/zIHiZFMp1RszvILoBAAAA4AdmCkXGz1Fbj3Sr9PJu/z7dHAnX61K9Ur/dyIR4Z3OnLda8eitLhE7amHm9Bb+33avbELW878UN9D8vb6ZcsU2vsky0RSjLpNor7ZMXD/SezTpSr20S3frYh0XqeYfrkW5LdK+0UssPnzg0I0LoZ8IlooA/w6CffxhYWEk0eksWMzW/mu5qa9zW7261TdTUMSNGagEXajgzQdL/RQzbotvgYO72LDC5l+u1yfWW6OYFLxHIeg202728OqJIN18XZL9EzPL4yPboIlEvf/DCVD5hu75niXTrKeYmMzUxNPTr0x1VPXemD0UvtXu0LCuE6ObWg6YFLTFWk3p7iG4AAAAA+IGZQhHhaJZd9+oRIbN7dav0chFUXr2i0yZqYSa5IjKkD69f9FjquiV11yu93M9MjQXKN/7rFfrqvS/nJFYd7cKyTLRFiIig8kv/9XIwF1FjSo+1x74nj0i3Q3S3OEzU5kxOmVgZa7oN6eVeJQgFcTDPUtctYsw05iJKRPiNtVLCR9ju5cFEt4hIPgxFSEt6uSwM6Yi4cpuT8f129oR27ujHlxzz9Yaa7z0Ri25ZPHNvj8lMLYjo9ot0ZzNS083UTKLbZECnblcXTnSLj0NUkW7OHAjiPZHOein3KGOxFvfgXg4AAAAAHzBTKCK66ZVXTbeeXi6Cyi3QRWDx5DstRstDT2DZIM2rntvdq9sW3T4Tfanrdke62fma57ccPTRFwYMgKa3Z2qJVuifDHuPs52DuW9MtfbpzcC+Xfefo7oGj6+3PmYWYmKixAZcb273cEOmWEgRTVD76um5/B3MpQRhpHTM67kUBjnTrCzVBa7pt5/KaSjuyb2eHGER3a0adtNUyyiO9nEW0RDV3WIZvpprubEZqXqLbVCLg7vesL/aIyDdFunNOLw8Q6ebFH375DbvbMhYzvESvfjuqdmFuB/Ooarp5CHU/jDBGavrxLIshftcZAAAAAADMFIpIh9b2ytO9fHBtZk13Rj1h+rbU2da5euQGqum20sv93Iwl0p1OL/ee6ItD817LpVvgqL2Qa723nQpcFTDSLZNhH1dhk9lUtpru/Pp0S3p5tXrvsdYCy5tbmtRP1ki3qae4ONwXML11QgAHczb32ml9zpKt4Se62USNsWu6Ax4Xdj23dszKQpW/e7krvVzr0+1ufSXCWo5bXaTafbq1lmFBSC+cmNOaxYPAvahk6tWdrX1fapudIp+juu76dj/4+eIt8JJl8BempturfCZX5LznbAo70h5ioVHQ6+SD9Oq2jdQ8Snxk0RN9ugEAAADgB2YKRUR3yc6WXs4CRlLRM9LLtVRHmVS7hYMfMlmVqKGpXVhm2zCJdHu/rldN93YrYhjWpVpHBFLoSLePGE33v/ao6TYYk+k90sMiixES3ZUUc6515+j6qMHVdl9s53bGm14ukW7pr21ib1uXnSEgx4yOe1FA0svD1nSLUBJR6WizZ3IvlzZZlpCuN7QMcy9YyTEmmSnmPt25RbpZ7JuOHTsi7yr1sNPLtRZ48vQwkW6+lsj+BEkvZ8RM7SUrC0PwijTrtyUyHRUi4vNNL1d18gHrunmhwivrRdLN5XNDTTcAAAAA/MBMoYjofYy9IsYsWPhPPNmT9FavKIs+qR4UYpLr7nPtl14uqcJS/phLTbcuhnKNdAdOL9daU2Wv6a7yr+k2tODSe6SHgSfwesswt+hOp/Qa2psNyp5eXtia7pRA3uQT6ZYoM0euTdviXmSS10y7lwdbjGnuyGx7NWaIlR1iMFITsSpp2nqkW5yn3cdUZrQ5s0932jQumPCTbAWvaLdtFOh6b5NAtPt0+3zk7mNbf359lg4AwpH7pbIuVroi3V413Y5Id8QiVK5v7d09nu8flCEBRbfeoUBf6FS3XQtyEN0AAAAA8AMzhYiRHrq+zuU+s2UWLGIutcmqoc1ILy83ie7wRmqCn5EaR191/NLLJWrpFtZ6TWiuNd1B08tlbORj8E8vz6GmO8dIt94PXMbpIKuuW0TY7EnDPLbTux5YHO5NUfmokOi7n3u5fMbiJO7GLcTHWqJbnNFZALn7pZuQsdJTviW9nDM3dEMyPaXaL708M03aHG02/S1oWjHvvwh2k5ma3TPcLbqlb7ieXp6DkZpkCPD++/W51zly/1Sk+5VNTaR/NHakubqIRmrW6/Hnm37/4CU1OkEj3XoJiVfLMGEgpZfffPPNNGXKFKqtraVjjjmGli1b5vnY3/3ud/T+97+fhg8frn4WLlzo+3gAAACgvzJwZgpF4PG3ttNBVy2he1/cYPy7iIpspjuSLis1tG7BwsJXhLjUI4dpn5MR6dYieW5GWiZVgt9EX1Jo3cJaF91B04jdmEyvTLi3z2+svWqlfft02y3DwkW6Zb/5+SKkDrQi3cIcK7KYsZ121DLTcVkWCAo56R9vie6tje2ei0ryGZvquU2RQKnp5n2TYQ6SBaGLRz1yKZFV/VjjRS7ZXGOfbo80Za+6av35uUR0/RzMvcon0gIx073cL73c3TIsjHO5cOCoepWVwdetTSmTfWdNt3vctDR99zUmMvfy7l7tc8vtmJcsiWw13bpZYobozrjORLu/SeWee+6hK664gq6++mpasWIFzZ49m0477TTavn278fFPPPEEnX/++fT444/T0qVLafLkyXTqqafSpk2bir7tAAAAQJxAdEfIX5ZvVGnYD72+zddIzS/66hDdVmSxqtI7zVkEY6iWYa7Jqn+kO7jolrRpru/V0Q2ucq3pDlrH6Z4cB2kZ5tmn2xA9ttPLQ0a6ZSFi6CAuH0i97kFjnKJ7lkekW9LLU271PUVPLx87pEYJY15oELM0T9HtOl4EPVuDX0uOcY66puu6u4IbqWk13TyeJjM1PZIpbbhEdPJihSyEeAld+3a1T6Q7hOhu8BHd6UWlykiM1MSvgMeAF2pk3PwW2NzwuEq/7rXN6f70suDkPhdrNWOzqN285ZrV0dWTV59ux5hqdfLZIt2Z6eUDM9J9ww030CWXXEIXXXQRzZgxg2655Raqq6uj22+/3fj4P//5z/SFL3yB5syZQ9OmTaPbbruNent76dFHHy36tgMAAABxMjBmCkWAJ7YvrNmtfl+7s9n4mHQdaLBI9xYrvdw0oRNBmEt6eUbdqF9Nd0Z6OWVtGdbYGn1Nd7o1mn+krsI1Oc7FvTzdB9nUp1vSy8NFumW/JZ2aGd9Qa39u3EJMIqFu+DESWXPXA0t6ubsEIUo4lVaMz7zqurNGurVjmBdydNEiizXScixITbe77ZXUdevHmjjYs6iWlGoW33IMyzb7pUnrzzWJ1jBiyy/SLQLQq6ZbF9328elrpJZ6L35oS2eP/fzB2mJFEOZaKearG8scUW63yJaxkIWAyCPdBiO1XFPY7RKDLJFuKSHhXXKn5FcOwJruzs5OWr58uUoRF8rLy9VtjmIHobW1lbq6umjEiBEF3FIAAAAgeeRWFAcyWLuz1Y4Crt/dqibG7kiUXdMdUHR7tQxL3WdFuq0JfLa0ax33hNjPvVz6dAtBjNQyWoZlqenmye0Pl6yi+QeNpFNmjDW+dltnb6D9rHKNuZ+Lspd7eVeQlmEh+3S7TdQYnsiz2H59cxPN8YhyS8SRo6QsSt0LBPa2FjjSxg7m7F7OP0cY/r49RHr5OKueW2APgzXUEmhBxtQyTK8l153yRcjq0WMe8/rqShUB3mm1zMuo4daOMXfUO59Id5D08voQRmp+tdl83PPxyxF9PmbsHt0h0suZEw4ZTT998C1a1VimFr46elPvyZcB94IDH6e8qMG19VG7lztahuXhXu6Vsm9CzBJN55Y78h21cVwS2blzJ/X09NDYsc5rNN9etWpVoNf45je/SRMmTHAIdzcdHR3qR2hqSrVT5Ag5/wBveHw4AIBxAn7gOImW8n4av+T9KqOyfrt/TFTnQNDXgeiOiGXr9jiE0KY9bbTfyDpjenm2ibpE7QRT6nC6pjsHI7Wq4JFuTm2WyXsuNd26YZVXTfeydbvp9mfX0kNvbPUW3V3B9jOjptsn4mbXvVrR00z3clN6eW413el2Yc7MAU7fZdF93NRRvs/nBQIW3e5It4j/6iKI7uXv7fHs1Z0t0q0fw1LPLciY7A4iuu2IrYfo1hZ47OiuKzrNkU4+LuVzzqzpdtaL69RHILqbfI3U3OnlPkZqPgtgLIA5G4AXe/gaYY9bSNF92IQGmjR8EG3c00ZPv7OLZk0eYY+ZyVSRTRpTortARmoc6Y4ovVyyJrJFut0LeQM10p0vP/rRj+juu+9Wdd5swubFddddR9dcc03G/Tt27KD2du+2hSA18WtsbFSCirMQADCB4yRa5jbMpf4IC+6pdVPV730Ubs5bKmz38CMJy759+wI9DqI7Ilg46qzZ2ZwhujtDRrr9RHdGTXeYPt1ukeEzEeeJNaeYb7PaMfm7l1fZEX1OA+WJsh559Ip0b7MewxN7Fhfu6CLj5TRd7Jruqhwj3bLfeno5843TD6XTDhtHx00d6fv8tOlbt/GYKmR6OTPBik5vtkoe3OxozlbT7RPprjcb8AWt6dbd0PVFAU6rNgpZ/txTwTMP93KfSLc7vTyHmm6z6PZvGSb9xh19urO4kPMYpUR3V9qAzrVYkQ0+30+bMYZ+/+x79OAb2+jgcUNT2+khqmXsoo78SuScryteRm5BSdfJZ4l0+2SRuO8bCJHuUaNGUUVFBW3b5vQs4dvjxo3zfe7PfvYzJbofeeQRmjVrlu9jr7zySmXWpke62YBt9OjR1NDQkOde9H8xxecsjxXEFPACx0m0LG9aTv0RiXCvaFpBvdQ/syLGjBkTyev4LSTrQHRHAGd7SqR7ZH017WrppHU7W4gOJY/08ixGai7hYprY5xPpdpsc+UW6pVe3iG6/6BpPZjnSzBFEFlDjhlY4Io9eke6d+9L3rdnRQodPSk3sc3MvLw/vXt4WvKZbhHjoSHerOdLNwuh9B/tHuR1R+bYu0uWmbEchjdQk0s1ki3SLoZkb/RiW+nBhuES6g9R0W4tM7ojtlJGp9mvrdrXa90l02F2HnZky7v33jOfm2DIsW3p5Ww7p5dneWhaVeKHG7m8eMtLNnDpjrBLdj7+1kz41/wB1n1ckW65DUbt5m9LLc67p9lhscyPXAWN5T/nAi3RXV1fT3LlzlQnaOeeco+4TU7RLL73U83k/+clP6Ac/+AE9+OCDNG/evKzvU1NTo37csDiAQMgOiymMFcgGjpPo6K+CVCLcvH/9dR/LIzr+g74OzrYI2NXB0doONTk7a/YEdd9aFt0uOrqCGam5hYtp0ieRlpxEd2W5wxDNz73cbabmN9Hni/gwSTG30qlFjEmvZ3Y2d7e90h2x39m+Lz/38hDp5XqkW98mv5ZhkmEQtk+3qaY7DOJgnmGkVgT3cma8FUk29ermsgkRkqMHm1f7dHEqryUMrxf38iBGapl9up2iu8Ugup2PHZJFdOu33SKVI676uRNVTbdE5d1GgfL+kn4eNL3cfXybWq0FZc6kodRQ1afG/tE3t/lmnMj9kaeXW+Pc3p1/enl6IcM/s0LOLTnnfdPLB4h7OUeguff2H//4R3rzzTfp85//PLW0tCg3c+aCCy5QkWrhxz/+MX33u99V7ubc23vr1q3qp7nZbDYKAAAA9FcGxkyhwKxuSk3AZk8aRtPHD1G/rzGJ7p7o0svlPhEhYSagLI51M7VsbYT0tmF+6eXMUDFTs0SmRLoPHTfETocWAe1OTWZWbzdPxoKml2fWdPull6e2levVOYImSP26X59ueUxQZDzE4T0seq9us+gucHq5tWiyyZBeLoZkLDxkccDXSC0j0p3atz0BIt3p9HKX6B5VZ4+zZBXYottDyApugeiXXs7Hv/56UfXpbrNrus2RdUef7gBGam53fnl+2PRyeZ9ZI1Lv+feVm30Xv+xId8SRXznvVcsw61zNNb18SMD0cvs64NM6kOGPodBGhknhvPPOU6niixcvVm3AVq5cSQ888IBtrrZ+/XrasmWL/fjf/OY3yvX8ox/9KI0fP97+4dcAAAAABhJIL49QdB9z4Ag6YNRgn0i39Okuzzop5MdIOrqfkZoQNrLEETsRv1kj3VYkMpuRGmNHum3RnRJp+4+sU6KMHdk56jumvjJDtDHveIhurz7G2cbFb6w5lZd3h+fWLExkYt/j16fb+ixyTS9313QHRVLhvdzLi5VezlkJHNnWMwikbp8Xi7wWZfi4kdIDqb92p5cH6dPtFbHl44LN1HiRh1PM59RV29Fjv/Ry/vzdx4h+jJkiwyyMZbEravdyt+g29unOJdKdR3o5M3tkHz2zjWir9Vl7Cd4DRtXTc+/usjMPokKubzzu0tUhX/dyMZfLaqTm07JxoKSW63AquVc6OZuk6axbt65IWwUAAAAkm4E1WygQ74roPmCkmnRKP2NxKxdksphtkqYMLrRot7FPt0v8hp2A6o/PFukeqUW6s2huu2a5UdLLrVpwdmQf6hHR3Lkve6Q7aHp5Zk13RRaHZ4kgp4VQ2qnd1CpI+nSHNFKzhJaMQVjs9mZeke4CT/x5sUDMrLY2OqPdUkIwysO5XPjkMfspd/oDXIJM0suztQxjsdncaTZSY6ZY557yU9CEqnuhRo+S89/cCwX6+WASqbpoD5NW3BAgvTyjfZm1LXz8S41x2nOgLJgPgN4yLIdIN3NQQ5+9oOZn3Lj4rBn04FeOz2oMGBZZ5NHN9vJ1L9cXMkzIgpZpnPVIdzaPDgAAAAAAiO48YXG9uyMVxZu7/3AaNbhaTeo4A3S9Zuqk13QHmaRJCySmqtI74iqE6dPtjozXh6jpLi8LF+mW1HHeH4nyukWHXtP93q6WjMUKZ0ulsDXdWbIKDGJWarrNrYLEvTzXSHeO6eUepm+26M62GpInLEwnDBUztfZQzuXC9z84k353wbyMtOigRmrcd1tK703icYrVLUDquiV67BbO+m1ThsigKt1IzRzpFqoLnV6uvb/0HQ+eXh5NTTfDgd2Tp4+2bw/yMCjkaxuXkmQrQwmLLPjodf+5prDbLcOyGKnZXQx8ykwGYqQbAAAAAOHBbCFPlq1NuZbPnNCgJsg82ZRot7uuW9LFg0zS9Ei3Kb3RHWELm16uR4BNwkKHFxKEbNE1u6bbEhbbrUg3749EwfWJM0cv2e2dkVRvd2o+P8au4wxd010RMBqoiW6fSKJM/rM5H+uwSZtd051zpDtzO4uZXu7nYJ7NuTwbshjDCx9+GQSy7yx4TILLM9Ltk15uWsTRI92mc6NeW6TKRXTzfki0Olt6Oe+niD7Zn1zSyyWVOtu57sdpM1J1u/mkdueKXN/2tHT59gkPgtS1uyPdfPuNzU2Zi2/Z0ssHSD03AAAAAHIHs4U8kVZhR08Zbt8notstHoP26Q4iut31xmFTLfVIVbbosW6klj3SXW2s6WZBZhtmaSmiLM5FgBw2YagxxZwdi9PbXaBItxZ99KvpPnD0YDs6v0uL0Luj8iKMGE7tFSGfe6Q73vRy3YH+vd2tRtGdLdLtJ0blsJLFGhOSIs2RSpPgcrcNk8hwRqTbkV6eeTzp97mdztXfNVEeLr08/VruKGtadBuM21zp0GGN1PjYzjfSzSw4aKT9/FxTu3NFznu7njuP97cj3SpzIn2efuMvr9CiXz5Ny9/b7XQvz2KkNhB6dAMAAAAgPzBbyJNl61ITtKMPyBTdEnETJG06WHp5bZaabud9YSNPEjniaG62SaOjpjubkZpELdu61CKDCGzeH1uQa2nEuywTNX7eNMvh/J1tzUZBEkh0VwTv0+10eO4OVMvJE3Y2hWPe3JLZ3mz5e3to5tUP0o2PvG3fJwsQHCXPtZVS2r3cI728CNG2g8emFhze2bbPLLqz1HT7fWayf3513WIiZ6rnNrUNE3dqt5AdrIlmk3jTHx9lpJvPe8mUcKeY+5VP2CLR2h9JBggT6W7Js6Y7tf3l9IFpY9Tv9VlKUqLGfd7kE2mX8WS9rV9bXlqfWkBduaFR/SsLZVUmbweklwMAAAAgBJgt5AEbSq3f3UZl1Edz9xtm3++VXt4ZYXq526U7VyM1nuRnS9N0uJdni3Tb6eWddq02byvXeg+rd6aeMztbLBOuwTW2qHNHusW5nCf92UR/mD7devRRdwWXyLupPy8zY3yD+veNLanJuc6Dr29VKfL/++qWyNqFpbbTK728ODXdetu3t1yie3ueopuRLIjdVvqwiX1ZorXutmEiNHWRnbpd5Xve1DtEt38kPKzgMtV192jlE37p7hnp5VneWhYn+L3EqC2fSDfztVMPpQ8dMZHOP2Y/KiayWCHkE+nm15IFNcmeaO/qoc2WQSD7SmSNdGuDj0g3AAAAALKB2UIevLB2l/p3Yr0z+uaVXt4eJr1ciy6bJn1uIR52EiqRo/oAESt+rEzWs2k7ERUsfESMsaBmsZxuDdWZ0S6M68YPHjPELLrt1mbZ9zFMn26vCLJfn25ddJsi3Ss37FX/rtnRQo2W2Jb9zbWe27mdaTMxprPbisYVYeJ/6NghdgYHi5SoIt26g7l+bLjJ5sAtbcPUNu5q9XQE14Wn6ZjSz6Vs7uU1EYhuvW+9qSWevJ/sf1gjtS2N6Rr8XPp06+w3so5+ft4cOsgqsyjFSHeq13q6BZmeHcG8Z5UnSE23qQe3vsiFSDcAAAAAsoHZQh5w5HrC0Fqa2tBnNHRiMWJPlHv76J9rU6nok4anamP90E2pstV0c/A57ORf0q7dJlPZHMyzp5ena7qlf7MIIYlm6m1/0qK7hqaOSU3k1+xsdhhqedW7Bop0Z00vT6fgumu6KwyLHcyMCVakWzNdYnibX92Yjn6/vHGvI7Kfl+i2IvJ8HHX2ZjosFyO9PGWGV6Ui+e/uSC2McE1sUPdyP+wFGR8Hc/mM/FKkdTM1iQy7o9W6kB5kOKZYREkmiUmkOiLdFRV5i25JLefz2B3RZUbWp8Z14562UEZq7jZzKQO60mxvVeva7nyN3GSRVGrd1+7QRXeL89zy6WLAQHQDAAAAIBuYLeTBufMm05NfO57O2q83Y2Itjt9S1/3M6p20taldCc8TD03VRfqRrU+3XmeYi5OvTFrrA9ZmSop51vRyTVTYYsyqTx86KDOauUsT3WzUxaKDa6rXa2Zdkl5uEiSFcC+Xmm6vSPd0K9K9ekezI+LLt/WopUS9820XJp+XbE+blmEuKbDVHgsEUcLH2CFWtPttK8WcBZ2UTeSXXi7Hho+RWgAzML1tmIjZej8jNQ/xNm1cgzJRE/M4zz7dEUS6W6X23OM8PsIqXRGDL4l0Z+sk4K59zze1PE5qq13dGvI0cnOn7OulQLy4weeVfR0wppfDvRwAAAAAwcFsIQJM824xdZLJ3F+Wb1T/fnDOxEATdYlueffpLssr6iPpmkH7ex83dZSKpk8bnxJdXkg0lyP80lpKovZ2n+7WzJpuFmwcRZdo9ztainlbV3fwSLfbSC0n93L/mu7xQ2vVfvLj9FT4ly2R7b6db7swhsWY1HW39mSml5tSYAuZYr5q6z5HajlHVXM1iWPSzva5G6m5I92SZVLvk17uVZZx3+fm09PfPMn4XvnUdDcYI909nlF3Zt7+KZPGF9ftUZkFcnxm6yTgzgjIN7U8TljY6rvr1Sc8KHIMSCs13fSSy0v42mVfB7IsepZq9gAAAAAAigdEd4Gw67q5tretSxlsMR+dOynQ83kyz1E7DmbpAlzQ04lzETsSNQ7at/erpx5KL199qt3WywsWKTI5FhdyO7283r+mm5lq1YrqYrbNyqcOUrcevmWYd59ur0g3C2DbTE1LMRfX46MPGGHd3qtEUrqmO/dIt54ubIp0FyO9XDdTe9sluvOJcjuODb/0cjFG80sv1xa7xJwsI9IdQHTzOeX1edXn6F7uXdNtToMXZk8eplLD2SNhw+426g0Y6dazI9wGcqUGn3N6inm+6eXuNmxu/w32BPAzKXREupFeDgAAAIAsYLZQIA4YnW5f9L+vbFYpuNwS6zCrHjgIf/z00XTf5xYYBY3uXp6Lk6/uXh6UIOKehYCkbEu0WrZfIr0sOKQuVU8vZw62Iqm66JY04SAT7Yz08izPSfe/7gpc062nmL+xpSkjsv3JY/ZTn8+ulk6VqiqRfYnm5opESdt6ygw13YVPL3eIbmtBRfqw5y26DSZ7OdV0W6L7LWtRwHSM6+7VXunlfuTap1sX3frxJq3NvI5vPu8On5ha7Prnut3pSHcW0c1CVR8rU8/xUkIvL8m3T7iMS7NLdLNHh9R124tvRk8N1HQDAAAAIDiYLRSIA7W2YZJazlHuMLXX+4+sp7lWaqnfpC+XqA/XrOriMUpEXIshkfQcl5ZZPJcVcydpKyaiO51enhZN71omR16RwMgj3T3e/Xkz24Y12XXn0kqLI90yrhzttiPdebQMY2QxQ+8a1mWllxerrvQQy2F+0942le6djnSn+8rnwgirnVyQmm4/8ShtwzqsOnM+HtzHAJ+DEu3ORbzV5xHpls/QlF7utwB21JRU9sSL77HopkD+CoyeHh/k/Eky+qJfPqUMTH11WnTzZ8ELZMwJlt/Gup2ttpmjKeNFvw+iGwAAAADZwGyhQEht6Zubm+il9XtVZI3ruaNCT3nMRXQvnDGW/nnVQvrCiQdR1IiZmhUostPLeXIqrXp4ostZsjLZHTXEKbrf3d6iouGvbWqk255eo+477bBxoSLd/KtXinhGTbehZZhf+q44mL+5pUmlkL++uVFFIHlfxzXU0uxJw2zRHYV7uR6Vb9NruoucXj60rkrtn0S7o3AuZySV2z+9PHtNt942LHXbbE6Wj+jWxXGuLcOajOnl3osJ8yzR/c91e7T08uzvp0e6B/uMWymgX+fyTS+XEgVeyJF6bs7WkEyk9btb/I3UdNENIzUAAAAAZAGzhQIhaa4iik46dHTeKbheke5cnXx5e8K6ngdhqKsWVm9/Zour1k4lHmViK+7o+4+oU6nS7AK+dlcLXXHvSiWCTz9sHJ09e0LW9+b9EbHMBkfZ9k8ijxzxkpT3tIGS93O5TzFvJ0fIOYVcnMq5/pbfc85kTXTbRmrRRLpNNd1+2xo1h9gp5vsiq+mWz5/rluVzyMW9XF/w8nus3B+mvMLoXp5jerke6c6WXs5IxguXXchCVTYjtQzRXeLp5TVRim6tpltSy9mHQ67bXNMtpRsmQ0V9kStbW0IAAAAAAMwWCgSnP+rthoIaqAVFn/Tl6+RbqEi3oBvBDbfSiDn6u68rLQwkXZQXEw4clYp2X37PShVN5dTzH3xoZuAFAolCBZkMiyjh4GGzVTsuQtYv0s1R+4OtVGtOMX/Z6s8tYnuO1eaJI/WSQp9vTbdsa1u3VtNtLVoUM9p26NjBdt20iG49upwLLJQ5C4IXP/Q6eZORml9Nt942jKnzEJqySGAyKcyGCHU+zrLVVZsyBTKM1AKkl4+or6aDLJ8IPqaCGKm5swKyjdtAqunW3culwwSXBO1vHTvrd7XarfBMfgn6IlcNIt0AAAAAyAJmC0VwMGex9YFpYyN9bX0iGKSVVjHR06hZLOg1j1LXzNHffVYmsTs1WVLMX7GE7I8/cjiNDJG+bIvuAKm/LPZl+8TwTCLdfjXdeoo5O5iLidqsSSnDqwNG1iuRw7XFUi8ugivvSLeWXm47LBexrlTv1R1VpJsXkY49cKTd096EjGO21ld6pFvKGdwsPmsGff+Dh9GCg1LvGYYJwwapBbV5U8x+C6Ej3dZij9cCgbuuW47PYKK7/0S6dffyvGu6tUi3pJfzccOfLV9bOUOJneI9jdS0awNqugEAAACQDcwWCgi7lYfpzR0GvaYw3wloISPd7gioCPJUpLvMYaLmFt3Mx4+aTCdPD7dgoaeXB4FT2nXztiA13bqZ2tJ3d9H63a3q91kTUxFujoBK1FvI20htUGZ6ebFrunUTPj3SHUXpBPeCZ571EN2SXi6LD15IirBfnTQvHFwwf0pO/c35fHvi6yfSXRcfG/q5ek23pNHbRmpZzmOp6xaCpJfrY1XyoluPdFdF516up5fzOT95eOp6sHpHs2fLMH3RE6IbAAAAANnAbKGAfPGkqSqa9o3TD438tfN1Ly8kek23W4xJaygV6baCfaOGOMWotEeaPGIQfedfZuQ8NkFNrmZa7/fapqbANd2MOJQvW7fbTk/Vo9m66OaIa76TczFSazVEurMZxkUJL4qw3uPaYqkvjkJ0v//glOhetnY3tXdpO2ntJ9f5B6rp1kR3obJAeJEjbGq5Lrp7rXIGFt6PvrlN3SepzV4c5YqsD7RIt55Snm96eb24l7enRbd0nJDPQbovZG0ZhvRyAAAAAGQBs4UCMry+WkXTCjHx1yd6g6rLExvpzhTdVZroNke6T54+hn7x8Tl077/Pz0koiBgJmgEgjsXsQB60pluPdAtsoqaji+58TdScRmqGmu4iRttY8Eh2gIyTLKbkK+Y5M4JT8le8t8fxN04DFrKnl6e3bXDC2mS5yxmeemeHaonHx/k5R/h3N9hvRJ3jfAptpFbqNd2V0buXs2EaR7t5KCdbxzS3amT83Mv16Ldu8AYAAAAAYCJZag0ERp8IJi3Srdd0S49udxTcEel2iW42TOOU/PFD00Z0YZAJcdBI92ETPCLdWWq6Oaqtm+XNtuq57dua6BYDuUjSy2NsGeau6xbn8SBR12zw5/4+K8X8aVeKudRzc4pxtn3V24Zlq5OOAzvFvL2Lbn92nfr9Y/Mm+7ZCk/HRo91hjdRKPdKti9t8S2pkLKS2ns9jeU3diM/L2wGRbgAAAACEAbOFEsXRMizRotsj0t3mLbrzpcJakAjaykcM0TbtbaO9rZ12TXeQlG1JMTdFunm/Jg0fFEk9d7aWYSaH5UJyqOVXwETZCu99B5vrum0TtZpgixdippZEodlgRVmXv7eHnnp7h+onf9FxUwI9d+7+6bru8H26kzcWudZ059LqTcd9XIjpJbO/9rtnn27UdAMAAAAgBJgtlCh6emO+9Y1RM1QTmHqPbkdNd1snNXVKenn+glRHItRBjdQ48sipu8zrm5vs/rwi3oMIdha9ugB3p5jrCxH51nRzpLuPe5xpKbBxRrrzbRdmMlN7dVOjWgAROAVYF6zZEDO+KNLeCxXp/vXj76p/T5kx1k5tzoYe6Q6WXq61DEvgAkQY9MXFvFuGuY4jqedm9NIJr5puPfoN0Q0AAACAbGC2UKLoIivfqE/U6ALT3Q7Mdi93GKlFHOkOmV7OzJyYruuWOulsLcOYI6x+3LMmDTNmHJxwyOgMkZpvpLu3r4z+8epW+thvl9qp8GH2NUpn/qgj3WMbaungMYNV3/Tn3t1l37/bMmwLGq1lE8MrTjmEzjtqMiVVdG9talf/fuZ9BwZ+LvsIyPke1kjNy8l9QNZ0u8ZCbzM3aXidyj4QTO7liHQDAAAAIAylPQsbwCS5pltEBTOmwVnTLYZiLLo7uszCPF/C9OnW67qXvLpV1XWH6YN84iGj6cbz5mSklgsfOXKSEuYHjkq3QcsVFlu8Tbx9l9/3qr2v/3rs/pEYtYWBRQpH9znSHqXolhTzd7Y3q37diw4fT62d3XT9Q2+pvx08JtjiBdfofvnkgymJ6OcHO/W7Xcn94Kgr13//32tbjJkVbhr6UXq5bhiZb0kNXxv43JFSEj29nEX0xOGD7D7dpuuAXs5R7AUvAAAAAJQepT0LG8BUJbimm7ft7NkTaFtTu5227a7pblG9ic3u5fkStk+37mD+GjuYp+bhWVuGibmVn+s0t5WaGlAoBnkvTuXe0tiuxNQnjtmfLlywf86Gc/l+xgeNHkyrtu6LfNGEzdT+8Ow6u677u/e/rkQ4i/tvnTGNSh1ddH/mfQeozzUM3zv7MLr6rBmBnifp9SwS6wvUPq0U08t57DjyL0Zq7kUxbjsnottUuqGbLEJ0AwAAACAbpT0LG8DoJl9Ji3Qzvzz/CM8Uad50K8BE9TUVkdek2326Axqp6Q7m3LOX3biZKBy5o+bGj82i+x9bSt88/wM0bHDxxbbO6TPH0Xu7WumoA9LmXlFwzIEj1fHNr33DQ2/Rf63YqI6ZX51/RORR9ThFNy+gcCQ/F4IK9ZGDa2jxv8xQaeZJPJ5zTS+vjUDoDrZENy9ITBjmzMjhXt1Pv+O9+KaPZXVF8q6/AAAAAEgWWKIvUaq0SWfSjNT84MivHukbVR+9iMolvZzF3NiGGlVLvLO5M3BNd7E5cr9hdOyYvkTU535l4SH0yvdOtRcsooLFkNTK//Kx1epfrs8+9sCR1B84cdoYld1x5aJpRakH/vT7DqBz5yWvtj0ssojGLbpM5ma51nVzNo779TjSLZhaB+rRb9R0AwAAACAbmC2UKLogTGKk2w+9/jhq5/Jc08sZt3gs9chgMSiUa7q4mDPHHzKavnDiVOovHLnfcHrxOwvpQ0dMintTSjK9XG8dlg9S436AwW9BL4sxteODkRoAAAAAwoDZQomiT/qSVtMdxt18ZAFEdy6RbmamVddtv06Re1+DNAunj1X/jmuopZ9/bLbKkAADG1lcjCqzR7JFDhiV2a5NdzPP1jIMNd0AAAAAyEb8OaogJ/ToSymll7t7Jxc00h0yIjbDFenW6+ZBcZk5cSj99QsLaNLwQaouGYAGqyxFWuflCx9bzOGThvlHutEyDAAAAAB5AtFdoiS5T3eYSHcharplbMKml0uvbr9aTlDcNGwAhFkTh9LlCw+hI/c3t+cLCzvhn3bYOOWW74azhyYMraXNje3GxTuObnPkndv31Ze4KzwAAAAACg9mCyWKnvKou/qWAsMGVRc0vXyE5T4e1umaezuzyZu0EapAejkAiYFLDC5bGF3vdY6Yn3DIaM+/f/OMabT03V00yxAJ5+vvrRfMpe6evpLLNAIAAABA8YHoLlE4LZtbDrGwLLV6V+nVXaj08m+ePo1OOnQMnTIjVRccpg0TR7ufXb1L3UZ6OQADlw/Omah+vHj/wd6CHQAAAABAB6K7ROHU6Se+fmJJpkAPsyLRDLdOihpeiDhzVm79j9nBHKIbAAAAAAAAEBUQ3SVMXYnWEuqR7kKkl+fDYZqDeSkuaAAAAAAAAACSRSJUxc0330xTpkyh2tpaOuaYY2jZsmW+j7/vvvto2rRp6vGHH344LVmypGjbCqKt6R6lRb2TgN6rGzXdAAAAAAAAgJIX3ffccw9dccUVdPXVV9OKFSto9uzZdNppp9H27duNj3/uuefo/PPPp8985jP00ksv0TnnnKN+XnvttaJvO8jPvby6vM/ulZsUDhxVT0dNGU7z9h9O9TBIAgAAAAAAAJS66L7hhhvokksuoYsuuohmzJhBt9xyC9XV1dHtt99ufPwvfvELOv300+nrX/86TZ8+na699lo68sgj6aabbir6toPcOHTcEDr+4JF0/Lg+ShpsSnff5xbQfZ+br4zVAAAAAAAAACAfYg0zdnZ20vLly+nKK6+07ysvL6eFCxfS0qVLjc/h+zkyrsOR8fvvv9/4+I6ODvUjNDU1qX+7urrUT77Ia0TxWgOJW86fRQ8//DDGLSQ43nID45Y7GLvijxvGGgAAAOhfxCq6d+7cST09PTR2rLO1E99etWqV8Tlbt241Pp7vN3HdddfRNddck3H/Qw89pCLqUcECEoQH45YbGLfcwLjlDsaueOPW2tpakG0BAAAAQDwkq6C2AHAUXY+Mc6R78uTJdOqpp1JDQ9qpOlc4IsGTqlNOOYWqqtKu3MAfjFtuYNxyA+OWOxi74o+bZGQBAAAAoH8Qq+geNWoUVVRU0LZt2xz38+1x48YZn8P3h3l8TU2N+nHDk6AoJ5BRv95AAeOWGxi33MC45Q7GrnjjhnEGAAAA+hexGqlVV1fT3Llz6dFHH7Xv6+3tVbfnz59vfA7frz+e4WiC1+MBAAAAAAAAAIABm17Oqd8XXnghzZs3j44++mi68cYbqaWlRbmZMxdccAFNnDhR1WYzl112GZ1wwgl0/fXX05lnnkl33303vfjii3TrrbfGvCcAAAAAAAAAAEDCRPd5551HO3bsoMWLFysztDlz5tADDzxgm6WtX79eOZoLCxYsoLvuuou+853v0Le//W06+OCDlXP5zJkzY9wLAAAAAAAAAAAggaKbufTSS9WPiSeeeCLjvnPPPVf9AAAAAAAAAAAASSbWmm4AAAAAAAAAAKA/A9ENAAAAAAAAAAAUCIhuAAAAAAAAAACgQEB0AwAAAAAAAAAABQKiGwAAAAAAAAAAKBAQ3QAAAAAAAAAAQIGA6AYAAAAAAAAAAAoERDcAAAAAAnHzzTfTlClTqLa2lo455hhatmyZ7+Pvu+8+mjZtmnr84YcfTkuWLCnatgIAAABJAaIbAAAAAFm555576IorrqCrr76aVqxYQbNnz6bTTjuNtm/fbnz8c889R+effz595jOfoZdeeonOOecc9fPaa68VfdsBAACAOIHoBgAAAEBWbrjhBrrkkkvooosuohkzZtAtt9xCdXV1dPvttxsf/4tf/IJOP/10+vrXv07Tp0+na6+9lo488ki66aabir7tAAAAQJxU0gCjr69P/dvU1BTJ63V1dVFra6t6vaqqqkhecyCAccsNjFtuYNxyB2NX/HGT7yf5vkoCnZ2dtHz5crryyivt+8rLy2nhwoW0dOlS43P4fo6M63Bk/P777/d8n46ODvUjNDY2qn/37t1Lvb29EexJ/4XHh4+d6upq9dkAYALHSbSUtZdRf6SMyqi3ulftH//XH9m7d28krxP0O3vAie59+/apfydPnhz3pgAAAAC+31dDhw6lJLBz507q6emhsWPHOu7n26tWrTI+Z+vWrcbH8/1eXHfddXTNNddk3L///vvnvO0AAADC0UM9tJyWU39m+I+GF/U7e8CJ7gkTJtCGDRtoyJAhVFZWFsnqBgt4fs2GhoZItnEggHHLDYxbbmDccgdjV/xx49Vy/vLm76uBBkfS9eg4R+V2795NI0eOjOQ7uz+DcxUEAccJCAKOk+i/swec6OZUmkmTJkX+unxA4qAMD8YtNzBuuYFxyx2MXXHHLSkRbmHUqFFUUVFB27Ztc9zPt8eNG2d8Dt8f5vFMTU2N+tEZNmxYXts+0MC5CoKA4wQEAcdJdN/ZKOYAAAAAgC9c/zl37lx69NFHHVFovj1//nzjc/h+/fHMww8/7Pl4AAAAoL8y4CLdAAAAAAgPp31feOGFNG/ePDr66KPpxhtvpJaWFuVmzlxwwQU0ceJEVZfNXHbZZXTCCSfQ9ddfT2eeeSbdfffd9OKLL9Ktt94a854AAAAAxQWiO084DY57lrrT4YA/GLfcwLjlBsYtdzB2udEfx+28886jHTt20OLFi5UZ2pw5c+iBBx6wzdLWr1/vcENesGAB3XXXXfSd73yHvv3tb9PBBx+snMtnzpwZ4170X/rjMQeiB8cJCAKOk+gp60tSTxIAAAAAAAAAAKAfgZpuAAAAAAAAAACgQEB0AwAAAAAAAAAABQKiGwAAAAAAAAAAKBAQ3QAAAAAAAAAAQIGA6M6Dm2++maZMmUK1tbV0zDHH0LJly+LepETBbWOOOuooGjJkCI0ZM4bOOecceuuttxyPaW9vpy9+8Ys0cuRIGjx4MH3kIx+hbdu2xbbNSeRHP/oRlZWV0Vf+f3t3Ai5j/f5x/NuvRJEtEkkRZZdsP9m3IlLITojKVrQgoVSULQkJLQhZs29RicienSRC2R2yr/X8r8/9+z9zzdE5RcXMnHm/rut0ziw0ZuY889zf7720bRu4juctfrt373YNGjSw5+aGG25wefLksTFFPvWOVPfl9OnT2+3ly5d3W7duddHst99+c126dHGZM2e25+Suu+5yb7zxhj1XPp435xYuXOgefvhhlyFDBvudVCfuYJfyHB0+fNjVr1/fJU+e3KVMmdI1bdrUnThx4ir/SwDgn1myZIk7e/ZsqB8GEDEIuv+mcePG2cxStdP/7rvvXL58+dyDDz7oDhw4EOqHFjYWLFhggeHSpUvdvHnz3Pnz590DDzxgc119zz33nJs+fbqbMGGC3X/Pnj2uevXqIX3c4WTFihVuyJAhLm/evLGu53mL25EjR1yxYsVcokSJ3OzZs92mTZtsRnCqVKkC9+nVq5fr37+/Gzx4sFu2bJlLmjSp/e5qISNa9ezZ073//vtu4MCBbvPmzXZZz9OAAQMC9+F5c3bs0rFeC65xuZTnSAH3xo0b7Zg4Y8YMC+Sfeuqpq/ivAK6s33///U8vI/Lps0GftToPOXfuXKgfDhKAc9HwPtLIMFy+woULe61atQpc/u2337wMGTJ4b731VkgfVzg7cOCAts28BQsW2OVff/3VS5QokTdhwoTAfTZv3mz3WbJkiRftjh8/7mXLls2bN2+eV6pUKa9NmzZ2Pc9b/Dp06OAVL1483tt///1379Zbb/V69+4duE7PZ+LEib0xY8Z40apy5creE088Eeu66tWre/Xr17efed7+SL9vkydPDly+lOdo06ZN9udWrFgRuM/s2bO9a665xtu9e/dV/hcAV9bEiRO906dPh/ph4App3LixlyJFCjsX4XXG5dq1a5c3Y8YM+3ns2LFep06dvDNnzngJGTvdf3M1ZtWqVZY66PvPf/5jl5Vug7gdPXrUvqdOndq+6znU7nfw85g9e3aXKVMmnkfnLEugcuXKsZ4f4XmL37Rp01zBggVdzZo1raQhf/787oMPPgjc/tNPP7l9+/bFeu5SpEhh5SHR/Nzdf//97ssvv3Q//PCDXV67dq1btGiRq1Spkl3meftrl/Ic6btSyvUe9en++vzQzjiQUKxfv9516NDBjivCbnfC4aeUDxs2zFWrVs21aNHCzZw5M6qynvDPnD592nXu3NnK2Dp27Ojq1q3rsmbN6hInTuwSMoLuv+HQoUNWA5kuXbpY1+uyTrrwR/rAVU2y0pFy585t1+m5uv766+0kNBjPo3Njx461sgXVxV+M5y1+27dvtzTpbNmyuc8//9xOBp599lk3YsQIu91/fvjdje2ll15yderUscUbpeZrsUK/r0qFFp63v3Ypz5G+azEo2HXXXWcLkTyPSEh0Aq2+GhMnTrTLWlhCwjiX8wOjTz75xJUoUcLFxMRYyZvKZajxxqW44YYb3Isvvmi9UVTO1qZNG9e4cWO7LbiXTELDURBXbdd2w4YNFkziz/388892ABo9erQ16cPlnRDcd9997s0337TAUbWyTz75pNXYIn7jx4+399unn35qiz1apOjTp09gsQIA4qIT5AsXLgR+Fm1K6KRai8ZffPFFYLcbkc9fPFHjTQXaWqTt27evNSxVU0jVeBN441JkzJjRjhP33nuvZcb4jUkViCfUzBiC7r8hTZo07tprr/1Dt2hdvvXWW0P2uMJV69atbQV0/vz59kvm03OlVP1ff/011v2j/XlU+rga8il41C6YvtQsTQ2a9LN2znje4qau0Tlz5ox1XY4cOdyuXbvsZ//54Xc3tnbt2gV2u3Xy1LBhQzuh8jMteN7+2qU8R/p+cbNNBSzqaM7ziEizY8cOO0HW55IsXrzYvuv8SDQFQVlHfumEgnFEnp07d8a6rGOYmrj27t3bPiuUFaX08ipVqtgit873lD4MXMwL2sVWg9tZs2a5Dz/80KZ5vPPOO4HA21/cOXXqlEtICLr/BqX2FihQINbqrVZldLlo0aIhfWzh9sulgHvy5Mnuq6++snFEwfQcapU0+HnUSDEFSNH8PJYrV85W/dasWRP4Ug2oUn39n3ne4qbyhYvH0qlO+Y477rCf9R5UcBP83B07dsxOCqP5udMH28Xpnzpx9lebed7+2qU8R/quxTItrPl0bNTzrNpvIFJoQa506dL2mSQamVqyZEmbUKJaTY3B03FXQZnGXurzyQ/GETk06nXkyJGxrtPiiYJqvz+P33Va99MY3U6dOrnPPvvMes8AwTHBNddcY1N5VJqg48LBgwdtg0mZEypH0eaSYgbp2rWrpZ4nqMW6UHdyi1TqtKeutMOHD7eOtE899ZSXMmVKb9++faF+aGGjRYsW1tny66+/9vbu3Rv4OnXqVOA+zZs39zJlyuR99dVX3sqVK72iRYvaF2IL7l4uPG9xW758uXfdddd53bt397Zu3eqNHj3au/HGG71Ro0YF7tOjRw/7XZ06daq3bt0675FHHvEyZ84c1d1XGzVq5N12223WSfSnn37yJk2a5KVJk8Zr37594D48b/+bKLB69Wr70sdn37597eedO3de8nNUsWJFL3/+/N6yZcu8RYsW2YSCunXrhvBfBVw+HSuqVq1qk1zWrl1r123ZssUmSOg6TXN5/fXXvZkzZ3q1atXyevbsGZj0gsih6Slnz561nw8dOhS4vmTJkl758uUDl8+fP++dO3fOq1atmpcqVSqvQoUKIXm8CP+JBrfccotXrlw5r0iRIl7q1Km9/v37222avlO7dm37zCxTpox3/fXXx5r0kRAQdP8DAwYMsMBHbwx9yCxdujTUDyms6KQ0rq9hw4YF7qOT0ZYtW9pBWsGRDtgKzPHnQTfPW/ymT5/u5c6d2xbFsmfP7g0dOjTW7Rrt1KVLFy9dunR2Hx38dbIYzY4dO2bvLx3PkiRJ4mXJksXGd/gnW8Lz5nnz58+P85imRYtLfY5iYmIsyE6WLJmXPHlyr0mTJhbMA5Hmiy++8KpUqeIVLFgwcHKskT/6PdDCZ506dbykSZPaSLzgRWHdjsjSr18/r169eoEFFr32OXPm9Bo2bBi4jxZU9JprhCmLK7jY6tWrbaymNivl5MmTdmwIHrW8YcMGb8iQIV7btm3tfZTQXKP/hHq3HQAAAJGTJirz5s1zAwYMcHv37nVDhgyxVFGfUszVmFFjG+fMmWMppJomgch6jWXUqFHWbbp69eruhRdesHIapQiraalq+gsXLmzNcvWab9y4MVCeRNd6+ObMmWNN9+bOneu+//579+CDD1o5ij/WVZOh1DMrIeO3AQAAAH/K7/EQHIxVqFDBppOon8HTTz8dqPFW0HbjjTdanfe7777ratas6VauXBmyx47L47/Ges1Um92gQQP33nvvWXdy1dmqRl8jnqZNm2avse6vvhQKvBVwqw6XgDt6qZeOgungTvbbt293R48etb4mFStWtC8t1IneR926dXMnT550CRm/EQAAAIhX8K6lZm9rl3PcuHF2WTtW2sFW4K3u1Qq8FYQp8NafU8MtnWCrySDz6MNb8KgmBUIauzl06FALomvUqGE7lbNnz7YdbjUtzZ49u43kHDZsmAXl2vXWRAaa5kW3zz//3Bbh1JncD7yrVatmTfduueUW2+FWwO0v7ixcuNBt3bo1wTff+9+cBwAAAOAiCp79gFvjBRWEZcqUyXazdHL98ccf2463KNW8efPm9r1QoUKBv2Pt2rW2863pLwj/hZWxY8falIVt27ZZpoImpjzxxBOWsSBKMVeAraBcc5aD+SPkEL2eeeYZC7a1GKf3ld4nqVKlcrVr13bDhw+3EWG6XscQLeDpGPLNN9+4lClTuoSM3wwAAADEyd+N0nzmpUuXukWLFlntpQLpunXr2vioMWPGWOCt+77yyiu2i6WgWyfWOvnWLpZ2xv0xUwi/+m0/4NbIt/fff992s/v06WOvpb60g63gSYG37lunTh2r7b446EZ00/tECy/qAaAMCc1x13GgTZs2rlmzZpZCPnr0aFu804g53V9ZMLly5XIJHY3UAAAAEC/N1P36669d2rRpLQDTrrXoOjXXUoq5Am+/DlgN1RSY+QGdTr5JOQ4/e/bscRkyZAhc3rFjhy2eqL5Wu5Jy+PBhKxvYtGmTe/75512TJk0sqJo/f77Vc/O6QotqquHWe0bHCGXC+LR4o4Wct99+2z333HO2CHfs2DF7/2TLls2lT5/eSlOiATvdAAAAiJMCZu1sa5c7Y8aMLnHixHa9AurSpUu7SZMm2e6n6jTVmbhgwYJ/SFcmMAs/LVu2dDfddJM1RvMlS5bMvit7QbQLqeyEESNGuNy5c1vZgL/jXaZMGbsPCyrRTQG0fv/XrVtnxwKljuu9ogUZXf/yyy9bYN20aVMrL1EDPgXmtWrVctGGRmoAAAAw+/fvt52rJUuWWKdhBVRKC1XK8Y8//mh13cFp5wq8NVLKHxPlo3t1ePN3tEWvs2gHO2nSpJbBIH4ncgXjBQoUsNd8woQJ7ttvvw38PQTc0U3vCS3C5MmTx6VIkcKa7Sk7QqUJd999t6tUqZJlxlStWtVKT7SAc+rUKReNOCICCNmBesqUKaF+GACA//fpp59aimixYsVc5cqVXY4cOVynTp1s3I/qt5VaPnDgwEDg7VN6ubpaK9AODrwRfvyqUnWTVoM0NbKqV6+eNbVSIysFTSoV0A6lPqf9xZQkSZK47t27Wwd6dSsHRNkSOjaoZnv37t1Wnz1jxgybxa1FHe1663ixb98+d+TIEStVURfzaER6ORCFlN6j1caL6cRpzpw5IXlMAIDQUSClmdu9evWyWkudTGsMlL5Wr15tXawbNWoU2NlSgB2cmuxjhzu8+RkKfvq/0oM1P/m1116zr7Jly9qoJ3UrX758uY142rlzp4uJibEGWNoFV92+X6+P6KPyA+1Wa5FGizLqTK7eDnpPdOjQwR06dMgyY9QLQNQLICYmxrIktKiX0LuUx4egG4hSmpt68Wq1X6sHAIgeqsfUrpSaIdWvXz9w/f3332+7mwq4tUPVv39/u10n2g0bNrSGSQrUERm0+6jgR2nAfrfo1q1bW0q5zgfU8Oqtt96y11bpwlpwUbdpdSjv16+f3V9jxO666y6C7ii1efNm60z+888/u5tvvtnGx2nDRoG0X6etLAlR4C1auEmfPr3tekczliOBKKUAWx0jg7+0Win6INXBUrU4N9xwg8uSJYubOHFirD+/fv16WxHX7TrwaufjxIkTse6j2Yv6YNf/SwdcfbgH02qoUtxU76OdlWnTpl2FfzkAIJgCKR3/dfLsO3/+vH1XernSj5V6rhNtBdxqkKR08qeffjqEjxqXQ2nkKhnQoknevHltMUUBuKgjubIY1M28Y8eOlmquQHvQoEH2uiv41o54+/btbWScAi0yGqKPxgRqIU7ni82bN7fjgd4TKj8RZcco8FbH8s8++8zmdAt1///DbwyAOHXp0sXVqFHDDrL6kNZMTq1wila+dXKmk7QVK1ZYytAXX3wRK6hW0K4dEAXjCtAVUGfNmjXW/0OpbDpAa5floYcesv+PGnAAAK4enTQr4ArOdlK9r5poSdeuXa0rsY7zoi7E+gxQ4y11s0Z400xkpfrqdZw6daqNblJmgz6bfeourcBbtbe635YtW+w9IAquFHirO/W8efNczpw5Q/ivQSjovVKqVCnrev/RRx/Zd40BU/bEwoULA/dT4K1zR5Wp6D2jXXH8P83pBhBdGjVq5F177bVe0qRJY311797dbtehoXnz5rH+TJEiRbwWLVrYz0OHDvVSpUrlnThxInD7zJkzvf/85z/evn377HKGDBm8Tp06xfsY9P/o3Llz4LL+Ll03e/bsf/3fCwCI34gRI7xrrrnG27hxo10+f/58rNv379/vpU2b1hs0aFCIHiH+rpEjR9prO23atMB1c+fO9RIlSuR169btD/fX53v27Nm9V155JXDdhQsXvB07dnh79+69ao8b4eO3337zChUqZOeN27dvD1zfvn17e2/17dvXmzJlihcTE+OdPn3abjt16pS997Zs2RLCRx5eqOkGopRmbPr1Nj7N4/QVLVo01m26vGbNGvtZO9758uWzOjCfut2qMYtWx5Werl2TcuXK/eljUIqbT3+XdlIOHDjwj/9tAIBLp3E+KvFRmvGCBQusU7XSy/2dTmU3qY5X90Fk8Xez9Zr6/Hnbuk0lAhofdscdd7hChQrZjvidd95p5WM+pQfrdkQnlRIoy0HngWqwp+xGNdtT+YEyFDVyTqnkGTNmtPurm7neUw0aNAj1Qw8rBN1AlFKQe3G6979Fdd6Xwj+h8ylYZ9wMAFxdSglVeZDSisuXL2/jHNOkSWO3HT9+3D3zzDOWSq7FWkQWdZhXp+lHH33UUsvVLE/z1mfOnOnOnDljHcpff/1162Cu11zzlRUwicoLqMeNXhrxpd476lauTZJly5a5++67zxqi6VxNddsPPPCA3Ve9ALRgpw73gwcPdg8//HCoH37YIegGEKelS5e6xx9/PNbl/Pnz28+a3Tp8+HDb/fB3uxcvXmyroffcc4+dwGmlXPMaOUkDgPDlB1ba8dSJtGoxM2fObIGXjunKPlLgreDMn9lME63Iop1tvc4KkNQES71YbrvtNrtNzUz12mssmJqtlSxZMvDnCLijlzIaNXtbfR7SpUvnBg4c6DJkyGAZjyVKlLBFuOCNG2VSqM+DvnS80HkgYuOoCUSps2fPWsOU4C+taPqUPqTu4+pi+uqrr9oJl98oTelEOsCq6cqGDRvc/PnzbSdEY0Z0cBbtmKjJhkbMbN261X333Xf2wQ8ACD211tCXAistkGr+so7x6kquBpgK0pS1pKBMQZoyk5SSTMAdWf7XQsVZ0KQZyupYrgapwbcpdVxNstQgS+8Hv4EeopPO61QyWLBgQRshp0Z8OufT778WbdQ4TSnlar7nN9gV/32TLFmyED768MVONxCl5syZY2O8gmmXWp0o/c7iY8eOtQ6Vut+YMWMCHUs14uvzzz+3VVDVgOmyulX27ds38HcpIFfq2jvvvGPdK5W29thjj13lfyUAIL6Zyrpu8uTJltWk7CUFXEoj1YKpTrC1mxV8Qh18GeH/evsLK/pZiyUKoBQs6bNYn++q5ZeL3xvscEcvZbY0btzYzuE0+sun95E/rUDnhKtWrXIFChSwWm6d5ynl3H/fML89bteom1o8twGIUv6JmGrAAACRKzgdXAuhqs/UuEfR+Celg6oGU7vbfxagI/zrb5XBpp3Ii197NcFSJoMyz0QZDUolV3137dq1Q/ioEW5USqhmaSNHjrSSwoszW/yFHF2/e/duK0WpWLGimzhxoo0SRPzIEQIAAEiAggPu7t272w6nMpqUZqw0cjVF0sm1H3ALAXfkGTdunKtcubK9nsWLF7eGVuo+7wfc2rXMlStX4P5KNVejK6UNA8FUSvjLL7/YLrbePxfvzer4oMU71XarL8CuXbus+R4B918j6AYAAEiA/IC7c+fO7t1333V16tSx3U11ru7SpYsF5erRgcilwFk7k2qS1qpVK+tU3qxZM+vTonpbXaegSM3SgqnLtDIdgGDKlFCgreBbx4e4FuGUGaNywnPnztn977777pA81khDcQ6AP6DqBAASBgVeM2bMcOPHj3elS5e2SRMaGaUT57Rp0zIWKoKp2amCao1u8uuz69Wr57Jnz27p4+3atbOmqNr9jm9Rhm700e3YsWM2iUbnfepOrmwJvSeUAXPvvfcGdrD9shMdL7Zv3249ftjdvjz8lgEAACQAOjFWo6Ngan6kk2gF3ArOVH/Zr18/a5akXVHN5N6zZ0/IHjP+Hr12yl7Qwkm5cuUCi+Ua16kSAlFQFF/A7SPgjl4bN250lSpVsveI3jPKiFCTNDXSfe+992x+u3oFiAJulSxomo1KU2iMe/nY6QYAAEgATpw4EZiPO2vWLGuEpIBbJ849e/a0rx49erjmzZvbfdatW2c7WhoZpV0uRA5NDVG9toImNcNTN/KMGTO6adOmWTBVvnz5UD9EhDGNjVOwrbFfOXLksGNBx44dbRGnRYsW1sVc3e6//fZbm92u44im22hE7Ny5c2PN6MaloXs5AABAhFN36gYNGljquOq1tYP9zTffWM3lCy+8YGN9Xn75ZdetWze7v7qY16pVy36eOnUqO54RQoHP4cOHLc23RIkSbseOHa5ChQq2w62afb3WfiYDqeOIy6ZNmyyNvH379rab7VNfAAXbK1assHptjYZV4H3o0CGXOnVqV7hwYde2bdtAJgUuDzvdAAAAES5p0qS2Y6Vda6WBaufKHx+lEVHqSOx3GVZqsubs7t27161evZra3gjx6aefWudxZSVol1JBt4JtNUSrUqWKe/LJJy2bQQG30IkecVE2hIJq7WCLSlJUhqJeADp26EvHk+rVq1tXfN2eOHFiez/R/+Hv4+gKAAAQ4QoVKuTy5MnjDh48aCnmSj8WJTRqlq7qfzt16uTmzJlj6ce6r8b+JEqUyE6qCbjD2/Dhwy2oVuO0t99+23YcfQq81SxPY8GmT58eqNEn6EZcXnrpJXsfqfme+jwo4NZ7Ru+xhx56KNaxQ8G2AnDdh2PEP0N6OQAAQARTqrECLKWTK+j+6KOP3NatW91XX33lMmXKFNjJ8u8bvFtF9/Lw991337maNWva6LcmTZoErvc7SvuUaq4UYTXDGjVqlLv99ttD9IgRCRR4q8u9FnHU60FNFgcNGhTnewv/HEsWAAAAEUbp4D4FzdqFKlWqlHUVfuWVV2z3s2zZsm737t2BgFtN0zS/2acTawLu8Kc6/ZQpU1rtdvDr7gdFwZ3L1eRKQfqbb74ZsseLyKAO5SpFUGPFu+66i4D7CmOnGwAAIIIE119rVvPy5cvtcpkyZWxHVJYtW2Y7o2q89f7771t6ubqba043aaKR5fnnn7c6XAXf8dFtqtvXaDgtrKgLNQsq8AUH0sGZL6LmezpGqAO+P+8d/z6OugAAABHED5o7dOhgc3PViVzq1atnJ89SpEgRSxv973//65599ln7MwsXLrTv7LdEljRp0rjjx4+7Xbt22eWLXz8twqikQOOdRA30FHCrdADRS83S/GODH3CrSZoCbpUi6PghOk6obKFhw4ZuwoQJIX3MCRlBNwAAQIRRLeb48eOtEdKIESNcuXLlLMhSnabG/EjevHntJFq13bNnzw40TSN1NLxpcUSBkL7Uab5atWqWpdC7d2+7Xa+fAm8/+D569KjbvHnzH2ats9MdvZThUrduXetQrjnuS5cudWfPnrVjwM6dO63z/a+//hp4DynV/NFHH7UGfXqv4d9H0A0AABBBdPKsbsNKC9XsXHWubtq0qaWQd+/e3bqU+/WZfq2vPxYsOK0U4bmYoi7lShX3u9Bny5bNdiWVxfDcc8/Z66/AW1+aq6wdyiNHjth3YO3ata5o0aIuSZIkNtXg559/dg0aNLDd7ZMnT1qzPY0CGzx4cKwFOC3eaZRgsmTJQvr4EypqugEAAMJYXI2NFHQrdVRzt9V1uFmzZhaQaUdLDdWURjps2DDXqFGjkD1uXB41unv66aftu+Zua1yTKDjSax0TE+P69evnsmbNaostuk5d6vVa63XXLibd6KPb+vXrXbFixVybNm3cG2+8Ydft37/fZnBr51vvH2XH1KlTh4yXq4ydbgAAgDCl3Wn/5Fi7mX4QrlRidRzetm2bBVk6oRbN1FWgPWXKFFe/fv2QPnZcOqWHK338nXfecTVq1AgE3GqM17JlSxsHp9d71qxZLmPGjNah/MyZM+6RRx6xpnl+6QABd/TS69+uXTtLD9d30SJMunTpXMGCBe1YokU6HSsIuK8+gm4AAIAwpODab5qmEVCq7VWHcs1gVj2m6PYNGza4L7/80mo1O3bsaLc9/PDDlkquE3GEP6UAq1mashT8sWCqz1+zZo2bPn263TZp0iR7T6icYMmSJVavr9dbr7OCK0oHopte/4EDB1omhNLHdTzQIoyyYrRoo8AboUN6OQAAQBinlKuWV3Xamr89c+ZMC6o1Gkp1vups/eKLL7q+ffu6LFmyWB2wRohp55N5u5FDtfja5T506FDgur1791owrZ3tTZs2uaeeesouq4He7bffHtLHi/Bx+PBhy4JRQ7377rvP/fTTT1a3rV4Oel8pW0KLcArIETrsdAMAAIQZP1hesWKFBVxqsKXOwvPmzbNdLO1c9ejRw060+/TpYynGQ4cOdStXrqRLeQTS7qRqtPX6+tKnT28Bt3a+c+bMaTOUU6dO7W6++eaQPlaEV1mCRgVq4U3HCDXZy5w5s72PlD2hkYHKjvEDbj+LAlcfQTcAAEAYUDdydR72KY1YHalVn60dbF/Xrl1dpUqVLPDWTpY6WKtLcdmyZQPzmUk1jix6/fSaDRkyxNKCg6mEQOnler3vuece62gOqKxETdPy589vWTBqkub3AtAu95w5c1y+fPms2Z6yJsQvV8HVxzMPAAAQBrOZNZM5V65cgesqVKhg87c15keBt273vfrqq9bheuLEiW7cuHGx/i6aaUUelQaoS7kWWl5++WWr5fYpCFdzNe1c9urVy66jOjS6aaHt8ccft6aJb731lgXeF+9kK/CePHmy3VfN0/T+QehQ0w0AABAGNPpJqeFjxoyxjsPaudZ1Gv+jOm2dOLdo0SLWTufHH39sJ94E2pFPGQoa86Zu5Xr9c+fObWUC2uUW7XQzFgyiRnrNmze398u99977hx3s4H4OWrRRUF6kSBFb1OG9ExoE3QAAACGigPrcuXPWLM2v0WzSpIlLnjy5pYwWL17cbm/durXtftauXfsPgbcQiCUcep0//PBD98MPP7hMmTJZcyzN79brqyCc0gH079/fde7c2RqoXfx77wfcypDR3G7Vde/atcuOI+odgNAgvRwAACAE1AQtSZIktoOpE2jJkSOHa9++ve1oql5bt2m2rhohabdK6eRKMVbDpGAE3AmHdi71es+dO9eCb+18U6uPYGqop93t77//3oLs4D1Uf4dbWTAjR46027R4Q8AdWgTdAAAAIZAiRQrrOqy0cY0C0wgwqV69unvyySftZFnzuf3Ae8CAATYqavfu3XYZCVdciagsrESvX375xY0ePdp98MEHLiYmxua5axHmvffesyBbX8H13PpZUw90vGCKQXhguQwAAOAq89PB06ZN6woWLGhNjtRIK1myZK5Lly7u0UcftfvpOjVK0omzUs114q0/p8vM4U64eF3h27hxo6tfv77LkyePy5AhQ2BsXMeOHa0ERdkyffv2DdR1K4389ddft0wJfSE8UNMNAAAQItrpVlOkW2+91ZqlqXGaaroVaIu6lmt36+DBg/ZdI4D8nSzG/wAJP+AuUaKEa9WqlWvXrp31ehDN4d6yZYtlvWhUWOHChV358uUt4FZfiAULFljA7Xc1R+gRdAMAAISAxvk88cQTbtasWXbSvH//fjuBnjZtmnvsscdct27d7H7qZr5ixQrXp08fAm0gShw+fNhVq1bN5c2b10pLfD169LCxcpUrV3ZVq1Z16dOnd2+88YaNBkuVKpV1KVeDxuzZs4f08SM20ssBAACugot3p/fu3WvpooUKFbKUcf387LPPWs2mGmmpQ7lOrlXzra+4/g4ACZMW4bST3bVr18DvvcpNlFKu7uVanJs+fbplyyxbtsxGy6nXg5rtUf8ffjhqAwAAXAV+sKyA+ssvv7QmRzqZ3rBhQ+A+GTNmdE2bNrV6bXUv133j+jsAJGyrVq1yO3bscKVLlw783lepUsWOHRohqDruU6dOueeff96OITfddJNLnDgxAXeY4sgNAABwBQV3FR40aJA1OVINd5YsWayGW2Oh1EjNpxPnsmXLuiFDhthMbgDR584777Rda5WhiBbitCinGm8dU3Lnzu1q165tAXmaNGlC/XDxF0gvBwAAuIL8XSrtXO3Zs8d2qHLlymXX6ecGDRq406dPu3LlyrmcOXO6Tp06uXTp0lnHYnWx9judA4iuoFtjBUeMGOEKFCjg7rjjjj8cU9RMTfdLmjRpCB8pLgWN1AAAAK4gnWqtXr3aRoOJdrA1h9s3e/Zs9/bbbwdSRNUMafHixS5RokSMBQOi2Geffebq1atnO9ovvfSSLcrJsWPHrNGismS++eabwCIewhdBNwAAwFUwatQo9/jjj9vOdu/evW03O7hTsRohHTlyxLoVayfrwoULll4KIDopy0WBtWq4s2bN6u6//35bjFODtZUrV9rkA8aCRQaCbgAAgH9RcIfxi3eqhw4d6po3b24diNu2betSpkz5l38HgOim7uS9evVy27Zts2yY4sWLW8NFBeKIDATdAAAA/5LgYPmDDz5w69atc+fOnbPZuQ0bNrRdKjVT087Vq6++avN04wu8AcBHb4fIRtANAADwL2vfvr376KOPXM2aNd3atWtttI/mcE+dOtVm6WrergJvBd2vvfaaS5YsWagfMoAwFpw1Q6+HyEOhEAAAwL9oyZIlbvz48W7KlCk23kcnyJMmTXI9e/Z0derUsduUYn727Fn7mc7DAP5KcJBNwB15KBYCAAD4Fx04cMBGgGXLli1wgly5cmULtLdv3+42b95s12uXe9GiRXY7iYcAkHARdAMAAPxL9dySMWNGq9PWmDBfkiRJXKVKldwPP/zgNm7cGLjeD7jZuQKAhIugGwAA4B8E2T6/gdqdd95pQffAgQNt9rZPTZCyZ8/+h8ZpBNwAkLDRSA0AAOAfdilfv369i4mJcTVq1HDVq1d3O3bscKVKlbIU87Jly7rcuXNbEH7w4EGbr0sXYgCIHux0AwAAXCY/4G7Xrp3r1KmT279/vzt+/Lh77LHH3DPPPOPSp0/vFixY4NKlS+dGjRpl48ESJ07sli9fbgG3xv8AAKID3csBAAD+BgXVo0ePdjNnznSFChWy69SNvEWLFtaRvEePHu7DDz90Fy5ccCdPnrQAXKnkunzddZyCAUC04IgPAABwCTRvW2njadKkccWKFXNnzpxxN954ozVO0861dr9r1apl1zdr1szVrVvX5cuXz/7sTTfdFEhLJ+AGgOhCejkAAMBf0I5248aN3ccff2w726I08Z07d1ott34+d+6cXV+1alWXIUMG9+OPP8ablg4AiB4stQIAAPyJTz75xGZsK+CuWLFioPt4mTJlbP52gwYN3KRJk1yWLJt6G7gAAAISSURBVFnsegXf119/vY0JAwCA7uUAAADx0Ezt2rVru7Zt21rKuM+fra267p49e7rvv//ede/e3a4bOXKk27dvX6BpGgAgurHTDQAAEI/du3e7U6dOuZIlSwYCbfG/ayxYqlSp3ODBg13r1q1dpkyZ3G233eaWLl0a6FJO4A0A0Y2gGwAAIB6rVq2yUWB33323XQ4OvP1Z3YkSJXKtWrVyffr0cefPn3fJkyenSzkAIIBuHgAAAPHImjWrjfuaO3euXfYD7uCmaMOHD3fvvvuuzeFOkSKF3Ycu5QAAH0E3AABAPAoUKGBN0YYOHep27doVuN5viXPs2DG3bds2lydPnlhp5HQpBwD4+EQAAACIhzqSq157xowZrmPHjm716tV2vXaz9+zZ4+rUqWNN01q0aBHqhwoACFN0LwcAAPgTaoY2bNgw17JlS5cuXTqXO3duSx8/evSofV+8eLHVddM0DQAQF4JuAACAS7BmzRqb1b1lyxZ3++23u/z589v8bgXaNE0DAMSHoBsAAOAfYIcbAPBnCLoBAAAuUfDIMAAALgWN1AAAAC4RATcA4HIRdAMAAAAAcIUQdAMAAAAAcIUQdAMAAAAAcIUQdAMAAAAAcIUQdAMAAAAAcIUQdAMAAAAAcIUQdAMAAAAAcIUQdAMAAAAAcIUQdAMAAAAAcIUQdAMAAAAAcIUQdAMAAAAA4K6M/wMil1deUFJUcwAAAABJRU5ErkJggg==", "text/plain": ["<Figure size 1000x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Method Comparison Summary:\n", "            method          rmse  median_distance  good_points_pct  \\\n", "0  Coordinate-Only  3.089200e+01     1.038600e+01             1.50   \n", "1        GCP-Rigid  2.976900e+01     1.051800e+01             1.38   \n", "2       GCP-PCRNet  1.324314e+11     1.324310e+11             0.00   \n", "\n", "   centroid_error  \n", "0    0.000000e+00  \n", "1    0.000000e+00  \n", "2    1.324315e+11  \n"]}], "source": ["# Plot training loss\n", "if enable_visualization:\n", "    plt.figure(figsize=(10, 6))\n", "    \n", "    plt.subplot(1, 2, 1)\n", "    plt.plot(training_losses)\n", "    plt.title('PCRNet Training Loss')\n", "    plt.xlabel('Epoch')\n", "    plt.ylabel('Loss')\n", "    plt.grid(True)\n", "    \n", "    # Compare alignment methods\n", "    plt.subplot(1, 2, 2)\n", "    methods = ['Coordinate-Only', 'GCP-Rigid', 'GCP-PCRNet']\n", "    rmse_values = [coord_results['rmse'], gcp_results['rmse'], pcrnet_results['rmse']]\n", "    \n", "    plt.bar(methods, rmse_values, color=['blue', 'orange', 'green'])\n", "    plt.title('Alignment Method Comparison')\n", "    plt.ylabel('RMSE (m)')\n", "    plt.xticks(rotation=45)\n", "    plt.grid(True, alpha=0.3)\n", "    \n", "    plt.tight_layout()\n", "    plt.savefig(output_path / f\"{site_name}_gcp_pcrnet_analysis.png\", dpi=300, bbox_inches='tight')\n", "    plt.show()\n", "\n", "# Summary comparison\n", "print(\"\\nMethod Comparison Summary:\")\n", "comparison_df = pd.DataFrame([\n", "    coord_results,\n", "    gcp_results,\n", "    pcrnet_results\n", "])\n", "print(comparison_df[['method', 'rmse', 'median_distance', 'good_points_pct', 'centroid_error']].round(3))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. Save Results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Results saved to: ../../../data/output_runs/gcp_pcrnet_alignment/ransac_pmf\n", "Best performing method: GCP-Rigid\n", "\n", "GCP-Anchored PCRNet Alignment Complete\n", "Final PCRNet RMSE: 132431437775.84m\n", "Training converged with loss: 81079420984258627371008.000000\n"]}], "source": ["if save_results:\n", "    # Save aligned point cloud\n", "    aligned_pcd = o3d.geometry.PointCloud()\n", "    aligned_pcd.points = o3d.utility.Vector3dVector(transformed_drone)\n", "    o3d.io.write_point_cloud(str(output_path / f\"{site_name}_gcp_pcrnet_aligned.ply\"), aligned_pcd)\n", "    \n", "    # Save comprehensive results\n", "    results_data = {\n", "        'experiment_info': {\n", "            'site_name': site_name,\n", "            'ground_method': ground_method,\n", "            'timestamp': datetime.now().isoformat(),\n", "            'gcp_search_radius': gcp_search_radius,\n", "            'pcrnet_epochs': pcrnet_epochs,\n", "            'learning_rate': learning_rate\n", "        },\n", "        'gcp_info': {\n", "            'num_gcps_loaded': len(ifc_gcp_coords),\n", "            'num_correspondences': len(drone_gcps),\n", "            'gcp_locations': gcp_df['Location'].tolist(),\n", "            'gcp_point_ids': gcp_df['Point_ID'].tolist(),\n", "            'drone_gcp_coords': drone_gcps.tolist(),\n", "            'ifc_gcp_coords': ifc_gcps.tolist()\n", "        },\n", "        'learned_transformation': learned_transform.tolist(),\n", "        'training_losses': training_losses,\n", "        'final_loss': training_losses[-1],\n", "        'alignment_results': {\n", "            'coordinate_only': coord_results,\n", "            'gcp_rigid': gcp_results,\n", "            'gcp_pcrnet': pcrnet_results\n", "        },\n", "        'best_method': min([coord_results, gcp_results, pcrnet_results], key=lambda x: x['rmse'])['method']\n", "    }\n", "    \n", "    with open(output_path / f\"{site_name}_gcp_pcrnet_results.json\", 'w') as f:\n", "        json.dump(results_data, f, indent=2)\n", "    \n", "    print(f\"\\nResults saved to: {output_path}\")\n", "    print(f\"Best performing method: {results_data['best_method']}\")\n", "\n", "print(\"\\nGCP-Anchored PCRNet Alignment Complete\")\n", "print(f\"Final PCRNet RMSE: {pcrnet_results['rmse']:.2f}m\")\n", "print(f\"Training converged with loss: {training_losses[-1]:.6f}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}