#!/usr/bin/env python3
"""
Test script to verify refactored alignment notebooks work with shared config
"""

import sys
from pathlib import Path

# Add notebooks directory to path
notebooks_dir = Path(__file__).parent.parent.parent
sys.path.append(str(notebooks_dir))

# Test shared config import
try:
    from shared.config import get_data_path, get_output_path, get_mlflow_tracking_uri, find_latest_file, get_processed_data_path
    print("✅ Shared config import successful")
except ImportError as e:
    print(f"❌ Shared config import failed: {e}")
    sys.exit(1)

# Test path generation
site_name = "trino_enel"
ground_method = "ransac_pmf"

print(f"\n🧪 Testing path generation for site: {site_name}, method: {ground_method}")

# Test basic path functions
try:
    data_path = get_data_path(site_name, "raw")
    print(f"✅ Raw data path: {data_path}")
    
    processed_path = get_processed_data_path(site_name, "ground_segmentation")
    print(f"✅ Processed data path: {processed_path}")
    
    output_path = get_output_path("alignment", site_name)
    print(f"✅ Output path: {output_path}")
    
    mlflow_uri = get_mlflow_tracking_uri()
    print(f"✅ MLflow URI: {mlflow_uri}")
    
except Exception as e:
    print(f"❌ Path generation failed: {e}")
    sys.exit(1)

# Test specific alignment paths
print(f"\n📁 Testing alignment-specific paths:")

try:
    # Ground segmentation path
    ground_seg_path = get_processed_data_path(site_name, "ground_segmentation") / ground_method
    print(f"✅ Ground segmentation path: {ground_seg_path}")
    
    # IFC metadata path
    ifc_metadata_path = get_processed_data_path(site_name, "ifc_metadata")
    print(f"✅ IFC metadata path: {ifc_metadata_path}")
    
    # IFC point clouds path
    ifc_pointcloud_path = get_processed_data_path(site_name, "ifc_pointclouds")
    print(f"✅ IFC point cloud path: {ifc_pointcloud_path}")
    
    # Alignment output path
    alignment_output_path = get_processed_data_path(site_name, "coordinate_alignment")
    print(f"✅ Alignment output path: {alignment_output_path}")
    
except Exception as e:
    print(f"❌ Alignment path generation failed: {e}")
    sys.exit(1)

# Test file discovery (if files exist)
print(f"\n🔍 Testing file discovery:")

try:
    # Check if ground segmentation directory exists
    if ground_seg_path.exists():
        print(f"✅ Ground segmentation directory exists: {ground_seg_path}")
        
        # Try to find nonground file
        try:
            nonground_file = find_latest_file(ground_seg_path, f"{site_name}_nonground.ply")
            print(f"✅ Found nonground file: {nonground_file}")
        except FileNotFoundError:
            print(f"⚠️  Nonground file not found in {ground_seg_path}")
    else:
        print(f"⚠️  Ground segmentation directory does not exist: {ground_seg_path}")
    
    # Check if IFC metadata directory exists
    if ifc_metadata_path.exists():
        print(f"✅ IFC metadata directory exists: {ifc_metadata_path}")
        
        # Try to find metadata file
        try:
            metadata_file = find_latest_file(ifc_metadata_path, "*enhanced_metadata.csv")
            print(f"✅ Found metadata file: {metadata_file}")
        except FileNotFoundError:
            print(f"⚠️  Enhanced metadata file not found in {ifc_metadata_path}")
    else:
        print(f"⚠️  IFC metadata directory does not exist: {ifc_metadata_path}")
        
except Exception as e:
    print(f"❌ File discovery test failed: {e}")

# Test output directory creation
print(f"\n📝 Testing output directory creation:")

try:
    test_output_path = alignment_output_path / ground_method / "test"
    test_output_path.mkdir(parents=True, exist_ok=True)
    print(f"✅ Created test output directory: {test_output_path}")
    
    # Clean up test directory
    test_output_path.rmdir()
    print(f"✅ Cleaned up test directory")
    
except Exception as e:
    print(f"❌ Output directory creation failed: {e}")

print(f"\n🎯 Path refactoring test complete!")
print(f"📋 Summary:")
print(f"  - Shared config import: ✅")
print(f"  - Path generation: ✅") 
print(f"  - Alignment paths: ✅")
print(f"  - File discovery: ⚠️  (depends on data availability)")
print(f"  - Directory creation: ✅")

print(f"\n🚀 Ready to test refactored alignment notebooks!")
