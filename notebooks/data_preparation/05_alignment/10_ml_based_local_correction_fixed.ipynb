# Parameters (Papermill)
site_name = "trino_enel"
ground_method = "ransac_pmf"
ml_method = "ensemble"  # Options: neural_network, gaussian_process, random_forest, ensemble
feature_engineering = True
uncertainty_quantification = True
save_results = True

# Imports
import sys
from pathlib import Path
import numpy as np
import pandas as pd
import open3d as o3d
import matplotlib.pyplot as plt
from scipy.spatial import cKDTree
import json
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# ML/AI Libraries
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
from sklearn.ensemble import RandomForestRegressor
from sklearn.gaussian_process import GaussianProcessRegressor
from sklearn.gaussian_process.kernels import RBF, WhiteKernel, Matern
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.preprocessing import StandardScaler, PolynomialFeatures
from sklearn.metrics import mean_squared_error, r2_score
import joblib

# Add project root to path
project_root = Path().resolve().parents[2]
sys.path.append(str(project_root))

import sys
from pathlib import Path

# Add the `notebooks` folder to sys.path
notebooks_root = Path(__file__).resolve().parents[1] if '__file__' in globals() else Path.cwd().parents[1]

if str(notebooks_root) not in sys.path:
    sys.path.insert(0, str(notebooks_root))

# Now import from shared package
from shared.config import get_data_path, get_output_path, get_mlflow_tracking_uri, find_latest_file, get_processed_data_path
# Setup paths
output_dir = get_processed_data_path(site_name, "ml_local_alignment")
output_dir.mkdir(parents=True, exist_ok=True)

print("=== AI/ML-BASED LOCAL NON-UNIFORM CORRECTION ===")
print(f"Site: {site_name}")
print(f"ML Method: {ml_method}")
print(f"Feature Engineering: {feature_engineering}")
print(f"Output directory: {output_dir}")
print(f"Device: {'CUDA' if torch.cuda.is_available() else 'CPU'}")

# Load drone point cloud
drone_file = f"../../../data/processed/{site_name}/ground_segmentation/{ground_method}/{site_name}_nonground.ply"
drone_pcd = o3d.io.read_point_cloud(drone_file)
drone_points = np.asarray(drone_pcd.points)

# Load IFC metadata for ground truth
ifc_metadata_file = f"../../../data/processed/{site_name}/ifc_metadata/GRE.EEC.S.00.IT.P.14353.00.265_enhanced_metadata.csv"
ifc_df = pd.read_csv(ifc_metadata_file)

print(f"IFC metadata columns: {list(ifc_df.columns)}")
print(f"IFC metadata shape: {ifc_df.shape}")

# Extract pile coordinates as ground truth (using correct column names)
pile_mask = ifc_df['Name'].str.contains('Pile', case=False, na=False)
pile_coords = ifc_df[pile_mask][['X', 'Y', 'Z']].dropna().values

print(f"Loaded drone points: {len(drone_points):,}")
print(f"Loaded pile coordinates: {len(pile_coords):,}")
print(f"Pile coordinate ranges:")
print(f"  X: [{pile_coords[:, 0].min():.1f}, {pile_coords[:, 0].max():.1f}]")
print(f"  Y: [{pile_coords[:, 1].min():.1f}, {pile_coords[:, 1].max():.1f}]")
print(f"  Z: [{pile_coords[:, 2].min():.1f}, {pile_coords[:, 2].max():.1f}]")

# Create training data by finding correspondences
def create_training_data(drone_pts, pile_pts, max_distance=5.0):
    """Create training dataset from drone-pile correspondences"""
    
    drone_tree = cKDTree(drone_pts[:, :2])
    
    training_features = []
    training_targets = []
    
    for pile_pt in pile_pts:
        distances, indices = drone_tree.query(pile_pt[:2], k=1, distance_upper_bound=max_distance)
        
        if distances < np.inf:
            drone_pt = drone_pts[indices]
            
            # Features: drone coordinates
            features = drone_pt
            
            # Target: Z correction needed
            z_correction = pile_pt[2] - drone_pt[2]
            
            training_features.append(features)
            training_targets.append(z_correction)
    
    return np.array(training_features), np.array(training_targets)

# Create training dataset
X_train_raw, y_train = create_training_data(drone_points, pile_coords)
print(f"Training samples: {len(X_train_raw)}")
print(f"Z correction range: [{y_train.min():.2f}, {y_train.max():.2f}]m")
print(f"Mean Z correction: {y_train.mean():.2f}m")
print(f"Std Z correction: {y_train.std():.2f}m")

def engineer_features(points, include_spatial=True, include_geometric=True, include_contextual=True):
    """Engineer features for ML-based correction prediction"""
    
    features = []
    feature_names = []
    
    # Basic coordinates
    features.extend([points[:, 0], points[:, 1], points[:, 2]])
    feature_names.extend(['x', 'y', 'z'])
    
    if include_spatial:
        # Spatial features
        centroid = np.mean(points, axis=0)
        
        # Distance from centroid
        dist_from_center = np.linalg.norm(points - centroid, axis=1)
        features.append(dist_from_center)
        feature_names.append('dist_from_center')
        
        # Normalized coordinates
        points_norm = (points - centroid) / np.std(points, axis=0)
        features.extend([points_norm[:, 0], points_norm[:, 1]])
        feature_names.extend(['x_norm', 'y_norm'])
        
        # Polar coordinates
        angles = np.arctan2(points[:, 1] - centroid[1], points[:, 0] - centroid[0])
        features.extend([np.cos(angles), np.sin(angles)])
        feature_names.extend(['cos_angle', 'sin_angle'])
    
    if include_geometric:
        # Geometric features
        # Polynomial features
        features.extend([points[:, 0]**2, points[:, 1]**2, points[:, 0]*points[:, 1]])
        feature_names.extend(['x_squared', 'y_squared', 'xy_product'])
        
        # Radial basis functions
        rbf_centers = np.percentile(points[:, :2], [25, 50, 75], axis=0)
        for i, center in enumerate(rbf_centers):
            rbf_dist = np.linalg.norm(points[:, :2] - center, axis=1)
            features.append(np.exp(-rbf_dist**2 / (2 * 100**2)))  # RBF with 100m bandwidth
            feature_names.append(f'rbf_{i}')
    
    if include_contextual:
        # Contextual features (local neighborhood)
        tree = cKDTree(points[:, :2])
        
        # Local density
        neighbor_counts = tree.query_ball_point(points[:, :2], r=50.0, return_length=True)
        features.append(neighbor_counts)
        feature_names.append('local_density')
        
        # Local Z statistics
        local_z_std = []
        for i, pt in enumerate(points):
            neighbors = tree.query_ball_point(pt[:2], r=25.0)
            if len(neighbors) > 1:
                local_z_std.append(np.std(points[neighbors, 2]))
            else:
                local_z_std.append(0.0)
        features.append(local_z_std)
        feature_names.append('local_z_std')
    
    return np.column_stack(features), feature_names

if feature_engineering:
    # Engineer features for training data
    X_train_engineered, feature_names = engineer_features(X_train_raw)
    
    # Engineer features for all drone points
    X_all_engineered, _ = engineer_features(drone_points)
    
    print(f"Engineered features: {len(feature_names)}")
    print(f"Feature names: {feature_names}")
    
    X_train = X_train_engineered
    X_all = X_all_engineered
else:
    X_train = X_train_raw
    X_all = drone_points
    feature_names = ['x', 'y', 'z']

# Normalize features
scaler = StandardScaler()
X_train_scaled = scaler.fit_transform(X_train)
X_all_scaled = scaler.transform(X_all)

print(f"Training data shape: {X_train_scaled.shape}")
print(f"All data shape: {X_all_scaled.shape}")

# Quick Random Forest implementation for immediate results
print("Training Random Forest for immediate results...")

rf_model = RandomForestRegressor(
    n_estimators=100,
    max_depth=10,
    min_samples_split=5,
    min_samples_leaf=2,
    random_state=42,
    n_jobs=-1
)

# Split data for validation
X_train_split, X_val, y_train_split, y_val = train_test_split(
    X_train_scaled, y_train, test_size=0.2, random_state=42
)

# Train model
rf_model.fit(X_train_split, y_train_split)

# Validate
val_predictions = rf_model.predict(X_val)
val_rmse = np.sqrt(mean_squared_error(y_val, val_predictions))
val_r2 = r2_score(y_val, val_predictions)

print(f"Validation RMSE: {val_rmse:.3f}m")
print(f"Validation R²: {val_r2:.3f}")

# Feature importance
if feature_engineering:
    feature_importance = pd.DataFrame({
        'feature': feature_names,
        'importance': rf_model.feature_importances_
    }).sort_values('importance', ascending=False)
    
    print("\nTop 10 Most Important Features:")
    print(feature_importance.head(10))

# Make predictions for all points
rf_predictions = rf_model.predict(X_all_scaled)
print(f"\nRF predictions range: [{rf_predictions.min():.2f}, {rf_predictions.max():.2f}]m")
print(f"RF predictions mean: {rf_predictions.mean():.2f}m")

# Apply ML corrections
drone_ml_corrected = drone_points.copy()
drone_ml_corrected[:, 2] += rf_predictions

print(f"Applied ML corrections to {len(drone_points):,} points")
print(f"Original Z range: [{drone_points[:, 2].min():.2f}, {drone_points[:, 2].max():.2f}]m")
print(f"ML corrected Z range: [{drone_ml_corrected[:, 2].min():.2f}, {drone_ml_corrected[:, 2].max():.2f}]m")

# Validation function
def validate_ml_corrections(drone_corrected, pile_coords, max_distance=2.0):
    """Validate ML correction quality"""
    
    drone_tree = cKDTree(drone_corrected[:, :2])
    validation_errors = []
    
    for pile_pt in pile_coords:
        distances, indices = drone_tree.query(pile_pt[:2], k=1, distance_upper_bound=max_distance)
        
        if distances < np.inf:
            drone_z = drone_corrected[indices, 2]
            pile_z = pile_pt[2]
            error = abs(drone_z - pile_z)
            validation_errors.append(error)
    
    if len(validation_errors) > 0:
        errors = np.array(validation_errors)
        return {
            'rmse': np.sqrt(np.mean(errors**2)),
            'mean_error': np.mean(errors),
            'median_error': np.median(errors),
            'max_error': np.max(errors),
            'std_error': np.std(errors),
            'num_validated': len(errors),
            'sub_meter_pct': np.sum(errors < 1.0) / len(errors) * 100,
            'sub_half_meter_pct': np.sum(errors < 0.5) / len(errors) * 100
        }
    return None

# Validate ML corrections
ml_validation = validate_ml_corrections(drone_ml_corrected, pile_coords)

if ml_validation:
    print("\n=== ML CORRECTION VALIDATION ===")
    print(f"Method: Random Forest")
    print(f"Validated points: {ml_validation['num_validated']}")
    print(f"RMSE: {ml_validation['rmse']:.3f}m")
    print(f"Mean error: {ml_validation['mean_error']:.3f}m")
    print(f"Median error: {ml_validation['median_error']:.3f}m")
    print(f"Max error: {ml_validation['max_error']:.3f}m")
    print(f"Sub-meter accuracy: {ml_validation['sub_meter_pct']:.1f}%")
    print(f"Sub-half-meter accuracy: {ml_validation['sub_half_meter_pct']:.1f}%")
    
    # Compare with global correction
    global_z_offset = 154.69
    drone_global = drone_points.copy()
    drone_global[:, 2] += global_z_offset
    global_validation = validate_ml_corrections(drone_global, pile_coords)
    
    if global_validation:
        print("\n=== COMPARISON WITH GLOBAL CORRECTION ===")
        print(f"Global RMSE: {global_validation['rmse']:.3f}m")
        print(f"ML RMSE: {ml_validation['rmse']:.3f}m")
        improvement = (global_validation['rmse'] - ml_validation['rmse']) / global_validation['rmse'] * 100
        print(f"Improvement: {improvement:.1f}% reduction in RMSE")
        
        print(f"\nSub-meter accuracy improvement:")
        print(f"  Global: {global_validation['sub_meter_pct']:.1f}%")
        print(f"  ML: {ml_validation['sub_meter_pct']:.1f}%")
        print(f"  Gain: +{ml_validation['sub_meter_pct'] - global_validation['sub_meter_pct']:.1f}%")
else:
    print("No validation points found")