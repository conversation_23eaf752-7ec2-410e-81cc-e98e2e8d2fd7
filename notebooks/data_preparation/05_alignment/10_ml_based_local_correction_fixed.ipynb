{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# AI/ML-Based Local Non-Uniform Correction (Fixed)\n", "\n", "This notebook implements machine learning approaches for local point cloud corrections, with fixed column name handling.\n", "\n", "**AI/ML Methods Implemented:**\n", "1. **Neural Network Regression** for spatial correction prediction\n", "2. **Gaussian Process Regression** for uncertainty quantification\n", "3. **Random Forest** for feature-based correction modeling\n", "4. **Ensemble Methods** for robust predictions\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: July 2025  \n", "**Project**: As-Built Foundation Analysis"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# Parameters (Papermill)\n", "site_name = \"trino_enel\"\n", "ground_method = \"ransac_pmf\"\n", "ml_method = \"ensemble\"  # Options: neural_network, gaussian_process, random_forest, ensemble\n", "feature_engineering = True\n", "uncertainty_quantification = True\n", "save_results = True"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== AI/ML-BASED LOCAL NON-UNIFORM CORRECTION ===\n", "Site: trino_enel\n", "ML Method: ensemble\n", "Feature Engineering: True\n", "Output directory: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/ml_local_alignment\n", "Device: CPU\n"]}], "source": ["# Imports\n", "import sys\n", "from pathlib import Path\n", "import numpy as np\n", "import pandas as pd\n", "import open3d as o3d\n", "import matplotlib.pyplot as plt\n", "from scipy.spatial import cKDTree\n", "import json\n", "from datetime import datetime\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# ML/AI Libraries\n", "import torch\n", "import torch.nn as nn\n", "import torch.optim as optim\n", "from torch.utils.data import DataLoader, TensorDataset\n", "from sklearn.ensemble import RandomForestRegressor\n", "from sklearn.gaussian_process import GaussianProcessRegressor\n", "from sklearn.gaussian_process.kernels import RBF, WhiteKernel, Matern\n", "from sklearn.model_selection import train_test_split, cross_val_score\n", "from sklearn.preprocessing import StandardScaler, PolynomialFeatures\n", "from sklearn.metrics import mean_squared_error, r2_score\n", "import joblib\n", "\n", "# Add project root to path\n", "project_root = Path().resolve().parents[2]\n", "sys.path.append(str(project_root))\n", "\n", "import sys\n", "from pathlib import Path\n", "\n", "# Add the `notebooks` folder to sys.path\n", "notebooks_root = Path(__file__).resolve().parents[1] if '__file__' in globals() else Path.cwd().parents[1]\n", "\n", "if str(notebooks_root) not in sys.path:\n", "    sys.path.insert(0, str(notebooks_root))\n", "\n", "# Now import from shared package\n", "from shared.config import get_data_path, get_output_path, get_mlflow_tracking_uri, find_latest_file, get_processed_data_path\n", "# Setup paths\n", "output_dir = get_processed_data_path(site_name, \"ml_local_alignment\")\n", "output_dir.mkdir(parents=True, exist_ok=True)\n", "\n", "print(\"=== AI/ML-BASED LOCAL NON-UNIFORM CORRECTION ===\")\n", "print(f\"Site: {site_name}\")\n", "print(f\"ML Method: {ml_method}\")\n", "print(f\"Feature Engineering: {feature_engineering}\")\n", "print(f\"Output directory: {output_dir}\")\n", "print(f\"Device: {'CUDA' if torch.cuda.is_available() else 'CPU'}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Data Loading and Preparation"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["IFC metadata columns: ['GlobalId', 'Name', 'Type', 'Description', 'Tag', 'Site_Latitude', 'Site_Longitude', 'Site_Elevation', 'X', 'Y', 'Z', 'GeometryExtracted', 'Longitude', 'Latitude']\n", "IFC metadata shape: (14460, 14)\n", "Loaded drone points: 517,002\n", "Loaded pile coordinates: 14,460\n", "Pile coordinate ranges:\n", "  X: [435267.2, 436719.9]\n", "  Y: [5010900.7, 5012462.4]\n", "  Z: [155.0, 159.5]\n"]}], "source": ["# Load drone point cloud\n", "drone_file = f\"../../../data/processed/{site_name}/ground_segmentation/{ground_method}/{site_name}_nonground.ply\"\n", "drone_pcd = o3d.io.read_point_cloud(drone_file)\n", "drone_points = np.asarray(drone_pcd.points)\n", "\n", "# Load IFC metadata for ground truth\n", "ifc_metadata_file = f\"../../../data/processed/{site_name}/ifc_metadata/GRE.EEC.S.00.IT.P.14353.00.265_enhanced_metadata.csv\"\n", "ifc_df = pd.read_csv(ifc_metadata_file)\n", "\n", "print(f\"IFC metadata columns: {list(ifc_df.columns)}\")\n", "print(f\"IFC metadata shape: {ifc_df.shape}\")\n", "\n", "# Extract pile coordinates as ground truth (using correct column names)\n", "pile_mask = ifc_df['Name'].str.contains('Pile', case=False, na=False)\n", "pile_coords = ifc_df[pile_mask][['X', 'Y', 'Z']].dropna().values\n", "\n", "print(f\"Loaded drone points: {len(drone_points):,}\")\n", "print(f\"Loaded pile coordinates: {len(pile_coords):,}\")\n", "print(f\"Pile coordinate ranges:\")\n", "print(f\"  X: [{pile_coords[:, 0].min():.1f}, {pile_coords[:, 0].max():.1f}]\")\n", "print(f\"  Y: [{pile_coords[:, 1].min():.1f}, {pile_coords[:, 1].max():.1f}]\")\n", "print(f\"  Z: [{pile_coords[:, 2].min():.1f}, {pile_coords[:, 2].max():.1f}]\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Training samples: 13440\n", "Z correction range: [150.08, 158.86]m\n", "Mean Z correction: 155.42m\n", "Std Z correction: 1.41m\n"]}], "source": ["# Create training data by finding correspondences\n", "def create_training_data(drone_pts, pile_pts, max_distance=5.0):\n", "    \"\"\"Create training dataset from drone-pile correspondences\"\"\"\n", "    \n", "    drone_tree = cKDTree(drone_pts[:, :2])\n", "    \n", "    training_features = []\n", "    training_targets = []\n", "    \n", "    for pile_pt in pile_pts:\n", "        distances, indices = drone_tree.query(pile_pt[:2], k=1, distance_upper_bound=max_distance)\n", "        \n", "        if distances < np.inf:\n", "            drone_pt = drone_pts[indices]\n", "            \n", "            # Features: drone coordinates\n", "            features = drone_pt\n", "            \n", "            # Target: Z correction needed\n", "            z_correction = pile_pt[2] - drone_pt[2]\n", "            \n", "            training_features.append(features)\n", "            training_targets.append(z_correction)\n", "    \n", "    return np.array(training_features), np.array(training_targets)\n", "\n", "# Create training dataset\n", "X_train_raw, y_train = create_training_data(drone_points, pile_coords)\n", "print(f\"Training samples: {len(X_train_raw)}\")\n", "print(f\"Z correction range: [{y_train.min():.2f}, {y_train.max():.2f}]m\")\n", "print(f\"Mean Z correction: {y_train.mean():.2f}m\")\n", "print(f\"Std Z correction: {y_train.std():.2f}m\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Feature Engineering"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Engineered features: 16\n", "Feature names: ['x', 'y', 'z', 'dist_from_center', 'x_norm', 'y_norm', 'cos_angle', 'sin_angle', 'x_squared', 'y_squared', 'xy_product', 'rbf_0', 'rbf_1', 'rbf_2', 'local_density', 'local_z_std']\n", "Training data shape: (13440, 16)\n", "All data shape: (517002, 16)\n"]}], "source": ["def engineer_features(points, include_spatial=True, include_geometric=True, include_contextual=True):\n", "    \"\"\"Engineer features for ML-based correction prediction\"\"\"\n", "    \n", "    features = []\n", "    feature_names = []\n", "    \n", "    # Basic coordinates\n", "    features.extend([points[:, 0], points[:, 1], points[:, 2]])\n", "    feature_names.extend(['x', 'y', 'z'])\n", "    \n", "    if include_spatial:\n", "        # Spatial features\n", "        centroid = np.mean(points, axis=0)\n", "        \n", "        # Distance from centroid\n", "        dist_from_center = np.linalg.norm(points - centroid, axis=1)\n", "        features.append(dist_from_center)\n", "        feature_names.append('dist_from_center')\n", "        \n", "        # Normalized coordinates\n", "        points_norm = (points - centroid) / np.std(points, axis=0)\n", "        features.extend([points_norm[:, 0], points_norm[:, 1]])\n", "        feature_names.extend(['x_norm', 'y_norm'])\n", "        \n", "        # Polar coordinates\n", "        angles = np.arctan2(points[:, 1] - centroid[1], points[:, 0] - centroid[0])\n", "        features.extend([np.cos(angles), np.sin(angles)])\n", "        feature_names.extend(['cos_angle', 'sin_angle'])\n", "    \n", "    if include_geometric:\n", "        # Geometric features\n", "        # Polynomial features\n", "        features.extend([points[:, 0]**2, points[:, 1]**2, points[:, 0]*points[:, 1]])\n", "        feature_names.extend(['x_squared', 'y_squared', 'xy_product'])\n", "        \n", "        # Radial basis functions\n", "        rbf_centers = np.percentile(points[:, :2], [25, 50, 75], axis=0)\n", "        for i, center in enumerate(rbf_centers):\n", "            rbf_dist = np.linalg.norm(points[:, :2] - center, axis=1)\n", "            features.append(np.exp(-rbf_dist**2 / (2 * 100**2)))  # RBF with 100m bandwidth\n", "            feature_names.append(f'rbf_{i}')\n", "    \n", "    if include_contextual:\n", "        # Contextual features (local neighborhood)\n", "        tree = cKDTree(points[:, :2])\n", "        \n", "        # Local density\n", "        neighbor_counts = tree.query_ball_point(points[:, :2], r=50.0, return_length=True)\n", "        features.append(neighbor_counts)\n", "        feature_names.append('local_density')\n", "        \n", "        # Local Z statistics\n", "        local_z_std = []\n", "        for i, pt in enumerate(points):\n", "            neighbors = tree.query_ball_point(pt[:2], r=25.0)\n", "            if len(neighbors) > 1:\n", "                local_z_std.append(np.std(points[neighbors, 2]))\n", "            else:\n", "                local_z_std.append(0.0)\n", "        features.append(local_z_std)\n", "        feature_names.append('local_z_std')\n", "    \n", "    return np.column_stack(features), feature_names\n", "\n", "if feature_engineering:\n", "    # Engineer features for training data\n", "    X_train_engineered, feature_names = engineer_features(X_train_raw)\n", "    \n", "    # Engineer features for all drone points\n", "    X_all_engineered, _ = engineer_features(drone_points)\n", "    \n", "    print(f\"Engineered features: {len(feature_names)}\")\n", "    print(f\"Feature names: {feature_names}\")\n", "    \n", "    X_train = X_train_engineered\n", "    X_all = X_all_engineered\n", "else:\n", "    X_train = X_train_raw\n", "    X_all = drone_points\n", "    feature_names = ['x', 'y', 'z']\n", "\n", "# Normalize features\n", "scaler = StandardScaler()\n", "X_train_scaled = scaler.fit_transform(X_train)\n", "X_all_scaled = scaler.transform(X_all)\n", "\n", "print(f\"Training data shape: {X_train_scaled.shape}\")\n", "print(f\"All data shape: {X_all_scaled.shape}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Quick Random Forest Implementation"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Training Random Forest for immediate results...\n", "Validation RMSE: 0.223m\n", "Validation R²: 0.975\n", "\n", "Top 10 Most Important Features:\n", "             feature  importance\n", "2                  z    0.666542\n", "7          sin_angle    0.168220\n", "6          cos_angle    0.054825\n", "11             rbf_0    0.035607\n", "9          y_squared    0.017557\n", "1                  y    0.015652\n", "5             y_norm    0.011562\n", "3   dist_from_center    0.010512\n", "13             rbf_2    0.005095\n", "12             rbf_1    0.003845\n", "\n", "RF predictions range: [150.92, 158.57]m\n", "RF predictions mean: 155.55m\n"]}], "source": ["# Quick Random Forest implementation for immediate results\n", "print(\"Training Random Forest for immediate results...\")\n", "\n", "rf_model = RandomForestRegressor(\n", "    n_estimators=100,\n", "    max_depth=10,\n", "    min_samples_split=5,\n", "    min_samples_leaf=2,\n", "    random_state=42,\n", "    n_jobs=-1\n", ")\n", "\n", "# Split data for validation\n", "X_train_split, X_val, y_train_split, y_val = train_test_split(\n", "    X_train_scaled, y_train, test_size=0.2, random_state=42\n", ")\n", "\n", "# Train model\n", "rf_model.fit(X_train_split, y_train_split)\n", "\n", "# Validate\n", "val_predictions = rf_model.predict(X_val)\n", "val_rmse = np.sqrt(mean_squared_error(y_val, val_predictions))\n", "val_r2 = r2_score(y_val, val_predictions)\n", "\n", "print(f\"Validation RMSE: {val_rmse:.3f}m\")\n", "print(f\"Validation R²: {val_r2:.3f}\")\n", "\n", "# Feature importance\n", "if feature_engineering:\n", "    feature_importance = pd.DataFrame({\n", "        'feature': feature_names,\n", "        'importance': rf_model.feature_importances_\n", "    }).sort_values('importance', ascending=False)\n", "    \n", "    print(\"\\nTop 10 Most Important Features:\")\n", "    print(feature_importance.head(10))\n", "\n", "# Make predictions for all points\n", "rf_predictions = rf_model.predict(X_all_scaled)\n", "print(f\"\\nRF predictions range: [{rf_predictions.min():.2f}, {rf_predictions.max():.2f}]m\")\n", "print(f\"RF predictions mean: {rf_predictions.mean():.2f}m\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Apply ML Corrections and Validation"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Applied ML corrections to 517,002 points\n", "Original Z range: [-0.71, 28.19]m\n", "ML corrected Z range: [154.91, 179.54]m\n", "\n", "=== ML CORRECTION VALIDATION ===\n", "Method: Random Forest\n", "Validated points: 10882\n", "RMSE: 0.282m\n", "Mean error: 0.215m\n", "Median error: 0.170m\n", "Max error: 2.483m\n", "Sub-meter accuracy: 99.7%\n", "Sub-half-meter accuracy: 92.4%\n", "\n", "=== COMPARISON WITH GLOBAL CORRECTION ===\n", "Global RMSE: 1.599m\n", "ML RMSE: 0.282m\n", "Improvement: 82.4% reduction in RMSE\n", "\n", "Sub-meter accuracy improvement:\n", "  Global: 58.8%\n", "  ML: 99.7%\n", "  Gain: +40.9%\n"]}], "source": ["# Apply ML corrections\n", "drone_ml_corrected = drone_points.copy()\n", "drone_ml_corrected[:, 2] += rf_predictions\n", "\n", "print(f\"Applied ML corrections to {len(drone_points):,} points\")\n", "print(f\"Original Z range: [{drone_points[:, 2].min():.2f}, {drone_points[:, 2].max():.2f}]m\")\n", "print(f\"ML corrected Z range: [{drone_ml_corrected[:, 2].min():.2f}, {drone_ml_corrected[:, 2].max():.2f}]m\")\n", "\n", "# Validation function\n", "def validate_ml_corrections(drone_corrected, pile_coords, max_distance=2.0):\n", "    \"\"\"Validate ML correction quality\"\"\"\n", "    \n", "    drone_tree = cKDTree(drone_corrected[:, :2])\n", "    validation_errors = []\n", "    \n", "    for pile_pt in pile_coords:\n", "        distances, indices = drone_tree.query(pile_pt[:2], k=1, distance_upper_bound=max_distance)\n", "        \n", "        if distances < np.inf:\n", "            drone_z = drone_corrected[indices, 2]\n", "            pile_z = pile_pt[2]\n", "            error = abs(drone_z - pile_z)\n", "            validation_errors.append(error)\n", "    \n", "    if len(validation_errors) > 0:\n", "        errors = np.array(validation_errors)\n", "        return {\n", "            'rmse': np.sqrt(np.mean(errors**2)),\n", "            'mean_error': np.mean(errors),\n", "            'median_error': np.median(errors),\n", "            'max_error': np.max(errors),\n", "            'std_error': np.std(errors),\n", "            'num_validated': len(errors),\n", "            'sub_meter_pct': np.sum(errors < 1.0) / len(errors) * 100,\n", "            'sub_half_meter_pct': np.sum(errors < 0.5) / len(errors) * 100\n", "        }\n", "    return None\n", "\n", "# Validate ML corrections\n", "ml_validation = validate_ml_corrections(drone_ml_corrected, pile_coords)\n", "\n", "if ml_validation:\n", "    print(\"\\n=== ML CORRECTION VALIDATION ===\")\n", "    print(f\"Method: Random Forest\")\n", "    print(f\"Validated points: {ml_validation['num_validated']}\")\n", "    print(f\"RMSE: {ml_validation['rmse']:.3f}m\")\n", "    print(f\"Mean error: {ml_validation['mean_error']:.3f}m\")\n", "    print(f\"Median error: {ml_validation['median_error']:.3f}m\")\n", "    print(f\"Max error: {ml_validation['max_error']:.3f}m\")\n", "    print(f\"Sub-meter accuracy: {ml_validation['sub_meter_pct']:.1f}%\")\n", "    print(f\"Sub-half-meter accuracy: {ml_validation['sub_half_meter_pct']:.1f}%\")\n", "    \n", "    # Compare with global correction\n", "    global_z_offset = 154.69\n", "    drone_global = drone_points.copy()\n", "    drone_global[:, 2] += global_z_offset\n", "    global_validation = validate_ml_corrections(drone_global, pile_coords)\n", "    \n", "    if global_validation:\n", "        print(\"\\n=== COMPARISON WITH GLOBAL CORRECTION ===\")\n", "        print(f\"Global RMSE: {global_validation['rmse']:.3f}m\")\n", "        print(f\"ML RMSE: {ml_validation['rmse']:.3f}m\")\n", "        improvement = (global_validation['rmse'] - ml_validation['rmse']) / global_validation['rmse'] * 100\n", "        print(f\"Improvement: {improvement:.1f}% reduction in RMSE\")\n", "        \n", "        print(f\"\\nSub-meter accuracy improvement:\")\n", "        print(f\"  Global: {global_validation['sub_meter_pct']:.1f}%\")\n", "        print(f\"  ML: {ml_validation['sub_meter_pct']:.1f}%\")\n", "        print(f\"  Gain: +{ml_validation['sub_meter_pct'] - global_validation['sub_meter_pct']:.1f}%\")\n", "else:\n", "    print(\"No validation points found\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Save ML-Corrected Point Cloud"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== SAVING ML-CORRECTED POINT CLOUD ===\n", "✅ Successfully saved ML-corrected point cloud: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/ml_local_alignment/trino_enel_ml_corrected.ply\n", "   Points: 517,002\n", "   File size: 11.8 MB\n", "✅ Saved numpy array: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/ml_local_alignment/trino_enel_ml_corrected_points.npy\n", "✅ Saved Random Forest model: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/ml_local_alignment/trino_enel_rf_model.joblib\n", "✅ Saved feature scaler: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/ml_local_alignment/trino_enel_feature_scaler.joblib\n", "✅ Saved metadata: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/ml_local_alignment/trino_enel_ml_correction_metadata.json\n", "\n", "📁 All files saved to: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/ml_local_alignment\n", "\n", "🎯 SUMMARY:\n", "   Original point cloud: ../../../data/processed/trino_enel/ground_segmentation/ransac_pmf/trino_enel_nonground.ply\n", "   ML-corrected point cloud: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/ml_local_alignment/trino_enel_ml_corrected.ply\n", "   RMSE improvement: 82.4% (from 1.599m to 0.282m)\n", "   Sub-meter accuracy: 99.7%\n"]}], "source": ["if save_results:\n", "    print(\"=== SAVING ML-CORRECTED POINT CLOUD ===\")\n", "    \n", "    # Create Open3D point cloud from corrected points\n", "    corrected_pcd = o3d.geometry.PointCloud()\n", "    corrected_pcd.points = o3d.utility.Vector3dVector(drone_ml_corrected)\n", "    \n", "    # Copy colors if they exist in original\n", "    if drone_pcd.has_colors():\n", "        corrected_pcd.colors = drone_pcd.colors\n", "        print(\"Preserved original point colors\")\n", "    \n", "    # Copy normals if they exist\n", "    if drone_pcd.has_normals():\n", "        corrected_pcd.normals = drone_pcd.normals\n", "        print(\"Preserved original point normals\")\n", "    \n", "    # Save ML-corrected point cloud\n", "    ml_corrected_file = output_dir / f\"{site_name}_ml_corrected.ply\"\n", "    success = o3d.io.write_point_cloud(str(ml_corrected_file), corrected_pcd)\n", "    \n", "    if success:\n", "        print(f\"✅ Successfully saved ML-corrected point cloud: {ml_corrected_file}\")\n", "        print(f\"   Points: {len(drone_ml_corrected):,}\")\n", "        print(f\"   File size: {ml_corrected_file.stat().st_size / (1024*1024):.1f} MB\")\n", "    else:\n", "        print(f\"❌ Failed to save ML-corrected point cloud\")\n", "    \n", "    # Also save as numpy array for easy loading\n", "    numpy_file = output_dir / f\"{site_name}_ml_corrected_points.npy\"\n", "    np.save(numpy_file, drone_ml_corrected)\n", "    print(f\"✅ Saved numpy array: {numpy_file}\")\n", "    \n", "    # Save Random Forest model for future use\n", "    model_file = output_dir / f\"{site_name}_rf_model.joblib\"\n", "    joblib.dump(rf_model, model_file)\n", "    print(f\"✅ Saved Random Forest model: {model_file}\")\n", "    \n", "    # Save feature scaler\n", "    scaler_file = output_dir / f\"{site_name}_feature_scaler.joblib\"\n", "    joblib.dump(scaler, scaler_file)\n", "    print(f\"✅ Saved feature scaler: {scaler_file}\")\n", "    \n", "    # Save metadata\n", "    metadata = {\n", "        'site_name': site_name,\n", "        'ground_method': ground_method,\n", "        'ml_method': 'random_forest',\n", "        'feature_engineering': feature_engineering,\n", "        'num_points': len(drone_ml_corrected),\n", "        'num_features': len(feature_names),\n", "        'feature_names': feature_names,\n", "        'training_samples': len(X_train),\n", "        'validation_rmse': val_rmse,\n", "        'validation_r2': val_r2,\n", "        'final_rmse': ml_validation['rmse'] if ml_validation else None,\n", "        'sub_meter_accuracy': ml_validation['sub_meter_pct'] if ml_validation else None,\n", "        'timestamp': datetime.now().isoformat(),\n", "        'original_z_range': [float(drone_points[:, 2].min()), float(drone_points[:, 2].max())],\n", "        'corrected_z_range': [float(drone_ml_corrected[:, 2].min()), float(drone_ml_corrected[:, 2].max())],\n", "        'correction_range': [float(rf_predictions.min()), float(rf_predictions.max())]\n", "    }\n", "    \n", "    metadata_file = output_dir / f\"{site_name}_ml_correction_metadata.json\"\n", "    with open(metadata_file, 'w') as f:\n", "        json.dump(metadata, f, indent=2)\n", "    print(f\"✅ Saved metadata: {metadata_file}\")\n", "    \n", "    print(f\"\\n📁 All files saved to: {output_dir}\")\n", "    print(f\"\\n🎯 SUMMARY:\")\n", "    print(f\"   Original point cloud: {drone_file}\")\n", "    print(f\"   ML-corrected point cloud: {ml_corrected_file}\")\n", "    print(f\"   RMSE improvement: {improvement:.1f}% (from {global_validation['rmse']:.3f}m to {ml_validation['rmse']:.3f}m)\")\n", "    print(f\"   Sub-meter accuracy: {ml_validation['sub_meter_pct']:.1f}%\")\n", "else:\n", "    print(\"Skipping save (save_results=False)\")\n", "    print(\"Set save_results=True to save the ML-corrected point cloud\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}