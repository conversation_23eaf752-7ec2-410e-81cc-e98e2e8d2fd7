{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Local Non-Uniform Correction for Sub-Meter Accuracy\n", "\n", "This notebook implements local, non-uniform corrections to address the limitations of global similarity transformations when dealing with drone point clouds that lack cross-hatch flight patterns.\n", "\n", "**Problem:** Single-pass drone flights without cross-hatch patterns create:\n", "- Non-uniform Z accuracy across the site\n", "- Systematic errors that vary spatially\n", "- Inability to achieve sub-meter accuracy with global transformations\n", "\n", "**Solution:** Spatial partitioning with local transformations\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: July 2025  \n", "**Project**: As-Built Foundation Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Parameters (Papermill)\n", "site_name = \"trino_enel\"\n", "ground_method = \"ransac_pmf\"\n", "grid_size = 100  # meters - size of local correction zones\n", "min_gcp_per_zone = 3  # minimum GCPs needed for local transformation\n", "overlap_factor = 0.3  # overlap between zones for smooth transitions\n", "save_results = True"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Imports\n", "import sys\n", "from pathlib import Path\n", "import numpy as np\n", "import pandas as pd\n", "import open3d as o3d\n", "import laspy\n", "import matplotlib.pyplot as plt\n", "from scipy.spatial import cKDTree, Voronoi\n", "from scipy.interpolate import griddata, RBFInterpolator\n", "from sklearn.cluster import KMeans\n", "import json\n", "from datetime import datetime\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Add project root to path\n", "project_root = Path().resolve().parents[2]\n", "sys.path.append(str(project_root))\n", "\n", "from src.config.paths import get_processed_data_path, get_output_path\n", "\n", "# Setup paths\n", "output_dir = get_processed_data_path(site_name, \"local_nonuniform_alignment\")\n", "output_dir.mkdir(parents=True, exist_ok=True)\n", "\n", "print(\"=== LOCAL NON-UNIFORM CORRECTION FOR SUB-METER ACCURACY ===\")\n", "print(f\"Site: {site_name}\")\n", "print(f\"Ground method: {ground_method}\")\n", "print(f\"Grid size: {grid_size}m\")\n", "print(f\"Output directory: {output_dir}\")\n", "print(f\"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Data Loading"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load drone point cloud\n", "drone_file = f\"../../../data/processed/{site_name}/ground_segmentation/{ground_method}/{site_name}_nonground.ply\"\n", "drone_pcd = o3d.io.read_point_cloud(drone_file)\n", "drone_points = np.asarray(drone_pcd.points)\n", "\n", "# Load IFC metadata for pile coordinates\n", "ifc_metadata_file = f\"../../../data/processed/{site_name}/ifc_metadata/GRE.EEC.S.00.IT.P.14353.00.265_enhanced_metadata.csv\"\n", "ifc_df = pd.read_csv(ifc_metadata_file)\n", "\n", "# Extract pile coordinates\n", "pile_mask = ifc_df['Name'].str.contains('Pile', case=False, na=False)\n", "pile_coords = ifc_df[pile_mask][['X_Coordinate', 'Y_Coordinate', 'Z_Coordinate']].dropna().values\n", "\n", "print(f\"Loaded drone points: {len(drone_points):,}\")\n", "print(f\"Loaded IFC pile coordinates: {len(pile_coords):,}\")\n", "print(f\"Drone bounds: X[{drone_points[:, 0].min():.1f}, {drone_points[:, 0].max():.1f}], \"\n", "      f\"Y[{drone_points[:, 1].min():.1f}, {drone_points[:, 1].max():.1f}], \"\n", "      f\"Z[{drone_points[:, 2].min():.1f}, {drone_points[:, 2].max():.1f}]\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Spatial Partitioning Strategy"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def create_spatial_grid(points, grid_size, overlap_factor=0.3):\n", "    \"\"\"Create overlapping spatial grid for local corrections\"\"\"\n", "    \n", "    # Calculate bounds\n", "    x_min, y_min = points[:, :2].min(axis=0)\n", "    x_max, y_max = points[:, :2].max(axis=0)\n", "    \n", "    # Calculate grid parameters\n", "    overlap_size = grid_size * overlap_factor\n", "    step_size = grid_size - overlap_size\n", "    \n", "    # Create grid centers\n", "    x_centers = np.arange(x_min + grid_size/2, x_max, step_size)\n", "    y_centers = np.arange(y_min + grid_size/2, y_max, step_size)\n", "    \n", "    grid_zones = []\n", "    for i, x_center in enumerate(x_centers):\n", "        for j, y_center in enumerate(y_centers):\n", "            zone = {\n", "                'id': f\"zone_{i}_{j}\",\n", "                'center': np.array([x_center, y_center]),\n", "                'bounds': {\n", "                    'x_min': x_center - grid_size/2,\n", "                    'x_max': x_center + grid_size/2,\n", "                    'y_min': y_center - grid_size/2,\n", "                    'y_max': y_center + grid_size/2\n", "                }\n", "            }\n", "            grid_zones.append(zone)\n", "    \n", "    print(f\"Created {len(grid_zones)} spatial zones ({len(x_centers)}x{len(y_centers)} grid)\")\n", "    print(f\"Zone size: {grid_size}m x {grid_size}m\")\n", "    print(f\"Overlap: {overlap_size:.1f}m ({overlap_factor*100:.0f}%)\")\n", "    \n", "    return grid_zones\n", "\n", "# Create spatial grid\n", "spatial_zones = create_spatial_grid(drone_points, grid_size, overlap_factor)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def assign_points_to_zones(points, zones):\n", "    \"\"\"Assign points to spatial zones with overlap handling\"\"\"\n", "    \n", "    zone_assignments = {zone['id']: [] for zone in zones}\n", "    \n", "    for i, point in enumerate(points):\n", "        x, y = point[:2]\n", "        \n", "        for zone in zones:\n", "            bounds = zone['bounds']\n", "            if (bounds['x_min'] <= x <= bounds['x_max'] and \n", "                bounds['y_min'] <= y <= bounds['y_max']):\n", "                zone_assignments[zone['id']].append(i)\n", "    \n", "    # Convert to numpy arrays and filter empty zones\n", "    valid_zones = {}\n", "    for zone_id, indices in zone_assignments.items():\n", "        if len(indices) > 0:\n", "            valid_zones[zone_id] = np.array(indices)\n", "    \n", "    print(f\"Valid zones with points: {len(valid_zones)}/{len(zones)}\")\n", "    \n", "    return valid_zones\n", "\n", "# Assign drone points to zones\n", "drone_zone_assignments = assign_points_to_zones(drone_points, spatial_zones)\n", "\n", "# Assign pile coordinates to zones\n", "pile_zone_assignments = assign_points_to_zones(pile_coords, spatial_zones)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Local Ground Control Point Matching"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def find_local_correspondences(drone_pts, pile_pts, max_distance=5.0):\n", "    \"\"\"Find corresponding points between drone and pile coordinates within a zone\"\"\"\n", "    \n", "    if len(drone_pts) == 0 or len(pile_pts) == 0:\n", "        return np.array([]), np.array([])\n", "    \n", "    # Build KD-tree for drone points (XY only)\n", "    drone_tree = cKDTree(drone_pts[:, :2])\n", "    \n", "    correspondences_drone = []\n", "    correspondences_pile = []\n", "    \n", "    for pile_pt in pile_pts:\n", "        # Find nearest drone point\n", "        distances, indices = drone_tree.query(pile_pt[:2], k=1, distance_upper_bound=max_distance)\n", "        \n", "        if distances < np.inf:\n", "            correspondences_drone.append(drone_pts[indices])\n", "            correspondences_pile.append(pile_pt)\n", "    \n", "    if len(correspondences_drone) > 0:\n", "        return np.array(correspondences_drone), np.array(correspondences_pile)\n", "    else:\n", "        return np.array([]), np.array([])\n", "\n", "# Find local correspondences for each zone\n", "zone_correspondences = {}\n", "\n", "for zone in spatial_zones:\n", "    zone_id = zone['id']\n", "    \n", "    # Get points in this zone\n", "    if zone_id in drone_zone_assignments and zone_id in pile_zone_assignments:\n", "        drone_indices = drone_zone_assignments[zone_id]\n", "        pile_indices = pile_zone_assignments[zone_id]\n", "        \n", "        zone_drone_pts = drone_points[drone_indices]\n", "        zone_pile_pts = pile_coords[pile_indices]\n", "        \n", "        # Find correspondences\n", "        drone_corr, pile_corr = find_local_correspondences(zone_drone_pts, zone_pile_pts)\n", "        \n", "        if len(drone_corr) >= min_gcp_per_zone:\n", "            zone_correspondences[zone_id] = {\n", "                'drone_points': drone_corr,\n", "                'pile_points': pile_corr,\n", "                'num_correspondences': len(drone_corr),\n", "                'zone_center': zone['center']\n", "            }\n", "\n", "print(f\"Zones with sufficient correspondences: {len(zone_correspondences)}\")\n", "for zone_id, data in zone_correspondences.items():\n", "    print(f\"  {zone_id}: {data['num_correspondences']} correspondences\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Local Transformation Computation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def compute_local_z_correction(drone_pts, pile_pts):\n", "    \"\"\"Compute local Z correction using robust statistics\"\"\"\n", "    \n", "    z_differences = pile_pts[:, 2] - drone_pts[:, 2]\n", "    \n", "    # Use median for robustness\n", "    z_offset = np.median(z_differences)\n", "    z_std = np.std(z_differences)\n", "    \n", "    # Filter outliers (beyond 2 standard deviations)\n", "    valid_mask = np.abs(z_differences - z_offset) < 2 * z_std\n", "    \n", "    if np.sum(valid_mask) >= 3:  # Need at least 3 points\n", "        z_offset_filtered = np.median(z_differences[valid_mask])\n", "        rmse = np.sqrt(np.mean((z_differences[valid_mask] - z_offset_filtered)**2))\n", "    else:\n", "        z_offset_filtered = z_offset\n", "        rmse = z_std\n", "    \n", "    return {\n", "        'z_offset': z_offset_filtered,\n", "        'rmse': rmse,\n", "        'num_points': np.sum(valid_mask),\n", "        'outliers_removed': len(z_differences) - np.sum(valid_mask)\n", "    }\n", "\n", "# Compute local transformations\n", "local_transformations = {}\n", "\n", "for zone_id, data in zone_correspondences.items():\n", "    drone_pts = data['drone_points']\n", "    pile_pts = data['pile_points']\n", "    \n", "    # Compute local Z correction\n", "    transform = compute_local_z_correction(drone_pts, pile_pts)\n", "    transform['zone_center'] = data['zone_center']\n", "    transform['zone_id'] = zone_id\n", "    \n", "    local_transformations[zone_id] = transform\n", "    \n", "    print(f\"{zone_id}: Z offset = {transform['z_offset']:.2f}m, \"\n", "          f\"RMSE = {transform['rmse']:.2f}m, \"\n", "          f\"Points = {transform['num_points']}\")\n", "\n", "print(f\"\\nComputed {len(local_transformations)} local transformations\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Spatial Interpolation for Smooth Transitions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def create_correction_field(transformations, drone_points, method='rbf'):\n", "    \"\"\"Create smooth correction field using spatial interpolation\"\"\"\n", "    \n", "    if len(transformations) == 0:\n", "        print(\"No transformations available for interpolation\")\n", "        return np.zeros(len(drone_points))\n", "    \n", "    # Extract transformation centers and Z offsets\n", "    centers = np.array([t['zone_center'] for t in transformations.values()])\n", "    z_offsets = np.array([t['z_offset'] for t in transformations.values()])\n", "    \n", "    # Query points (drone point XY coordinates)\n", "    query_points = drone_points[:, :2]\n", "    \n", "    if method == 'rbf' and len(centers) >= 3:\n", "        # Radial Basis Function interpolation\n", "        rbf = RBFInterpolator(centers, z_offsets, kernel='thin_plate_spline', smoothing=1.0)\n", "        corrections = rbf(query_points)\n", "    else:\n", "        # Fallback to linear interpolation\n", "        corrections = griddata(centers, z_offsets, query_points, method='linear', fill_value=np.median(z_offsets))\n", "        \n", "        # Fill any remaining NaN values\n", "        nan_mask = np.isnan(corrections)\n", "        if np.any(nan_mask):\n", "            corrections[nan_mask] = np.median(z_offsets)\n", "    \n", "    print(f\"Created correction field using {method} interpolation\")\n", "    print(f\"Correction range: [{corrections.min():.2f}, {corrections.max():.2f}]m\")\n", "    print(f\"Mean correction: {corrections.mean():.2f}m\")\n", "    \n", "    return corrections\n", "\n", "# Create smooth correction field\n", "z_corrections = create_correction_field(local_transformations, drone_points, method='rbf')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Apply Local Corrections"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Apply local corrections to drone point cloud\n", "drone_corrected = drone_points.copy()\n", "drone_corrected[:, 2] += z_corrections\n", "\n", "print(f\"Applied local corrections to {len(drone_points):,} points\")\n", "print(f\"Original Z range: [{drone_points[:, 2].min():.2f}, {drone_points[:, 2].max():.2f}]m\")\n", "print(f\"Corrected Z range: [{drone_corrected[:, 2].min():.2f}, {drone_corrected[:, 2].max():.2f}]m\")\n", "print(f\"Z shift applied: [{z_corrections.min():.2f}, {z_corrections.max():.2f}]m\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Validation and Quality Assessment"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def validate_local_corrections(drone_corrected, pile_coords, max_distance=2.0):\n", "    \"\"\"Validate the quality of local corrections\"\"\"\n", "    \n", "    # Find correspondences for validation\n", "    drone_tree = cKDTree(drone_corrected[:, :2])\n", "    \n", "    validation_errors = []\n", "    \n", "    for pile_pt in pile_coords:\n", "        distances, indices = drone_tree.query(pile_pt[:2], k=1, distance_upper_bound=max_distance)\n", "        \n", "        if distances < np.inf:\n", "            drone_z = drone_corrected[indices, 2]\n", "            pile_z = pile_pt[2]\n", "            error = abs(drone_z - pile_z)\n", "            validation_errors.append(error)\n", "    \n", "    if len(validation_errors) > 0:\n", "        errors = np.array(validation_errors)\n", "        return {\n", "            'rmse': np.sqrt(np.mean(errors**2)),\n", "            'mean_error': np.mean(errors),\n", "            'median_error': np.median(errors),\n", "            'max_error': np.max(errors),\n", "            'std_error': np.std(errors),\n", "            'num_validated': len(errors),\n", "            'sub_meter_pct': np.sum(errors < 1.0) / len(errors) * 100,\n", "            'sub_half_meter_pct': np.sum(errors < 0.5) / len(errors) * 100\n", "        }\n", "    else:\n", "        return None\n", "\n", "# Validate corrections\n", "validation_results = validate_local_corrections(drone_corrected, pile_coords)\n", "\n", "if validation_results:\n", "    print(\"=== LOCAL CORRECTION VALIDATION ===\")\n", "    print(f\"Validated points: {validation_results['num_validated']}\")\n", "    print(f\"RMSE: {validation_results['rmse']:.3f}m\")\n", "    print(f\"Mean error: {validation_results['mean_error']:.3f}m\")\n", "    print(f\"Median error: {validation_results['median_error']:.3f}m\")\n", "    print(f\"Max error: {validation_results['max_error']:.3f}m\")\n", "    print(f\"Sub-meter accuracy: {validation_results['sub_meter_pct']:.1f}%\")\n", "    print(f\"Sub-half-meter accuracy: {validation_results['sub_half_meter_pct']:.1f}%\")\n", "    \n", "    # Compare with global correction (154.69m from previous analysis)\n", "    global_z_offset = 154.69\n", "    drone_global = drone_points.copy()\n", "    drone_global[:, 2] += global_z_offset\n", "    \n", "    global_validation = validate_local_corrections(drone_global, pile_coords)\n", "    \n", "    if global_validation:\n", "        print(\"\\n=== COMPARISON WITH GLOBAL CORRECTION ===\")\n", "        print(f\"Global RMSE: {global_validation['rmse']:.3f}m\")\n", "        print(f\"Local RMSE: {validation_results['rmse']:.3f}m\")\n", "        improvement = (global_validation['rmse'] - validation_results['rmse']) / global_validation['rmse'] * 100\n", "        print(f\"Improvement: {improvement:.1f}% reduction in RMSE\")\n", "        \n", "        print(f\"\\nSub-meter accuracy improvement:\")\n", "        print(f\"  Global: {global_validation['sub_meter_pct']:.1f}%\")\n", "        print(f\"  Local: {validation_results['sub_meter_pct']:.1f}%\")\n", "        print(f\"  Gain: +{validation_results['sub_meter_pct'] - global_validation['sub_meter_pct']:.1f}%\")\nelse:\n", "    print(\"No validation points found\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. Visualization and Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create visualization of correction field\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n", "fig.suptitle('Local Non-Uniform Correction Analysis', fontsize=16)\n", "\n", "# Plot 1: Spatial zones and transformations\n", "ax1 = axes[0, 0]\n", "ax1.scatter(drone_points[::100, 0], drone_points[::100, 1], c='lightblue', s=1, alpha=0.5, label='Drone points')\n", "ax1.scatter(pile_coords[:, 0], pile_coords[:, 1], c='red', s=10, alpha=0.7, label='IFC piles')\n", "\n", "# Plot transformation centers\n", "if local_transformations:\n", "    centers = np.array([t['zone_center'] for t in local_transformations.values()])\n", "    z_offsets = np.array([t['z_offset'] for t in local_transformations.values()])\n", "    scatter = ax1.scatter(centers[:, 0], centers[:, 1], c=z_offsets, s=100, \n", "                         cmap='viridis', edgecolors='black', label='Zone centers')\n", "    plt.colorbar(scatter, ax=ax1, label='Z offset (m)')\n", "\n", "ax1.set_xlabel('X (UTM Easting)')\n", "ax1.set_ylabel('Y (UTM Northing)')\n", "ax1.set_title('Spatial Zones and Local Transformations')\n", "ax1.legend()\n", "ax1.grid(True, alpha=0.3)\n", "\n", "# Plot 2: Correction field heatmap\n", "ax2 = axes[0, 1]\n", "if len(z_corrections) > 0:\n", "    # Create grid for interpolation\n", "    x_grid = np.linspace(drone_points[:, 0].min(), drone_points[:, 0].max(), 100)\n", "    y_grid = np.linspace(drone_points[:, 1].min(), drone_points[:, 1].max(), 100)\n", "    X_grid, Y_grid = np.meshgrid(x_grid, y_grid)\n", "    \n", "    # Interpolate corrections to grid\n", "    grid_points = np.column_stack([X_grid.ravel(), Y_grid.ravel()])\n", "    if len(local_transformations) >= 3:\n", "        centers = np.array([t['zone_center'] for t in local_transformations.values()])\n", "        z_offsets = np.array([t['z_offset'] for t in local_transformations.values()])\n", "        grid_corrections = griddata(centers, z_offsets, grid_points, method='linear')\n", "        grid_corrections = grid_corrections.reshape(X_grid.shape)\n", "        \n", "        im = ax2.contourf(X_grid, Y_grid, grid_corrections, levels=20, cmap='viridis')\n", "        plt.colorbar(im, ax=ax2, label='Z correction (m)')\n", "    \n", "ax2.set_xlabel('X (UTM Easting)')\n", "ax2.set_ylabel('Y (UTM Northing)')\n", "ax2.set_title('Correction Field Heatmap')\n", "\n", "# Plot 3: Error distribution\n", "ax3 = axes[1, 0]\n", "if validation_results:\n", "    # Recreate validation errors for histogram\n", "    drone_tree = cKDTree(drone_corrected[:, :2])\n", "    errors = []\n", "    for pile_pt in pile_coords:\n", "        distances, indices = drone_tree.query(pile_pt[:2], k=1, distance_upper_bound=2.0)\n", "        if distances < np.inf:\n", "            error = abs(drone_corrected[indices, 2] - pile_pt[2])\n", "            errors.append(error)\n", "    \n", "    ax3.hist(errors, bins=30, alpha=0.7, color='skyblue', edgecolor='black')\n", "    ax3.axvline(np.mean(errors), color='red', linestyle='--', label=f'Mean: {np.mean(errors):.2f}m')\n", "    ax3.axvline(np.median(errors), color='orange', linestyle='--', label=f'Median: {np.median(errors):.2f}m')\n", "    ax3.axvline(1.0, color='green', linestyle='-', label='1m threshold')\n", "    ax3.axvline(0.5, color='purple', linestyle='-', label='0.5m threshold')\n", "    \n", "ax3.set_xlabel('Absolute Error (m)')\n", "ax3.set_ylabel('Frequency')\n", "ax3.set_title('Error Distribution After Local Correction')\n", "ax3.legend()\n", "ax3.grid(True, alpha=0.3)\n", "\n", "# Plot 4: Comparison with global correction\n", "ax4 = axes[1, 1]\n", "if validation_results and global_validation:\n", "    methods = ['Global\\nCorrection', 'Local\\nCorrection']\n", "    rmse_values = [global_validation['rmse'], validation_results['rmse']]\n", "    sub_meter_values = [global_validation['sub_meter_pct'], validation_results['sub_meter_pct']]\n", "    \n", "    x = np.arange(len(methods))\n", "    width = 0.35\n", "    \n", "    bars1 = ax4.bar(x - width/2, rmse_values, width, label='RMSE (m)', color='lightcoral')\n", "    ax4_twin = ax4.twinx()\n", "    bars2 = ax4_twin.bar(x + width/2, sub_meter_values, width, label='Sub-meter %', color='lightblue')\n", "    \n", "    ax4.set_xlabel('Method')\n", "    ax4.set_ylabel('RMSE (m)', color='red')\n", "    ax4_twin.set_ylabel('Sub-meter Accuracy (%)', color='blue')\n", "    ax4.set_title('Global vs Local Correction Comparison')\n", "    ax4.set_xticks(x)\n", "    ax4.set_xticklabels(methods)\n", "    \n", "    # Add value labels on bars\n", "    for bar, value in zip(bars1, rmse_values):\n", "        ax4.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.1, \n", "                f'{value:.2f}', ha='center', va='bottom')\n", "    for bar, value in zip(bars2, sub_meter_values):\n", "        ax4_twin.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1, \n", "                     f'{value:.1f}%', ha='center', va='bottom')\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 9. Save Results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if save_results:\n", "    print(\"=== SAVING RESULTS ===\")\n", "    \n", "    # Save corrected point cloud\n", "    corrected_pcd = o3d.geometry.PointCloud()\n", "    corrected_pcd.points = o3d.utility.Vector3dVector(drone_corrected)\n", "    corrected_file = output_dir / f\"{site_name}_local_corrected.ply\"\n", "    o3d.io.write_point_cloud(str(corrected_file), corrected_pcd)\n", "    print(f\"Saved corrected point cloud: {corrected_file}\")\n", "    \n", "    # Save correction field\n", "    correction_data = {\n", "        'points': drone_points.tolist(),\n", "        'corrections': z_corrections.tolist(),\n", "        'corrected_points': drone_corrected.tolist()\n", "    }\n", "    correction_file = output_dir / f\"{site_name}_correction_field.json\"\n", "    with open(correction_file, 'w') as f:\n", "        json.dump(correction_data, f, indent=2)\n", "    print(f\"Saved correction field: {correction_file}\")\n", "    \n", "    # Save local transformations\n", "    transform_data = {}\n", "    for zone_id, transform in local_transformations.items():\n", "        transform_data[zone_id] = {\n", "            'z_offset': float(transform['z_offset']),\n", "            'rmse': float(transform['rmse']),\n", "            'num_points': int(transform['num_points']),\n", "            'outliers_removed': int(transform['outliers_removed']),\n", "            'zone_center': transform['zone_center'].tolist()\n", "        }\n", "    \n", "    transform_file = output_dir / f\"{site_name}_local_transformations.json\"\n", "    with open(transform_file, 'w') as f:\n", "        json.dump(transform_data, f, indent=2)\n", "    print(f\"Saved local transformations: {transform_file}\")\n", "    \n", "    # Save validation results\n", "    if validation_results:\n", "        validation_file = output_dir / f\"{site_name}_validation_results.json\"\n", "        with open(validation_file, 'w') as f:\n", "            json.dump({\n", "                'local_correction': {k: float(v) if isinstance(v, (int, float)) else v \n", "                                   for k, v in validation_results.items()},\n", "                'global_correction': {k: float(v) if isinstance(v, (int, float)) else v \n", "                                    for k, v in global_validation.items()} if global_validation else None,\n", "                'improvement_pct': float(improvement) if 'improvement' in locals() else None,\n", "                'parameters': {\n", "                    'grid_size': grid_size,\n", "                    'min_gcp_per_zone': min_gcp_per_zone,\n", "                    'overlap_factor': overlap_factor,\n", "                    'site_name': site_name,\n", "                    'ground_method': ground_method\n", "                }\n", "            }, f, indent=2)\n", "        print(f\"Saved validation results: {validation_file}\")\n", "    \n", "    print(f\"\\nAll results saved to: {output_dir}\")\nelse:\n    print(\"Skipping save (save_results=False)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 10. Summary and Recommendations"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"\\n\" + \"=\"*80)\n", "print(\"LOCAL NON-UNIFORM CORRECTION SUMMARY\")\n", "print(\"=\"*80)\n", "\n", "print(f\"\\nPROBLEM ADDRESSED:\")\n", "print(f\"  - Drone flight without cross-hatch pattern\")\n", "print(f\"  - Non-uniform Z accuracy across site\")\n", "print(f\"  - Global transformation RMSE: 1.771m (insufficient for sub-meter accuracy)\")\n", "\n", "print(f\"\\nSOLUTION IMPLEMENTED:\")\n", "print(f\"  - Spatial partitioning: {grid_size}m x {grid_size}m zones\")\n", "print(f\"  - Local transformations: {len(local_transformations)} zones\")\n", "print(f\"  - Smooth interpolation: RBF with {overlap_factor*100:.0f}% overlap\")\n", "\n", "if validation_results:\n", "    print(f\"\\nRESULTS ACHIEVED:\")\n", "    print(f\"  - Local correction RMSE: {validation_results['rmse']:.3f}m\")\n", "    print(f\"  - Sub-meter accuracy: {validation_results['sub_meter_pct']:.1f}%\")\n", "    print(f\"  - Sub-half-meter accuracy: {validation_results['sub_half_meter_pct']:.1f}%\")\n", "    \n", "    if global_validation and 'improvement' in locals():\n", "        print(f\"  - Improvement over global: {improvement:.1f}% RMSE reduction\")\n", "        print(f\"  - Sub-meter gain: +{validation_results['sub_meter_pct'] - global_validation['sub_meter_pct']:.1f}%\")\n", "\n", "print(f\"\\nCONSTRUCTION MONITORING SUITABILITY:\")\n", "if validation_results:\n", "    accuracy_levels = {\n", "        'Survey Grade (≤0.1m)': validation_results['rmse'] <= 0.1,\n", "        'Construction Layout (≤0.5m)': validation_results['rmse'] <= 0.5,\n", "        'Quality Control (≤2.0m)': validation_results['rmse'] <= 2.0,\n", "        'Progress Monitoring (≤5.0m)': validation_results['rmse'] <= 5.0\n", "    }\n", "    \n", "    for application, suitable in accuracy_levels.items():\n", "        status = \"✅ Suitable\" if suitable else \"❌ Not suitable\"\n", "        print(f\"  {application}: {status}\")\n", "\n", "print(f\"\\nRECOMMENDATIONS FOR FUTURE DRONE SURVEYS:\")\n", "print(f\"  1. Implement cross-hatch flight patterns (90° intersecting passes)\")\n", "print(f\"  2. Increase overlap: 80% forward, 70% side overlap minimum\")\n", "print(f\"  3. Deploy Ground Control Points (GCP) every 100-200m\")\n", "print(f\"  4. Use RTK/PPK GPS for improved absolute positioning\")\n", "print(f\"  5. Fly at consistent altitude with stable camera angles\")\n", "print(f\"  6. Consider multiple flight altitudes for better Z accuracy\")\n", "\n", "print(f\"\\nNEXT STEPS:\")\n", "print(f\"  1. Apply local corrections to full point cloud dataset\")\n", "print(f\"  2. Validate with independent ground truth measurements\")\n", "print(f\"  3. Integrate with pile detection and compliance analysis\")\n", "print(f\"  4. Document methodology for future projects\")\n", "\n", "print(\"\\n\" + \"=\"*80)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}