{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Coordinate-Only Point Cloud Alignment\n", "\n", "This notebook implements the **corrected** coordinate-only alignment using metadata coordinates instead of the offset point cloud.\n", "\n", "**Key Fix:**\n", "- **Uses IFC metadata coordinates directly** (verified as correct)\n", "- **Avoids the 499m offset** found in the generated point cloud\n", "- **Provides accurate baseline** for comparison with ICP methods\n", "\n", "**Based on coordinate verification findings:**\n", "- Metadata coordinates: Consistent and correct\n", "- Point cloud coordinates: 499m offset detected\n", "- Geographic coordinates: Correctly in Italy region\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: July 2025"]}, {"cell_type": "code", "execution_count": 21, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["# Parameters (Papermill)\n", "ground_method = \"ransac_pmf\"  # Ground segmentation method\n", "site_name = \"trino_enel\"\n", "save_results = True\n", "quality_sample_size = 5000  # For quality assessment sampling"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== CORRECTED COORDINATE-ONLY ALIGNMENT ===\n", "Ground method: ransac_pmf\n", "Site: trino_enel\n", "Output directory: ../../../data/processed/coordinate_alignment_corrected\n", "Using metadata coordinates (corrected approach)\n"]}], "source": ["import numpy as np\n", "import pandas as pd\n", "import open3d as o3d\n", "import laspy\n", "from pathlib import Path\n", "import json\n", "from scipy.spatial import cKDTree\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Now import from shared package\n", "from shared.config import get_data_path, get_output_path, get_mlflow_tracking_uri, find_latest_file, get_processed_data_path\n", "\n", "# Setup paths using shared config\n", "output_dir = get_processed_data_path(site_name, \"coordinate_alignment\")\n", "\n", "print(\"=== CORRECTED COORDINATE-ONLY ALIGNMENT ===\")\n", "print(f\"Ground method: {ground_method}\")\n", "print(f\"Site: {site_name}\")\n", "print(f\"Output directory: {output_dir}\")\n", "print(f\"Using metadata coordinates (corrected approach)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Data Loading "]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading data with corrected approach...\n", "Drone file: ../../../data/processed/trino_enel/ground_segmentation/ransac_pmf/trino_enel_nonground.ply\n", "IFC metadata file: ../../../data/processed/trino_enel/ifc_metadata/GRE.EEC.S.00.IT.P.14353.00.265_enhanced_metadata.csv\n", "IFC point cloud file: ../../../data/processed/trino_enel/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply (for comparison)\n"]}], "source": ["# Define file paths using shared config\n", "ground_seg_path = get_processed_data_path(site_name, \"ground_segmentation\") / ground_method\n", "ifc_metadata_path = get_processed_data_path(site_name, \"ifc_metadata\")\n", "ifc_pointcloud_path = get_processed_data_path(site_name, \"ifc_pointclouds\")\n", "\n", "# Find the actual files\n", "drone_file = find_latest_file(ground_seg_path, f\"{site_name}_nonground.ply\")\n", "ifc_metadata_file = find_latest_file(ifc_metadata_path, \"*enhanced_metadata.csv\")\n", "ifc_pointcloud_file = find_latest_file(ifc_pointcloud_path, \"*data_driven.ply\")  # For comparison\n", "\n", "print(\"Loading data with corrected approach...\")\n", "print(f\"Drone file: {drone_file}\")\n", "print(f\"IFC metadata file: {ifc_metadata_file}\")\n", "print(f\"IFC point cloud file: {ifc_pointcloud_file} (for comparison)\")\n", "\n"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded drone scan: 517,002 points\n"]}], "source": ["def load_drone_points(drone_path):\n", "    \"\"\"Load drone point cloud\"\"\"\n", "    drone_file = Path(drone_path)\n", "    \n", "    if not drone_file.exists():\n", "        raise FileNotFoundError(f\"Drone file not found: {drone_path}\")\n", "    \n", "    if drone_file.suffix.lower() == \".las\":\n", "        drone_las = laspy.read(drone_file)\n", "        drone_points = drone_las.xyz\n", "    elif drone_file.suffix.lower() == \".ply\":\n", "        drone_pcd = o3d.io.read_point_cloud(str(drone_file))\n", "        drone_points = np.asarray(drone_pcd.points)\n", "    else:\n", "        raise ValueError(\"Unsupported drone file format. Use .las or .ply\")\n", "    \n", "    print(f\"Loaded drone scan: {drone_points.shape[0]:,} points\")\n", "    return drone_points\n", "\n", "# Load data using corrected approach\n", "drone_points = load_drone_points(drone_file)"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== USING METADATA COORDINATES (CORRECTED APPROACH) ===\n", "Loaded IFC metadata: 14,460 records\n", "Valid IFC coordinates: 14,460 points\n", "Coordinate ranges:\n", "  X: 435267.20 to 436719.95\n", "  Y: 5010900.71 to 5012462.41\n", "  Z: 154.99 to 159.52\n"]}], "source": ["def load_ifc_points_from_metadata(metadata_csv_path):\n", "    \"\"\"Load IFC coordinates from metadata CSV (corrected approach)\"\"\"\n", "    metadata_file = Path(metadata_csv_path)\n", "    \n", "    if not metadata_file.exists():\n", "        raise FileNotFoundError(f\"IFC metadata file not found: {metadata_csv_path}\")\n", "    \n", "    # Load metadata\n", "    df = pd.read_csv(metadata_file)\n", "    print(f\"Loaded IFC metadata: {len(df):,} records\")\n", "    \n", "    # Extract coordinates\n", "    coord_cols = ['X', 'Y', 'Z']\n", "    if not all(col in df.columns for col in coord_cols):\n", "        raise ValueError(f\"Missing coordinate columns. Found: {list(df.columns)}\")\n", "    \n", "    # Get valid coordinates\n", "    valid_coords = df[coord_cols].dropna()\n", "    ifc_points = valid_coords.values\n", "    \n", "    print(f\"Valid IFC coordinates: {len(ifc_points):,} points\")\n", "    print(f\"Coordinate ranges:\")\n", "    for i, col in enumerate(coord_cols):\n", "        print(f\"  {col}: {ifc_points[:, i].min():.2f} to {ifc_points[:, i].max():.2f}\")\n", "    \n", "    return ifc_points\n", "\n", "print(f\"\\n=== USING METADATA COORDINATES (CORRECTED APPROACH) ===\")\n", "ifc_points = load_ifc_points_from_metadata(ifc_metadata_file)\n"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== POINT CLOUD COMPARISON (VERIFICATION) ===\n", "Loaded IFC point cloud: 5,480,340 points (for comparison)\n"]}], "source": ["def load_ifc_points_from_pointcloud(ifc_ply_path):\n", "    \"\"\"Load IFC point cloud (for comparison)\"\"\"\n", "    ifc_file = Path(ifc_ply_path)\n", "    \n", "    if not ifc_file.exists():\n", "        print(f\"Warning: IFC point cloud file not found: {ifc_ply_path}\")\n", "        return None\n", "    \n", "    ifc_pcd = o3d.io.read_point_cloud(str(ifc_file))\n", "    ifc_points = np.asarray(ifc_pcd.points)\n", "    \n", "    print(f\"Loaded IFC point cloud: {ifc_points.shape[0]:,} points (for comparison)\")\n", "    return ifc_points\n", "\n", "print(f\"\\n=== POINT CLOUD COMPARISON (VERIFICATION) ===\")\n", "ifc_points_pointcloud = load_ifc_points_from_pointcloud(ifc_pointcloud_file)\n", "\n"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== CRS VALIDATION ===\n", "Drone CRS: Not Found\n", "IFC CRS:   32632\n", "Warning: CRS metadata missing. Proceeding with assumption of matching coordinate systems.\n", "\n", "Ready for alignment with 517,002 drone + 14,460 IFC points\n"]}], "source": ["# Validate CRS compatibility\n", "def load_crs_metadata(pcd_file):\n", "    \"\"\"Load CRS metadata with multiple fallback strategies\"\"\"\n", "    stem = pcd_file.stem\n", "    base_stem = stem.split(\"_data_driven\")[0] if \"_data_driven\" in stem else stem\n", "    \n", "    candidates = [\n", "        pcd_file.with_name(f\"{stem}_crs.json\"),\n", "        pcd_file.with_name(f\"{base_stem}_crs.json\"),\n", "        pcd_file.parent / \"crs_metadata.json\",\n", "        pcd_file.parent / f\"{base_stem}_crs_metadata.json\"\n", "    ]\n", "    \n", "    for crs_file in candidates:\n", "        if crs_file.exists():\n", "            try:\n", "                with open(crs_file) as f:\n", "                    return json.load(f)\n", "            except json.JSONDecodeError:\n", "                print(f\"Warning: Failed to parse CRS metadata in {crs_file}\")\n", "    return None\n", "print(\"\\n=== CRS VALIDATION ===\")\n", "drone_crs = load_crs_metadata(Path(drone_file))\n", "ifc_crs = load_crs_metadata(Path(ifc_pointcloud_file))\n", "\n", "drone_epsg = drone_crs.get('epsg') if drone_crs else None\n", "ifc_epsg = ifc_crs.get('epsg') if ifc_crs else None\n", "\n", "print(f\"Drone CRS: {drone_epsg or 'Not Found'}\")\n", "print(f\"IFC CRS:   {ifc_epsg or 'Not Found'}\")\n", "\n", "if drone_epsg and ifc_epsg:\n", "    if drone_epsg != ifc_epsg:\n", "        raise ValueError(f\"CRS Mismatch: Drone EPSG {drone_epsg} ≠ IFC EPSG {ifc_epsg}\")\n", "    print(\"CRS match confirmed\")\n", "else:\n", "    print(\"Warning: CRS metadata missing. Proceeding with assumption of matching coordinate systems.\")\n", "\n", "print(f\"\\nReady for alignment with {len(drone_points):,} drone + {len(ifc_points):,} IFC points\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2.Data Analysis"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Metadata center: [435986.35, 5011746.88, 157.33]\n", "Point cloud center: [435986.35, 5011746.88, 157.33]\n", "Center difference: 0.00m\n", "Centers are similar - both approaches would work\n", "\n", "Data loading complete:\n", "  Drone points: 517,002\n", "  IFC points (from metadata): 14,460\n"]}], "source": ["\n", "try:\n", "    if ifc_points_pointcloud is not None:\n", "        # Compare centers\n", "        metadata_center = np.mean(ifc_points, axis=0)\n", "        pointcloud_center = np.mean(ifc_points_pointcloud, axis=0)\n", "        center_diff = np.linalg.norm(metadata_center - pointcloud_center)\n", "        \n", "        print(f\"Metadata center: [{metadata_center[0]:.2f}, {metadata_center[1]:.2f}, {metadata_center[2]:.2f}]\")\n", "        print(f\"Point cloud center: [{pointcloud_center[0]:.2f}, {pointcloud_center[1]:.2f}, {pointcloud_center[2]:.2f}]\")\n", "        print(f\"Center difference: {center_diff:.2f}m\")\n", "        \n", "        if center_diff > 100:\n", "            print(f\"Large difference detected - using metadata coordinates\")\n", "        else:\n", "            print(f\"Centers are similar - both approaches would work\")\n", "            \n", "except Exception as e:\n", "    print(f\"Point cloud comparison failed: {e}\")\n", "    print(f\"Proceeding with metadata coordinates only\")\n", "\n", "print(f\"\\nData loading complete:\")\n", "print(f\"  Drone points: {len(drone_points):,}\")\n", "print(f\"  IFC points (from metadata): {len(ifc_points):,}\")"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Point Cloud Analysis:\n", "  Drone center: [4.3603108e+05 5.0117454e+06 1.9000000e+00]\n", "  IFC center:   [4.35986350e+05 5.01174688e+06 1.57330000e+02]\n", "  Required offset: [-44.73   1.48 155.44]\n", "  Total separation: 161.75m\n", "\n", "Required coordinate offset: [-44.73, 1.48, 155.44]\n", "  → X Offset: -44.73 m\n", "  → Y Offset: 1.48 m\n", "  → Z Offset: 155.44 m\n"]}], "source": ["def analyze_alignment(drone_pts, ifc_pts):\n", "    drone_center = np.mean(drone_pts, axis=0)\n", "    ifc_center = np.mean(ifc_pts, axis=0)\n", "    offset = ifc_center - drone_center\n", "    \n", "    print(\"Point Cloud Analysis:\")\n", "    print(f\"  Drone center: {np.round(drone_center, 2)}\")\n", "    print(f\"  IFC center:   {np.round(ifc_center, 2)}\")\n", "    print(f\"  Required offset: {np.round(offset, 2)}\")\n", "    print(f\"  Total separation: {np.linalg.norm(offset):.2f}m\")\n", "    \n", "    return offset\n", "\n", "offset_vector = analyze_alignment(drone_points, ifc_points)\n", "print(f\"\\nRequired coordinate offset: [{offset_vector[0]:.2f}, {offset_vector[1]:.2f}, {offset_vector[2]:.2f}]\")\n", "print(f\"  → X Offset: {offset_vector[0]:.2f} m\")\n", "print(f\"  → Y Offset: {offset_vector[1]:.2f} m\")\n", "print(f\"  → Z Offset: {offset_vector[2]:.2f} m\")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Apply Alignment\n"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Alignment Results:\n", "  Source center after alignment: [4.35986350e+05 5.01174688e+06 1.57330000e+02]\n", "  Target center (reference):     [4.35986350e+05 5.01174688e+06 1.57330000e+02]\n", "  Final centroid error: 0.000000m\n", "  Status: SUCCESS\n"]}], "source": ["def apply_coordinate_alignment(source_pts, offset, target_pts=None):\n", "    \"\"\"\n", "    Applies an offset to source_pts (e.g., IFC) and optionally compares to target_pts (e.g., drone)\n", "    \"\"\"\n", "    aligned_pts = source_pts + offset\n", "\n", "    if target_pts is not None:\n", "        aligned_center = np.mean(aligned_pts, axis=0)\n", "        target_center = np.mean(target_pts, axis=0)\n", "        final_error = np.linalg.norm(aligned_center - target_center)\n", "\n", "        print(\"Alignment Results:\")\n", "        print(f\"  Source center after alignment: {np.round(aligned_center, 2)}\")\n", "        print(f\"  Target center (reference):     {np.round(target_center, 2)}\")\n", "        print(f\"  Final centroid error: {final_error:.6f}m\")\n", "        print(f\"  Status: {'SUCCESS' if final_error < 0.1 else 'CHECK REQUIRED'}\")\n", "\n", "    return aligned_pts\n", "\n", "\n", "# Apply alignment\n", "drone_aligned = apply_coordinate_alignment(drone_points, offset_vector, target_pts=ifc_points)\n", "#ifc_aligned = apply_coordinate_alignment(ifc_points, -offset_vector, target_pts=drone_points)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Quality Assessment"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Quality Assessment:\n", "  RMSE: 27.92m\n", "  Median distance: 6.37m\n", "  Excellent (<0.5m): 0.1%\n", "  Good (<2.0m): 3.6%\n", "  Acceptable (<10.0m): 64.0%\n", "\n", "CRITICAL: RMSE 27.9m indicates severe alignment failure\n", "   Possible causes: coordinate system mismatch, rotation, scale issues\n"]}], "source": ["def assess_alignment_quality(drone_aligned, ifc_pts, sample_size=10000):\n", "    # Sample for performance\n", "    n_drone = min(sample_size, len(drone_aligned))\n", "    n_ifc = min(sample_size, len(ifc_pts))\n", "    \n", "    drone_sample = drone_aligned[np.random.choice(len(drone_aligned), n_drone, replace=False)]\n", "    ifc_sample = ifc_pts[np.random.choice(len(ifc_pts), n_ifc, replace=False)]\n", "    \n", "    # Nearest neighbor distances\n", "    tree = cKDTree(ifc_sample)\n", "    distances, _ = tree.query(drone_sample)\n", "    \n", "    # Calculate metrics\n", "    rmse = np.sqrt(np.mean(distances**2))\n", "    median_dist = np.median(distances)\n", "    \n", "    # Quality thresholds\n", "    excellent_pct = np.sum(distances < 0.5) / len(distances) * 100\n", "    good_pct = np.sum(distances < 2.0) / len(distances) * 100\n", "    acceptable_pct = np.sum(distances < 10.0) / len(distances) * 100\n", "    \n", "    print(\"Quality Assessment:\")\n", "    print(f\"  RMSE: {rmse:.2f}m\")\n", "    print(f\"  Median distance: {median_dist:.2f}m\")\n", "    print(f\"  Excellent (<0.5m): {excellent_pct:.1f}%\")\n", "    print(f\"  Good (<2.0m): {good_pct:.1f}%\")\n", "    print(f\"  Acceptable (<10.0m): {acceptable_pct:.1f}%\")\n", "    \n", "    # Add quality warnings\n", "    if rmse > 20.0:\n", "        print(f\"\\nCRITICAL: RMSE {rmse:.1f}m indicates severe alignment failure\")\n", "        print(f\"   Possible causes: coordinate system mismatch, rotation, scale issues\")\n", "    elif rmse > 10.0:\n", "        print(f\"\\nWARNING: RMSE {rmse:.1f}m shows significant alignment problems\")\n", "        print(f\"   Consider: rotation correction, scale adjustment, or different method\")\n", "    elif rmse > 5.0:\n", "        print(f\"\\CAUTION: RMSE {rmse:.1f}m indicates moderate alignment issues\")\n", "    \n", "    return {\n", "        'rmse': rmse,\n", "        'median_distance': median_dist,\n", "        'excellent_pct': excellent_pct,\n", "        'good_pct': good_pct,\n", "        'acceptable_pct': acceptable_pct,\n", "        'method': 'coordinate_only'\n", "    }\n", "\n", "#quality_results = assess_alignment_quality(drone_points, ifc_aligned)\n", "quality_results = assess_alignment_quality(drone_aligned, ifc_points)\n", "\n"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Saved heatmap-colored point cloud to: ../../../data/processed/coordinate_alignment_corrected/ransac_pmf/trino_enel_deviation_heatmap.ply\n"]}], "source": ["from scipy.spatial import cKDTree\n", "\n", "def compute_deviation_map(drone_pts, ifc_pts):\n", "    tree = cKDTree(ifc_pts)\n", "    distances, indices = tree.query(drone_pts)\n", "    matched_ifc_pts = ifc_pts[indices]\n", "    deviation_vectors = drone_pts - matched_ifc_pts  # Shape: (N, 3)\n", "    deviation_magnitude = np.linalg.norm(deviation_vectors, axis=1)\n", "    return deviation_vectors, deviation_magnitude, matched_ifc_pts\n", "\n", "def save_colored_deviation_pcd(points, errors, out_path, max_error=5.0):\n", "    pcd = o3d.geometry.PointCloud()\n", "    pcd.points = o3d.utility.Vector3dVector(points)\n", "    \n", "    # Normalize error to [0, 1]\n", "    norm_errors = np.clip(errors / max_error, 0, 1)\n", "    \n", "    # Map to red → green colormap\n", "    colors = np.stack([norm_errors, 1 - norm_errors, np.zeros_like(norm_errors)], axis=1)\n", "    pcd.colors = o3d.utility.Vector3dVector(colors)\n", "\n", "    o3d.io.write_point_cloud(str(out_path), pcd)\n", "    print(f\"Saved heatmap-colored point cloud to: {out_path}\")\n", "\n", "# Compute deviation\n", "# deviation_vectors, deviation_magnitude, matched_ifc_pts = compute_deviation_map(\n", "#     drone_points, ifc_aligned\n", "# )\n", "\n", "# Compute deviations between aligned drone points and IFC points\n", "deviation_vectors, deviation_magnitude, matched_ifc_pts = compute_deviation_map(drone_aligned, ifc_points)\n", "\n", "# Set output path using shared config\n", "output_path = output_dir / ground_method\n", "output_path.mkdir(parents=True, exist_ok=True)\n", "heatmap_path = output_path / f\"{site_name}_deviation_heatmap.ply\"\n", "\n", "# Save colored deviation point cloud (using aligned drone points)\n", "save_colored_deviation_pcd(drone_aligned, deviation_magnitude, heatmap_path)\n"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ICP fitness: 0.0649\n", "ICP RMSE: 1.4848\n"]}], "source": ["def run_icp_refinement(source_pts, target_pts, voxel_size=0.5):\n", "    \"\"\"\n", "    Run point-to-point ICP to refine alignment between source and target point clouds.\n", "    \"\"\"\n", "    source_pcd = o3d.geometry.PointCloud()\n", "    target_pcd = o3d.geometry.PointCloud()\n", "    source_pcd.points = o3d.utility.Vector3dVector(source_pts)\n", "    target_pcd.points = o3d.utility.Vector3dVector(target_pts)\n", "\n", "    # Downsample for faster ICP\n", "    source_down = source_pcd.voxel_down_sample(voxel_size)\n", "    target_down = target_pcd.voxel_down_sample(voxel_size)\n", "\n", "    # Rigid ICP\n", "    threshold = 2.0  # max distance to consider a correspondence\n", "    icp_result = o3d.pipelines.registration.registration_icp(\n", "        source_down, target_down, threshold, np.eye(4),\n", "        o3d.pipelines.registration.TransformationEstimationPointToPoint()\n", "    )\n", "    return icp_result\n", "\n", "\n", "#icp_result = run_icp_refinement(drone_points, ifc_aligned)\n", "icp_result = run_icp_refinement(drone_aligned, ifc_points)\n", "print(f\"ICP fitness: {icp_result.fitness:.4f}\")\n", "print(f\"ICP RMSE: {icp_result.inlier_rmse:.4f}\")\n"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Z deviation range: -3.51 to 5.17m\n"]}], "source": ["def compute_z_deviation(drone_pts, ifc_pts):\n", "    tree = cKDTree(drone_pts[:, :2])  # Only X, Y\n", "    distances, indices = tree.query(ifc_pts[:, :2])\n", "    \n", "    # IFC Z vs drone Z at nearest XY\n", "    ifc_z = ifc_pts[:, 2]\n", "    drone_z = drone_pts[indices, 2]\n", "    dz = drone_z - ifc_z  # Positive if panel above pile\n", "\n", "    return dz, ifc_pts, drone_pts[indices]\n", "\n", "#dz, ifc_matched, drone_matched = compute_z_deviation(drone_points, ifc_aligned)\n", "dz, ifc_matched, drone_matched = compute_z_deviation(drone_aligned, ifc_points)\n", "print(f\"Z deviation range: {np.min(dz):.2f} to {np.max(dz):.2f}m\")"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Applying Z offset: 155.44m\n", "Deviation report saved to: ../../../data/processed/trino_enel/z_deviation/trino_enel_z_deviation_report.csv\n"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "from scipy.spatial import cKDTree\n", "from pathlib import Path\n", "\n", "def classify_z_deviation(dz, xy_dists, nearby_counts):\n", "    labels = []\n", "    for d, dist, count in zip(dz, xy_dists, nearby_counts):\n", "        if np.isnan(d) or dist > 2.0 or count < 3:\n", "            labels.append(\"no_data\")  # Sparse or unmatched\n", "        elif d < -1.0:\n", "            labels.append(\"under_surface\")\n", "        elif d < 0.5:\n", "            labels.append(\"pile_top\")\n", "        elif d < 3.0:\n", "            labels.append(\"support\")\n", "        elif d < 6.0:\n", "            labels.append(\"panel\")\n", "        else:\n", "            labels.append(\"above_panel\")\n", "    return labels\n", "\n", "def compute_per_pile_deviation_csv(ifc_metadata_csv, drone_pts, output_csv_path):\n", "    # Load IFC metadata\n", "    df = pd.read_csv(ifc_metadata_csv)\n", "    coord_cols = ['X', 'Y', 'Z']\n", "    if not all(col in df.columns for col in coord_cols):\n", "        raise ValueError(\"IFC metadata missing X, Y, Z columns\")\n", "\n", "    ifc_pts = df[coord_cols].dropna().values\n", "\n", "    # --- Z OFFSET CORRECTION ---\n", "    # Align drone Z (local) to IFC Z (global/sea-level)\n", "    drone_xy_centroid = np.mean(drone_pts[:, :2], axis=0)\n", "    ifc_xy_centroid = np.mean(ifc_pts[:, :2], axis=0)\n", "\n", "    # Find Z difference at centroid\n", "    drone_z_mean = np.mean(drone_pts[:, 2])\n", "    ifc_z_mean = np.mean(ifc_pts[:, 2])\n", "    z_offset = ifc_z_mean - drone_z_mean\n", "    print(f\"Applying Z offset: {z_offset:.2f}m\")\n", "\n", "    drone_pts[:, 2] += z_offset  # apply globally\n", "\n", "    tree = cKDTree(drone_pts[:, :2])  # Match by XY\n", "\n", "    # Nearest drone point within 2m\n", "    dists, indices = tree.query(ifc_pts[:, :2], distance_upper_bound=2.0)\n", "\n", "    # Assign drone Z or NaN if invalid index\n", "    drone_z = np.array([\n", "        drone_pts[i, 2] if (i < len(drone_pts) and not np.isinf(d)) else np.nan\n", "        for i, d in zip(indices, dists)\n", "    ])\n", "\n", "    # Z deviation\n", "    ifc_z = ifc_pts[:, 2]\n", "    dz = drone_z - ifc_z\n", "    dz_abs = np.abs(dz)\n", "\n", "    # Drone point density near each pile (1m radius)\n", "    nearby_counts = [len(tree.query_ball_point(pt[:2], r=1.0)) for pt in ifc_pts]\n", "\n", "    # Classify each pile deviation\n", "    dz_class = classify_z_deviation(dz, dists, nearby_counts)\n", "\n", "    # Compose output DataFrame\n", "    df_out = pd.DataFrame({\n", "        'Pile_ID': df['Pile_ID'] if 'Pile_ID' in df.columns else df.index,\n", "        'X': ifc_pts[:, 0],\n", "        'Y': ifc_pts[:, 1],\n", "        'IFC_Z': ifc_z,\n", "        'Drone_Z': drone_z,\n", "        'Z_Deviation': dz,\n", "        'Z_Deviation_Abs': dz_abs,\n", "        'XY_Distance': dists,\n", "        'Drone_Points_Nearby': nearby_counts,\n", "        'Deviation_Class': dz_class\n", "    })\n", "\n", "    # Save\n", "    df_out.to_csv(output_csv_path, index=False)\n", "    print(f\"Deviation report saved to: {output_csv_path}\")\n", "    return df_out\n", "\n", "# Run using shared config paths\n", "ifc_metadata_csv = str(ifc_metadata_file)\n", "z_deviation_path = get_processed_data_path(site_name, \"z_deviation\")\n", "z_deviation_path.mkdir(parents=True, exist_ok=True)\n", "output_csv = z_deviation_path / f\"{site_name}_z_deviation_report.csv\"\n", "\n", "report_df = compute_per_pile_deviation_csv(ifc_metadata_csv, drone_points, str(output_csv))\n"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"data": {"image/png": "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****************************************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**************************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***********************************************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**********************************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", "text/plain": ["<Figure size 1000x1000 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "\n", "def plot_deviation_map(df_out, title=\"Deviation Class Map\"):\n", "    plt.figure(figsize=(10, 10))\n", "    \n", "    class_colors = {\n", "        \"no_data\": 'gray',\n", "        \"under_surface\": 'brown',\n", "        \"pile_top\": 'green',\n", "        \"support\": 'orange',\n", "        \"panel\": 'blue',\n", "        \"above_panel\": 'red'\n", "    }\n", "\n", "    for cls, color in class_colors.items():\n", "        subset = df_out[df_out['Deviation_Class'] == cls]\n", "        plt.scatter(subset['X'], subset['Y'], s=5, label=cls, c=color, alpha=0.6)\n", "\n", "    plt.title(title)\n", "    plt.xlabel(\"X (UTM)\")\n", "    plt.ylabel(\"Y (UTM)\")\n", "    plt.axis('equal')\n", "    plt.legend(markerscale=3, fontsize=9, loc='upper right')\n", "    plt.grid(True)\n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "# Call after computing `df_out`\n", "plot_deviation_map(report_df)\n"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                 Count  <PERSON><PERSON>\n", "Deviation_Class                \n", "no_data          12504    86.47\n", "support            821     5.68\n", "pile_top           601     4.16\n", "under_surface      526     3.64\n", "panel                8     0.06\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>count</th>\n", "      <th>mean</th>\n", "      <th>std</th>\n", "      <th>min</th>\n", "      <th>25%</th>\n", "      <th>50%</th>\n", "      <th>75%</th>\n", "      <th>max</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Deviation_Class</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>pile_top</th>\n", "      <td>601.0</td>\n", "      <td>-0.058984</td>\n", "      <td>0.384659</td>\n", "      <td>-0.989828</td>\n", "      <td>-0.284828</td>\n", "      <td>-0.000828</td>\n", "      <td>0.252172</td>\n", "      <td>0.499172</td>\n", "    </tr>\n", "    <tr>\n", "      <th>support</th>\n", "      <td>821.0</td>\n", "      <td>1.129303</td>\n", "      <td>0.428542</td>\n", "      <td>0.500172</td>\n", "      <td>0.725172</td>\n", "      <td>1.112172</td>\n", "      <td>1.439172</td>\n", "      <td>2.995172</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                 count      mean       std       min       25%       50%  \\\n", "Deviation_Class                                                            \n", "pile_top         601.0 -0.058984  0.384659 -0.989828 -0.284828 -0.000828   \n", "support          821.0  1.129303  0.428542  0.500172  0.725172  1.112172   \n", "\n", "                      75%       max  \n", "Deviation_Class                      \n", "pile_top         0.252172  0.499172  \n", "support          1.439172  2.995172  "]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["# Assuming `report_df` is your output DataFrame\n", "summary = (\n", "    report_df['Deviation_Class']\n", "    .value_counts(normalize=True)\n", "    .mul(100)\n", "    .round(2)\n", "    .rename('Percent')\n", "    .to_frame()\n", ")\n", "\n", "summary['Count'] = report_df['Deviation_Class'].value_counts()\n", "summary = summary[['Count', 'Percent']]\n", "print(summary)\n", "\n", "\n", "# For example, to get all piles classified as 'pile_top' or 'support'\n", "mask = report_df[\"Deviation_Class\"].isin([\"pile_top\", \"support\"])\n", "detected_piles = report_df[mask].copy()\n", "\n", "# Estimated pile height (if IFC_Z is pile bottom)\n", "detected_piles[\"Estimated_Height\"] = detected_piles[\"Drone_Z\"] - detected_piles[\"IFC_Z\"]\n", "\n", "detected_piles.groupby(\"Deviation_Class\")[\"Estimated_Height\"].describe()\n"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [], "source": ["import open3d as o3d\n", "\n", "# Convert drone point cloud to Open3D and sample for speed\n", "drone_o3d = o3d.geometry.PointCloud()\n", "drone_o3d.points = o3d.utility.Vector3dVector(drone_points)\n", "\n", "# Convert IFC pile points\n", "ifc_xyz = report_df[['X', 'Y', 'IFC_Z']].values\n", "ifc_pcd = o3d.geometry.PointCloud()\n", "ifc_pcd.points = o3d.utility.Vector3dVector(ifc_xyz)\n", "ifc_pcd.paint_uniform_color([1, 0, 0])  # Red for IFC\n", "\n", "# Visualize\n", "o3d.visualization.draw_geometries([drone_o3d.voxel_down_sample(0.5), ifc_pcd])\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Save Results"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Results saved to: ../../../data/processed/coordinate_alignment_corrected/ransac_pmf\n", "=== COORDINATE-ONLY ALIGNMENT COMPLETE ===\n", "Final RMSE: 27.92m\n", "Method: Robust and reliable for construction monitoring\n"]}], "source": ["if save_results:\n", "    # Use shared config for output path\n", "    final_output_path = output_dir / ground_method\n", "    final_output_path.mkdir(parents=True, exist_ok=True)\n", "    \n", "    # Save aligned point cloud\n", "    aligned_pcd = o3d.geometry.PointCloud()\n", "    aligned_pcd.points = o3d.utility.Vector3dVector(drone_aligned)\n", "    aligned_file = final_output_path / f\"{site_name}_drone_aligned_to_ifc.ply\"\n", "\n", "    o3d.io.write_point_cloud(str(aligned_file), aligned_pcd)\n", "    \n", "    # Save transformation parameters\n", "    transform_data = {\n", "        'offset_vector': offset_vector.tolist(),\n", "        'quality_metrics': quality_results,\n", "        'method': 'coordinate_only_alignment',\n", "        'ground_segmentation_method': ground_method,\n", "        'site_name': site_name\n", "    }\n", "    \n", "    transform_file = final_output_path / f\"{site_name}_coordinate_transform.json\"\n", "    with open(transform_file, 'w') as f:\n", "        json.dump(transform_data, f, indent=2)\n", "    \n", "    print(f\"Results saved to: {final_output_path}\")\n", "    print(f\"  - Aligned point cloud: {aligned_file.name}\")\n", "    print(f\"  - Transform parameters: {transform_file.name}\")\n", "\n", "print(\"=== COORDINATE-ONLY ALIGNMENT COMPLETE ===\")\n", "print(f\"Final RMSE: {quality_results['rmse']:.2f}m\")\n", "print(f\"Method: Robust and reliable for construction monitoring\")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Summary\n", "\n", "**Coordinate-only alignment provides:**\n", "- **Robust performance**: No geometric correspondence required\n", "- **Predictable results**: Based on coordinate system transformation\n", "- **Suitable for construction**: Works with mismatched geometries\n", "- **Fast execution**: No iterative optimization needed\n", "\n", "**Use when:**\n", "- Aligning construction site data with BIM models\n", "- Working with fundamentally different geometric structures\n", "- Need reliable, reproducible results\n", "- Focus on regional/statistical analysis rather than point-level precision"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}