{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# IFC Coordinate System Verification\n", "\n", "This notebook verifies that the IFC to point cloud conversion process maintains correct coordinate systems and doesn't introduce errors.\n", "\n", "**Key Checks:**\n", "- IFC file coordinate reference system (CRS)\n", "- ifcopenshell geometry extraction settings\n", "- Coordinate transformations applied\n", "- Comparison with original IFC coordinates\n", "- Validation against known reference points"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [], "source": ["import ifcopenshell\n", "import ifcopenshell.geom\n", "import numpy as np\n", "import pandas as pd\n", "import open3d as o3d\n", "from pathlib import Path\n", "from pyproj import CRS\n"]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["total 11760\n", "-rw-r--r--@ 1 <USER>  <GROUP>   1.7M Jul 14 19:11 GRE.EEC.S.00.IT.P.14353.00.265_coordinates.csv\n", "-rw-r--r--@ 1 <USER>  <GROUP>   2.0M Jul 14 19:11 GRE.EEC.S.00.IT.P.14353.00.265_enhanced_metadata.csv\n", "-rw-r--r--@ 1 <USER>  <GROUP>   2.0M Jul 14 19:11 GRE.EEC.S.00.IT.P.14353.00.265_piles.csv\n", "-rw-r--r--@ 1 <USER>  <GROUP>   642B Jul 14 19:11 GRE.EEC.S.00.IT.P.14353.00.265_summary.json\n"]}], "source": ["!ls -lh ../../../data/processed/trino_enel/ifc_metadata/\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 1: Load and Inspect IFC File"]}, {"cell_type": "code", "execution_count": 57, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["IFC COORDINATE SYSTEM VERIFICATION\n", "Files to check:\n", "  IFC: GRE.EEC.S.00.IT.P.14353.00.265.ifc\n", "  Point cloud: GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply\n", "  Enhanced metadata: GRE.EEC.S.00.IT.P.14353.00.265_enhanced_metadata.csv\n", "  Coordinates: GRE.EEC.S.00.IT.P.14353.00.265_coordinates.csv\n", "  Piles: GRE.EEC.S.00.IT.P.14353.00.265_piles.csv\n"]}], "source": ["# File paths - updated to use the correct metadata files\n", "ifc_file_path = \"../../../data/raw/trino_enel/ifc/GRE.EEC.S.00.IT.P.14353.00.265.ifc\"\n", "pointcloud_path = \"../../../data/processed/trino_enel/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply\"\n", "metadata_path = \"../../../data/processed/trino_enel/ifc_metadata/GRE.EEC.S.00.IT.P.14353.00.265_enhanced_metadata.csv\"\n", "coordinates_path = \"../../../data/processed/trino_enel/ifc_metadata/GRE.EEC.S.00.IT.P.14353.00.265_coordinates.csv\"\n", "piles_path = \"../../../data/processed/trino_enel/ifc_metadata/GRE.EEC.S.00.IT.P.14353.00.265_piles.csv\"\n", "\n", "print(f\"IFC COORDINATE SYSTEM VERIFICATION\")\n", "print(f\"Files to check:\")\n", "print(f\"  IFC: {Path(ifc_file_path).name}\")\n", "print(f\"  Point cloud: {Path(pointcloud_path).name}\")\n", "print(f\"  Enhanced metadata: {Path(metadata_path).name}\")\n", "print(f\"  Coordinates: {Path(coordinates_path).name}\")\n", "print(f\"  Piles: {Path(piles_path).name}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-15 16:58:01,024 - INFO - <PERSON>NO<PERSON>ING PIPELINE COMPLETED SUCCESSFULLY\n", "2025-07-15 16:58:01,025 - INFO - ==================================================\n", "2025-07-15 16:58:01,025 - INFO - Site: trino_enel\n", "2025-07-15 16:58:01,025 - INFO - Input: 1,000,000 points\n", "2025-07-15 16:58:01,026 - INFO - Output: 983,852 points\n", "2025-07-15 16:58:01,026 - INFO - Removed: 16,148 points (1.6%)\n", "2025-07-15 16:58:01,026 - INFO - Processing time: 1.6 seconds\n", "2025-07-15 16:58:01,027 - INFO - Registration ready: YES\n", "2025-07-15 16:58:01,027 - INFO - Output file: ../../../data/processed/trino_enel/denoising/trino_enel_denoised_for_registration.ply\n", "2025-07-15 16:58:01,027 - INFO - Completed: 2025-07-15 16:58:01\n", "2025-07-15 16:58:01,028 - INFO - ==================================================\n"]}], "source": []}, {"cell_type": "code", "execution_count": 46, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading IFC file: GRE.EEC.S.00.IT.P.14353.00.265.ifc\n"]}], "source": ["print(f\"Loading IFC file: {Path(ifc_file_path).name}\")\n", "ifc_file = ifcopenshell.open(ifc_file_path)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 2: Coordinate Reference System Verification"]}, {"cell_type": "code", "execution_count": 47, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["No IfcProjectedCRS found\n", "No IfcGeographicCRS found\n", "No IfcMapConversion found\n"]}], "source": ["projected_crs = safe_by_type(ifc_file, \"IfcProjectedCRS\")\n", "geographic_crs = safe_by_type(ifc_file, \"IfcGeographicCRS\")\n", "map_conversion = safe_by_type(ifc_file, \"IfcMapConversion\")\n", "\n", "if projected_crs:\n", "    for crs in projected_crs:\n", "        print(f\"Projected CRS: {crs.Name}\")\n", "else:\n", "    print(\"No IfcProjectedCRS found\")\n", "\n", "if geographic_crs:\n", "    for crs in geographic_crs:\n", "        print(f\"Geographic CRS: {crs.Name}\")\n", "else:\n", "    print(\"No IfcGeographicCRS found\")\n", "\n", "if map_conversion:\n", "    for conv in map_conversion:\n", "        print(\"Map Conversion found.\")\n", "else:\n", "    print(\"No IfcMapConversion found\")"]}, {"cell_type": "code", "execution_count": 48, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Schema: IFC4\n", "File size: 187.0 MB\n"]}], "source": ["def safe_by_type(ifc_file, entity_name):\n", "    try:\n", "        return ifc_file.by_type(entity_name)\n", "    except RuntimeError:\n", "        return []\n", "\n", "# --- Basic Info ---\n", "print(f\"Schema: {ifc_file.schema}\")\n", "print(f\"File size: {Path(ifc_file_path).stat().st_size / (1024*1024):.1f} MB\")\n"]}, {"cell_type": "code", "execution_count": 49, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Site Name: Surface:840048\n", "Latitude: (42, 24, 53, 508911)\n", "Longitude: (-71, -15, -29, -58837)\n", "Elevation: 0.0\n", "Inferred CRS: EPSG:32619 (EPSG:32619)\n"]}], "source": ["# --- Site Info ---\n", "sites = safe_by_type(ifc_file, \"IfcSite\")\n", "if sites:\n", "    site = sites[0]\n", "    print(f\"Site Name: {getattr(site, 'Name', 'N/A')}\")\n", "    print(f\"Latitude: {getattr(site, 'RefLatitude', 'N/A')}\")\n", "    print(f\"Longitude: {getattr(site, 'RefLongitude', 'N/A')}\")\n", "    print(f\"Elevation: {getattr(site, 'RefElevation', 'N/A')}\")\n", "\n", "    def dms_to_dd(dms):\n", "        deg, minutes, seconds, millionths = dms\n", "        sign = -1 if deg < 0 else 1\n", "        return sign * (abs(deg) + abs(minutes) / 60 + (abs(seconds) + abs(millionths) / 1e6) / 3600)\n", "\n", "    latitude_dd = dms_to_dd(site.RefLatitude)\n", "    longitude_dd = dms_to_dd(site.RefLongitude)\n", "\n", "    def infer_epsg_from_latlon(lat, lon):\n", "        zone = int((lon + 180) / 6) + 1\n", "        return 32600 + zone if lat >= 0 else 32700 + zone\n", "\n", "    epsg_code = infer_epsg_from_latlon(latitude_dd, longitude_dd)\n", "    crs = CRS.from_epsg(epsg_code)\n", "\n", "    print(f\"Inferred CRS: EPSG:{epsg_code} ({crs.to_string()})\")\n", "\n", "else:\n", "    print(\"No IfcSite found\")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 3: Verify Geometry Extraction Settings"]}, {"cell_type": "code", "execution_count": 50, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Verifying geometry extraction...\n", "Average offset between world and local: [4.35751684e+05 5.01218744e+06 1.60830786e+02]\n"]}], "source": ["# --- Geometry Verification ---\n", "print(\"\\nVerifying geometry extraction...\")\n", "\n", "settings = ifcopenshell.geom.settings()\n", "settings.set(settings.USE_WORLD_COORDS, True)\n", "settings.set(settings.WELD_VERTICES, True)\n", "\n", "element_types = ['IfcPile', 'IfcColumn', 'IfcBeam']\n", "test_elements = []\n", "for etype in element_types:\n", "    elements = ifc_file.by_type(etype)\n", "    test_elements.extend(elements[:3])\n", "\n", "coordinates_world = []\n", "coordinates_local = []\n", "\n", "settings_world = ifcopenshell.geom.settings()\n", "settings_world.set(settings_world.USE_WORLD_COORDS, True)\n", "\n", "settings_local = ifcopenshell.geom.settings()\n", "settings_local.set(settings_local.USE_WORLD_COORDS, False)\n", "\n", "for i, element in enumerate(test_elements[:5]):\n", "    try:\n", "        shape_world = ifcopenshell.geom.create_shape(settings_world, element)\n", "        shape_local = ifcopenshell.geom.create_shape(settings_local, element)\n", "\n", "        vertices_world = np.array(shape_world.geometry.verts).reshape(-1, 3)\n", "        vertices_local = np.array(shape_local.geometry.verts).reshape(-1, 3)\n", "\n", "        centroid_world = np.mean(vertices_world, axis=0)\n", "        centroid_local = np.mean(vertices_local, axis=0)\n", "\n", "        coordinates_world.append(centroid_world)\n", "        coordinates_local.append(centroid_local)\n", "\n", "    except Exception as e:\n", "        print(f\"Element {i+1} geometry extraction failed: {e}\")\n", "\n", "if coordinates_world and coordinates_local:\n", "    coords_world = np.array(coordinates_world)\n", "    coords_local = np.array(coordinates_local)\n", "    offset = np.mean(coords_world - coords_local, axis=0)\n", "    print(f\"Average offset between world and local: {offset}\")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 4: Point Cloud Comparison"]}, {"cell_type": "code", "execution_count": 51, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Point cloud center: [4.35986347e+05 5.01174688e+06 1.57335489e+02]\n", "IFC center: [4.35751684e+05 5.01218744e+06 1.58688286e+02]\n", "Center difference: 499.17 meters\n"]}], "source": ["# --- Point Cloud Comparison ---\n", "if Path(pointcloud_path).exists():\n", "    pcd = o3d.io.read_point_cloud(pointcloud_path)\n", "    pc_points = np.asarray(pcd.points)\n", "    pc_center = np.mean(pc_points, axis=0)\n", "    print(f\"\\nPoint cloud center: {pc_center}\")\n", "\n", "    if coordinates_world:\n", "        ifc_center = np.mean(coordinates_world, axis=0)\n", "        center_diff = np.linalg.norm(pc_center - ifc_center)\n", "        print(f\"IFC center: {ifc_center}\")\n", "        print(f\"Center difference: {center_diff:.2f} meters\")\n", "else:\n", "    print(\"Point cloud file not found.\")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 5: <PERSON><PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": 52, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Metadata center: [4.35986348e+05 5.01174688e+06 1.57334117e+02]\n", "Metadata vs point cloud center difference: 0.00 meters\n", "Geo center: 45.256307, 8.184157\n"]}], "source": ["if Path(metadata_path).exists():\n", "    metadata_df = pd.read_csv(metadata_path)\n", "    coord_cols = [col for col in ['X', 'Y', 'Z'] if col in metadata_df.columns]\n", "    if coord_cols:\n", "        valid_coords = metadata_df[coord_cols].dropna()\n", "        metadata_center = valid_coords.mean().values\n", "        print(f\"\\nMetadata center: {metadata_center}\")\n", "\n", "        if 'pc_center' in locals():\n", "            diff = np.linalg.norm(metadata_center - pc_center)\n", "            print(f\"Metadata vs point cloud center difference: {diff:.2f} meters\")\n", "\n", "        if 'Latitude' in metadata_df.columns and 'Longitude' in metadata_df.columns:\n", "            geo_coords = metadata_df[['Latitude', 'Longitude']].dropna()\n", "            lat_center = geo_coords['Latitude'].mean()\n", "            lon_center = geo_coords['Longitude'].mean()\n", "            print(f\"Geo center: {lat_center:.6f}, {lon_center:.6f}\")\n", "else:\n", "    print(\"Metadata file not found.\")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 6: <PERSON><PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": 53, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Metadata center: [4.35986348e+05 5.01174688e+06 1.57334117e+02]\n", "Metadata vs point cloud center difference: 0.00 meters\n", "Geo center: 45.256307, 8.184157\n"]}], "source": ["if Path(metadata_path).exists():\n", "    metadata_df = pd.read_csv(metadata_path)\n", "    coord_cols = [col for col in ['X', 'Y', 'Z'] if col in metadata_df.columns]\n", "    if coord_cols:\n", "        valid_coords = metadata_df[coord_cols].dropna()\n", "        metadata_center = valid_coords.mean().values\n", "        print(f\"\\nMetadata center: {metadata_center}\")\n", "\n", "        if 'pc_center' in locals():\n", "            diff = np.linalg.norm(metadata_center - pc_center)\n", "            print(f\"Metadata vs point cloud center difference: {diff:.2f} meters\")\n", "\n", "        if 'Latitude' in metadata_df.columns and 'Longitude' in metadata_df.columns:\n", "            geo_coords = metadata_df[['Latitude', 'Longitude']].dropna()\n", "            lat_center = geo_coords['Latitude'].mean()\n", "            lon_center = geo_coords['Longitude'].mean()\n", "            print(f\"Geo center: {lat_center:.6f}, {lon_center:.6f}\")\n", "else:\n", "    print(\"Metadata file not found.\")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 7: Final Summary "]}, {"cell_type": "code", "execution_count": 54, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Summary:\n", "- No explicit CRS in IFC\n", "- Geometry extraction succeeded\n", "- IFC and point cloud center difference: 499.17 m\n", "- Metadata coordinates available\n", "Verification complete.\n"]}], "source": ["print(\"\\nSummary:\")\n", "if projected_crs or geographic_crs:\n", "    print(\"- CRS found in IFC\")\n", "else:\n", "    print(\"- No explicit CRS in IFC\")\n", "\n", "if coordinates_world:\n", "    print(\"- Geometry extraction succeeded\")\n", "\n", "if 'center_diff' in locals():\n", "    print(f\"- IFC and point cloud center difference: {center_diff:.2f} m\")\n", "\n", "if 'metadata_center' in locals():\n", "    print(\"- Metadata coordinates available\")\n", "\n", "print(\"Verification complete.\")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 6: Comprehensive Metadata Analysis"]}, {"cell_type": "code", "execution_count": 56, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "================================================================================\n", "COMPREHENSIVE METADATA ANALYSIS\n", "================================================================================\n", "\n", "ENHANCED METADATA:\n", "  File: GRE.EEC.S.00.IT.P.14353.00.265_enhanced_metadata.csv\n", "  Records: 14,460\n", "  Columns: ['GlobalId', 'Name', 'Type', 'Description', 'Tag', 'Site_Latitude', 'Site_Longitude', 'Site_Elevation', 'X', 'Y']...\n", "  Local coordinates: 14,460 valid records\n", "    X: 435267.20 to 436719.95 (range: 1452.75 m)\n", "    Y: 5010900.71 to 5012462.41 (range: 1561.70 m)\n", "    Z: 154.99 to 159.52 (range: 4.53 m)\n", "    Center: [435986.35, 5011746.88, 157.33]\n", "  Geographic coordinates: 14,460 valid records\n", "    Latitude: 45.249000 to 45.263000\n", "    Longitude: 8.175000 to 8.193000\n", "    Geographic center: 45.256307°N, 8.184157°E\n", "    Location: Within Italy region\n", "\n", "COORDINATES:\n", "  File: GRE.EEC.S.00.IT.P.14353.00.265_coordinates.csv\n", "  Records: 14,460\n", "  Columns: ['GlobalId', 'Name', 'Type', 'X', 'Y', 'Z', 'Latitude', 'Longitude']\n", "  Local coordinates: 14,460 valid records\n", "    X: 435267.20 to 436719.95 (range: 1452.75 m)\n", "    Y: 5010900.71 to 5012462.41 (range: 1561.70 m)\n", "    Z: 154.99 to 159.52 (range: 4.53 m)\n", "    Center: [435986.35, 5011746.88, 157.33]\n", "  Geographic coordinates: 14,460 valid records\n", "    Latitude: 45.249000 to 45.263000\n", "    Longitude: 8.175000 to 8.193000\n", "    Geographic center: 45.256307°N, 8.184157°E\n", "    Location: Within Italy region\n", "\n", "PILES:\n", "  File: GRE.EEC.S.00.IT.P.14353.00.265_piles.csv\n", "  Records: 14,460\n", "  Columns: ['GlobalId', 'Name', 'Type', 'Description', 'Tag', 'Site_Latitude', 'Site_Longitude', 'Site_Elevation', 'X', 'Y']...\n", "  Local coordinates: 14,460 valid records\n", "    X: 435267.20 to 436719.95 (range: 1452.75 m)\n", "    Y: 5010900.71 to 5012462.41 (range: 1561.70 m)\n", "    Z: 154.99 to 159.52 (range: 4.53 m)\n", "    Center: [435986.35, 5011746.88, 157.33]\n", "  Geographic coordinates: 14,460 valid records\n", "    Latitude: 45.249000 to 45.263000\n", "    Longitude: 8.175000 to 8.193000\n", "    Geographic center: 45.256307°N, 8.184157°E\n", "    Location: Within Italy region\n", "\n", "============================================================\n", "COORDINATE CONSISTENCY CHECK\n", "============================================================\n", "\n", "Coordinate centers:\n", "  Enhanced Metadata: [435986.35, 5011746.88, 157.33]\n", "  Coordinates: [435986.35, 5011746.88, 157.33]\n", "  Piles: [435986.35, 5011746.88, 157.33]\n", "  Difference between Enhanced Metadata and Coordinates: 0.00 m\n", "  Difference between <PERSON><PERSON><PERSON> and <PERSON><PERSON>: 0.00 m\n", "  Difference between Coordinates and Piles: 0.00 m\n", "\n", "Consistency status:\n", "  Result: Good (max difference: 0.00 m)\n", "\n", "================================================================================\n"]}], "source": ["print(\"\\n\" + \"=\"*80)\n", "print(\"COMPREHENSIVE METADATA ANALYSIS\")\n", "print(\"=\"*80)\n", "\n", "# Load all available metadata files\n", "metadata_files = {\n", "    'Enhanced Metadata': metadata_path,\n", "    'Coordinates': coordinates_path,\n", "    'Piles': piles_path\n", "}\n", "\n", "coordinate_summary = {}\n", "\n", "for name, path in metadata_files.items():\n", "    if Path(path).exists():\n", "        print(f\"\\n{name.upper()}:\")\n", "        df = pd.read_csv(path)\n", "        \n", "        print(f\"  File: {Path(path).name}\")\n", "        print(f\"  Records: {len(df):,}\")\n", "        print(f\"  Columns: {list(df.columns)[:10]}{'...' if len(df.columns) > 10 else ''}\")\n", "        \n", "        # Check coordinate columns\n", "        coord_cols = [col for col in ['X', 'Y', 'Z'] if col in df.columns]\n", "        geo_cols = [col for col in ['Latitude', 'Longitude'] if col in df.columns]\n", "        \n", "        if coord_cols:\n", "            valid_coords = df[coord_cols].dropna()\n", "            print(f\"  Local coordinates: {len(valid_coords):,} valid records\")\n", "            \n", "            if len(valid_coords) > 0:\n", "                coord_ranges = {}\n", "                for col in coord_cols:\n", "                    min_val, max_val = valid_coords[col].min(), valid_coords[col].max()\n", "                    coord_ranges[col] = (min_val, max_val)\n", "                    print(f\"    {col}: {min_val:.2f} to {max_val:.2f} (range: {max_val - min_val:.2f} m)\")\n", "                \n", "                center = valid_coords[coord_cols].mean().values\n", "                print(f\"    Center: [{center[0]:.2f}, {center[1]:.2f}, {center[2]:.2f}]\")\n", "                coordinate_summary[name] = {'center': center, 'ranges': coord_ranges, 'count': len(valid_coords)}\n", "        \n", "        if geo_cols:\n", "            valid_geo = df[geo_cols].dropna()\n", "            print(f\"  Geographic coordinates: {len(valid_geo):,} valid records\")\n", "            \n", "            if len(valid_geo) > 0:\n", "                for col in geo_cols:\n", "                    min_val, max_val = valid_geo[col].min(), valid_geo[col].max()\n", "                    print(f\"    {col}: {min_val:.6f} to {max_val:.6f}\")\n", "                \n", "                if 'Latitude' in valid_geo.columns and 'Longitude' in valid_geo.columns:\n", "                    lat_center = valid_geo['Latitude'].mean()\n", "                    lon_center = valid_geo['Longitude'].mean()\n", "                    print(f\"    Geographic center: {lat_center:.6f}°N, {lon_center:.6f}°E\")\n", "                    \n", "                    if 35 <= lat_center <= 47 and 6 <= lon_center <= 19:\n", "                        print(\"    Location: Within Italy region\")\n", "                    else:\n", "                        print(\"    Location: Outside expected Italy region\")\n", "        \n", "        if not coord_cols and not geo_cols:\n", "            print(\"  No coordinate columns found\")\n", "    else:\n", "        print(f\"\\n{name}: File not found\")\n", "\n", "# Coordinate consistency check\n", "print(f\"\\n\" + \"=\"*60)\n", "print(\"COORDINATE CONSISTENCY CHECK\")\n", "print(\"=\"*60)\n", "\n", "if len(coordinate_summary) >= 2:\n", "    centers = [data['center'] for data in coordinate_summary.values()]\n", "    names = list(coordinate_summary.keys())\n", "    \n", "    print(\"\\nCoordinate centers:\")\n", "    for name, center in zip(names, centers):\n", "        print(f\"  {name}: [{center[0]:.2f}, {center[1]:.2f}, {center[2]:.2f}]\")\n", "    \n", "    # Compute differences\n", "    max_diff = 0\n", "    for i in range(len(centers)):\n", "        for j in range(i + 1, len(centers)):\n", "            diff = np.linalg.norm(centers[i] - centers[j])\n", "            print(f\"  Difference between {names[i]} and {names[j]}: {diff:.2f} m\")\n", "            max_diff = max(max_diff, diff)\n", "    \n", "    print(\"\\nConsistency status:\")\n", "    if max_diff < 10.0:\n", "        print(f\"  Result: Good (max difference: {max_diff:.2f} m)\")\n", "    elif max_diff < 50.0:\n", "        print(f\"  Result: Moderate (max difference: {max_diff:.2f} m)\")\n", "    else:\n", "        print(f\"  Result: Poor (max difference: {max_diff:.2f} m)\")\n", "        print(\"  Suggestion: Possible coordinate system misalignment\")\n", "else:\n", "    print(\"Not enough metadata sources to perform consistency check\")\n", "\n", "print(\"\\n\" + \"=\"*80)\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}