{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# IFC to Point Cloud Conversion\n", "\n", "## Overview\n", "\n", "This notebook converts IFC geometry to point clouds using a data-driven mesh-based approach:\n", "- Extract 3D meshes from IFC elements\n", "- Convert meshes to dense point clouds\n", "- Apply coordinate transformations\n", "- Export in standard point cloud formats\n", "\n", "**Method**: ifcopenshell + mesh extraction for reliable geometry conversion\n", "\n", "**Output**: PLY point cloud files ready for alignment and analysis\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: July 2025"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["# Parameters\n", "ifc_file_path = \"../../../data/raw/trino_enel/ifc/GRE.EEC.S.00.IT.P.14353.00.265.ifc\"\n", "output_dir = \"../../../data/processed/trino_enel/ifc_pointclouds\"\n", "site_name = \"trino_enel\"\n", "\n", "# Point cloud generation parameters\n", "point_density = 0.1  # Point spacing in meters\n", "include_element_types = [\"IfcPile\", \"IfcBeam\", \"IfcColumn\", \"IfcSlab\"]\n", "min_element_size = 0.5  # Minimum element size to include (meters)\n", "max_points_per_element = 10000  # Limit points per element"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["IFC TO POINT CLOUD CONVERSION\n", "IFC file: GRE.EEC.S.00.IT.P.14353.00.265.ifc\n", "Output directory: ../../../data/processed/trino_enel/ifc_pointclouds\n", "Point density: 0.1m\n"]}], "source": ["import ifcopenshell\n", "import ifcopenshell.geom\n", "import numpy as np\n", "import open3d as o3d\n", "from pathlib import Path\n", "import json\n", "import logging\n", "from typing import List, Tuple, Optional\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Setup logging\n", "logging.basicConfig(level=logging.INFO, format='%(levelname)s - %(message)s')\n", "logger = logging.getLogger(__name__)\n", "\n", "# Setup output directory\n", "output_path = Path(output_dir)\n", "output_path.mkdir(parents=True, exist_ok=True)\n", "\n", "print(\"IFC TO POINT CLOUD CONVERSION\")\n", "print(f\"IFC file: {Path(ifc_file_path).name}\")\n", "print(f\"Output directory: {output_path}\")\n", "print(f\"Point density: {point_density}m\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Load IFC and Setup Geometry Processing"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["from pyproj import Transformer, CRS\n", "\n", "def dms_to_decimal(dms):\n", "    if not dms or len(dms) < 3:\n", "        return None\n", "    # Convert to list and ensure we have at least 4 values\n", "    dms_values = list(dms)\n", "    while len(dms_values) < 4:\n", "        dms_values.append(0)\n", "    \n", "    deg, minute, sec, micro = dms_values[:4]\n", "    sign = -1 if deg < 0 else 1\n", "    return sign * (abs(deg) + minute / 60 + sec / 3600 + micro / 3600 / 1e6)\n", "\n", "def get_utm_crs(lat, lon):\n", "    \"\"\"Get EPSG code for UTM zone based on latitude and longitude.\"\"\"\n", "    zone_number = int((lon + 180) / 6) + 1\n", "    if lat >= 0:\n", "        return CRS.from_epsg(32600 + zone_number)  # Northern hemisphere\n", "    else:\n", "        return CRS.from_epsg(32700 + zone_number)  # Southern hemisphere\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading IFC file...\n", "IFC Schema: IFC4\n", "  IfcPile: 0 elements\n", "  IfcBeam: 0 elements\n", "  IfcColumn: 14460 elements\n", "  IfcSlab: 0 elements\n", "\n", "Total elements to convert: 14460\n", "\n", "=== IFC SITE GEOLOCATION ===\n", "  Raw RefLatitude:  (42, 24, 53, 508911)\n", "  Raw RefLongitude: (-71, -15, -29, -58837)\n", "  Elevation:        0.0 m\n", "  Decimal Latitude: 42.41486359\n", "  Decimal Longitude:-70.74192810\n", "  Detected coordinates outside Italy — overriding to Milan.\n", "\n", "=== UTM Zone EPSG:   32633\n", "  UTM CRS:         EPSG:32633\n", "  Transformed Site Origin (IFC local → UTM):\n", "  X = 45808.29, Y = 5050962.24\n"]}], "source": ["# Load IFC file\n", "print(\"Loading IFC file...\")\n", "ifc_file = ifcopenshell.open(ifc_file_path)\n", "\n", "# Setup geometry settings for mesh extraction\n", "settings = ifcopenshell.geom.settings()\n", "settings.set(settings.USE_WORLD_COORDS, True)\n", "#settings.set(settings.USE_WORLD_COORDS, False)\n", "\n", "settings.set(settings.WELD_VERTICES, True)\n", "#settings.set(settings.SEW_SHELLS, True)\n", "\n", "print(f\"IFC Schema: {ifc_file.schema}\")\n", "\n", "# Count elements to process\n", "total_elements = 0\n", "for element_type in include_element_types:\n", "    elements = ifc_file.by_type(element_type)\n", "    total_elements += len(elements)\n", "    print(f\"  {element_type}: {len(elements)} elements\")\n", "\n", "print(f\"\\nTotal elements to convert: {total_elements}\")\n", "\n", "lat_dms = ifc_file.by_type(\"IfcSite\")[0].RefLatitude\n", "lon_dms = ifc_file.by_type(\"IfcSite\")[0].RefLongitude\n", "elev = ifc_file.by_type(\"IfcSite\")[0].RefElevation\n", "\n", "# --- Site Metadata Check ---\n", "site = ifc_file.by_type(\"IfcSite\")\n", "if site:\n", "    site = site[0]\n", "    lat = dms_to_decimal(lat_dms)\n", "    lon = dms_to_decimal(lon_dms)\n", "\n", "    print(\"\\n=== IFC SITE GEOLOCATION ===\")\n", "    print(f\"  Raw RefLatitude:  {lat_dms}\")\n", "    print(f\"  Raw RefLongitude: {lon_dms}\")\n", "    print(f\"  Elevation:        {elev} m\")\n", "\n", "    if lat is not None and lon is not None:\n", "        print(f\"  Decimal Latitude: {lat:.8f}\")\n", "        print(f\"  Decimal Longitude:{lon:.8f}\")\n", "\n", "        # Optional override if out of expected range for Italy\n", "        if not (7.0 <= lon <= 19.0 and 36.0 <= lat <= 47.5):\n", "            print(\"  Detected coordinates outside Italy — overriding to Milan.\")\n", "            lat, lon = 45.4642, 9.1900\n", "\n", "        # Compute UTM transformation\n", "        #source_crs = CRS.from_epsg(4326)\n", "        #target_crs = get_utm_crs(lat, lon)\n", "        \n", "        target_crs = CRS.from_epsg(32633)\n", "        source_crs = CRS.from_epsg(4326)\n", "\n", "        transformer = Transformer.from_crs(source_crs, target_crs, always_xy=True)\n", "        utm_x, utm_y = transformer.transform(lon, lat)\n", "\n", "        print(f\"\\n=== UTM Zone EPSG:   {target_crs.to_epsg()}\")\n", "        print(f\"  UTM CRS:         {target_crs.to_string()}\")\n", "        print(f\"  Transformed Site Origin (IFC local → UTM):\")\n", "        print(f\"  X = {utm_x:.2f}, Y = {utm_y:.2f}\")\n", "    else:\n", "        print(\"Incomplete geolocation metadata. Cannot compute UTM.\")\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# from pyproj import Transformer, CRS\n", "\n", "\n", "# # Choose target CRS: same as drone LAS (EPSG:32632) or appropriate UTM from lat/lon\n", "# target_crs = CRS.from_epsg(32632)  # Your drone's CRS\n", "# source_crs = CRS.from_epsg(4326)   # WGS84 from lat/lon\n", "\n", "# transformer = Transformer.from_crs(source_crs, target_crs, always_xy=True)\n", "\n", "# # Transform the site reference point to get the shift\n", "# utm_x, utm_y = transformer.transform(lon, lat)\n", "# print(f\"Transformed site origin (IFC local → UTM): X={utm_x:.2f}, Y={utm_y:.2f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. <PERSON><PERSON> to Point Cloud Conversion Functions"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["# ifc_pointcloud.py\n", "import ifcopenshell\n", "import ifcopenshell.geom\n", "import numpy as np\n", "import open3d as o3d\n", "import logging\n", "from tqdm import tqdm\n", "\n", "# Configure logging\n", "logger = logging.getLogger(__name__)\n", "\n", "def create_geometry_settings(ifc_file):\n", "    \"\"\"Create properly configured geometry settings with context.\"\"\"\n", "    settings = ifcopenshell.geom.settings()\n", "    \n", "    # Set basic settings that are commonly available\n", "    try:\n", "        settings.set(settings.USE_WORLD_COORDS, True)\n", "    except AttributeError:\n", "        pass\n", "    \n", "    try:\n", "        settings.set(settings.WELD_VERTICES, True)\n", "    except AttributeError:\n", "        pass\n", "    \n", "    try:\n", "        settings.set(settings.USE_MATERIAL_NAMES, True)\n", "    except AttributeError:\n", "        pass\n", "    \n", "    try:\n", "        settings.set(settings.APPLY_DEFAULT_MATERIALS, True)\n", "    except AttributeError:\n", "        pass\n", "    \n", "    # Try additional settings that might be available\n", "    try:\n", "        settings.set(settings.DISABLE_TRIANGULATION, False)\n", "    except AttributeError:\n", "        pass\n", "    \n", "    # Set up context - this is crucial for geometry processing\n", "    try:\n", "        # Find the first geometric representation context\n", "        contexts = ifc_file.by_type('IfcGeometricRepresentationContext')\n", "        if contexts:\n", "            # Use the first available context\n", "            context = contexts[0]\n", "            settings.set_context(context)\n", "            logger.info(f\"Using context: {context}\")\n", "        else:\n", "            logger.warning(\"No geometric representation context found\")\n", "    except Exception as e:\n", "        logger.error(f\"Error setting context: {e}\")\n", "    \n", "    return settings\n", "\n", "def extract_mesh_from_element(element, settings, include_normals=False, include_colors=False):\n", "    \"\"\"Extract mesh geometry, normals, and colors from IFC element.\"\"\"\n", "    try:\n", "        # Skip elements without representation\n", "        if not element.Representation:\n", "            return None, None, None, None\n", "            \n", "        shape = ifcopenshell.geom.create_shape(settings, element)\n", "        if shape and shape.geometry:\n", "            vertices = np.array(shape.geometry.verts).reshape(-1, 3)\n", "            faces = np.array(shape.geometry.faces).reshape(-1, 3)\n", "            \n", "            normals = None\n", "            colors = None\n", "            \n", "            if include_normals and hasattr(shape.geometry, 'normals'):\n", "                normals = np.array(shape.geometry.normals).reshape(-1, 3)\n", "            \n", "            if include_colors and hasattr(shape.geometry, 'materials'):\n", "                try:\n", "                    materials = shape.geometry.materials\n", "                    if materials:\n", "                        # Create colors array based on materials\n", "                        colors = np.array([m.diffuse for m in materials])\n", "                        if len(colors) > 0:\n", "                            # Repeat colors for each vertex\n", "                            colors = np.repeat(colors, len(vertices) // len(colors), axis=0)\n", "                            # Handle remainder vertices\n", "                            remainder = len(vertices) % len(colors)\n", "                            if remainder > 0:\n", "                                colors = np.vstack([colors, colors[:remainder]])\n", "                except Exception as e:\n", "                    logger.debug(f\"Could not extract colors for {element.GlobalId}: {e}\")\n", "                    colors = None\n", "            \n", "            if len(vertices) > 0 and len(faces) > 0:\n", "                return vertices, faces, normals, colors\n", "            \n", "        logger.debug(f\"No valid geometry for {element.GlobalId}\")\n", "        return None, None, None, None\n", "    \n", "    except Exception as e:\n", "        logger.debug(f\"Mesh extraction failed for {element.GlobalId}: {str(e)}\")\n", "        return None, None, None, None\n", "\n", "def mesh_to_pointcloud(vertices, faces, normals=None, colors=None, point_density=0.05, max_points=50000, sampling_method='uniform'):\n", "    \"\"\"Convert mesh to point cloud with customizable sampling.\"\"\"\n", "    try:\n", "        mesh = o3d.geometry.TriangleMesh()\n", "        mesh.vertices = o3d.utility.Vector3dVector(vertices)\n", "        mesh.triangles = o3d.utility.Vector3iVector(faces)\n", "        \n", "        # Ensure mesh is valid\n", "        if not mesh.is_vertex_manifold() or not mesh.is_edge_manifold():\n", "            mesh.remove_degenerate_triangles()\n", "            mesh.remove_duplicated_triangles()\n", "            mesh.remove_duplicated_vertices()\n", "            mesh.remove_non_manifold_edges()\n", "        \n", "        mesh.compute_triangle_normals()\n", "        \n", "        # Calculate surface area safely\n", "        try:\n", "            surface_area = mesh.get_surface_area()\n", "        except:\n", "            # Fallback: estimate surface area from bounding box\n", "            bbox = mesh.get_axis_aligned_bounding_box()\n", "            extent = bbox.get_extent()\n", "            surface_area = 2 * (extent[0] * extent[1] + extent[1] * extent[2] + extent[0] * extent[2])\n", "        \n", "        if surface_area <= 0:\n", "            surface_area = 1.0  # Default fallback\n", "        \n", "        num_points = min(int(surface_area / (point_density ** 2)), max_points)\n", "        num_points = max(num_points, 100)  # Minimum points\n", "        \n", "        if sampling_method == 'poisson':\n", "            try:\n", "                point_cloud = mesh.sample_points_poisson_disk(number_of_points=num_points, init_factor=3)\n", "            except:\n", "                # Fallback to uniform sampling\n", "                point_cloud = mesh.sample_points_uniformly(number_of_points=num_points)\n", "        else:\n", "            point_cloud = mesh.sample_points_uniformly(number_of_points=num_points)\n", "        \n", "        points = np.asarray(point_cloud.points)\n", "        pc_normals = np.asarray(point_cloud.normals) if normals is not None and point_cloud.has_normals() else None\n", "        pc_colors = np.asarray(point_cloud.colors) if colors is not None and point_cloud.has_colors() else None\n", "        \n", "        return points, pc_normals, pc_colors\n", "    \n", "    except Exception as e:\n", "        logger.debug(f\"Point cloud conversion failed: {str(e)}\")\n", "        return None, None, None\n", "\n", "def filter_element_by_size(vertices, min_size=0.5, min_volume=0.01):\n", "    \"\"\"Filter elements by bounding box size and volume.\"\"\"\n", "    if vertices is None or len(vertices) == 0:\n", "        return False\n", "    \n", "    bbox_min = np.min(vertices, axis=0)\n", "    bbox_max = np.max(vertices, axis=0)\n", "    dimensions = bbox_max - bbox_min\n", "    \n", "    volume = np.prod(dimensions)\n", "    \n", "    return np.any(dimensions > min_size) and volume > min_volume\n", "\n", "def process_ifc_element(element, settings, point_density=0.05, max_points=50000, min_size=0.5, min_volume=0.01, sampling_method='uniform'):\n", "    \"\"\"Process a single IFC element to generate a point cloud.\"\"\"\n", "    try:\n", "        if not element or not element.Representation:\n", "            return None, None, None, None\n", "        \n", "        vertices, faces, normals, colors = extract_mesh_from_element(element, settings, include_normals=True, include_colors=True)\n", "        \n", "        if vertices is None or not filter_element_by_size(vertices, min_size, min_volume):\n", "            return None, None, None, None\n", "        \n", "        points, pc_normals, pc_colors = mesh_to_pointcloud(vertices, faces, normals, colors, point_density, max_points, sampling_method)\n", "        return points, pc_normals, pc_colors, element.GlobalId\n", "    \n", "    except Exception as e:\n", "        logger.debug(f\"Error processing element {element.GlobalId}: {str(e)}\")\n", "        return None, None, None, None\n", "\n", "def process_ifc_file(ifc_file_path, point_density=0.05, max_points=50000, min_size=0.5, min_volume=0.01, sampling_method='uniform', num_workers=4):\n", "    \"\"\"Process an IFC file to generate point clouds for multiple elements sequentially.\"\"\"\n", "    try:\n", "        ifc_file = ifcopenshell.open(ifc_file_path)\n", "    except Exception as e:\n", "        logger.error(f\"Failed to open IFC file {ifc_file_path}: {e}\")\n", "        return []\n", "    \n", "    # Create geometry settings with proper context\n", "    settings = create_geometry_settings(ifc_file)\n", "    \n", "    # Get all elements with representation\n", "    elements = [e for e in ifc_file.by_type('IfcProduct') if e.Representation]\n", "    \n", "    logger.info(f\"Processing {len(elements)} elements sequentially\")\n", "    \n", "    results = []\n", "    successful_count = 0\n", "    \n", "    for element in tqdm(elements, desc=\"Processing elements\"):\n", "        result = process_ifc_element(\n", "            element, settings, point_density, max_points, min_size, min_volume, sampling_method\n", "        )\n", "        \n", "        if result[0] is not None:  # If points were generated\n", "            successful_count += 1\n", "            \n", "        results.append(result)\n", "        \n", "        # Progress update every 1000 elements\n", "        if len(results) % 1000 == 0:\n", "            logger.info(f\"Processed {len(results)}/{len(elements)} elements, {successful_count} successful\")\n", "    \n", "    # Filter out failed conversions\n", "    point_clouds = [(points, normals, colors, guid) for points, normals, colors, guid in results \n", "                    if points is not None]\n", "    \n", "    logger.info(f\"Generated {len(point_clouds)} valid point clouds from {len(elements)} elements\")\n", "    return point_clouds"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Convert IFC Elements to Point Cloud"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Converting IFC elements to point clouds...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["ERROR - Error setting context: 'Settings' object has no attribute 'set_context'\n", "INFO - Processing 40606 elements sequentially\n", "Processing elements:   2%|▏         | 998/40606 [01:37<1:00:17, 10.95it/s]INFO - Processed 1000/40606 elements, 1000 successful\n", "Processing elements:   5%|▍         | 1998/40606 [03:14<1:03:58, 10.06it/s]INFO - Processed 2000/40606 elements, 2000 successful\n", "Processing elements:   7%|▋         | 2999/40606 [04:51<1:01:33, 10.18it/s]INFO - Processed 3000/40606 elements, 3000 successful\n", "Processing elements:  10%|▉         | 3998/40606 [06:27<1:00:02, 10.16it/s]INFO - Processed 4000/40606 elements, 4000 successful\n", "Processing elements:  12%|█▏        | 4999/40606 [08:06<59:36,  9.95it/s]  INFO - Processed 5000/40606 elements, 5000 successful\n", "Processing elements:  15%|█▍        | 5999/40606 [09:43<55:57, 10.31it/s]  INFO - Processed 6000/40606 elements, 6000 successful\n", "Processing elements:  17%|█▋        | 6999/40606 [11:19<53:25, 10.48it/s]INFO - Processed 7000/40606 elements, 7000 successful\n", "Processing elements:  20%|█▉        | 7999/40606 [12:52<49:59, 10.87it/s]INFO - Processed 8000/40606 elements, 8000 successful\n", "Processing elements:  22%|██▏       | 8999/40606 [14:24<48:00, 10.97it/s]INFO - Processed 9000/40606 elements, 9000 successful\n", "Processing elements:  25%|██▍       | 9999/40606 [15:56<46:24, 10.99it/s]INFO - Processed 10000/40606 elements, 10000 successful\n", "Processing elements:  27%|██▋       | 10999/40606 [17:27<45:31, 10.84it/s]INFO - Processed 11000/40606 elements, 11000 successful\n", "Processing elements:  30%|██▉       | 11999/40606 [19:00<43:42, 10.91it/s]INFO - Processed 12000/40606 elements, 12000 successful\n", "Processing elements:  32%|███▏      | 12999/40606 [20:31<41:52, 10.99it/s]INFO - Processed 13000/40606 elements, 13000 successful\n", "Processing elements:  34%|███▍      | 13999/40606 [22:03<40:20, 10.99it/s]INFO - Processed 14000/40606 elements, 14000 successful\n", "Processing elements:  37%|███▋      | 14999/40606 [1:05:00<36:52:31,  5.18s/it]INFO - Processed 15000/40606 elements, 15000 successful\n", "Processing elements:  38%|███▊      | 15476/40606 [1:46:39<39:45:55,  5.70s/it]"]}], "source": ["# Main conversion code\n", "import ifcopenshell\n", "import numpy as np\n", "\n", "print(\"\\nConverting IFC elements to point clouds...\")\n", "\n", "all_points = []\n", "all_normals = []\n", "all_colors = []\n", "element_info = []\n", "conversion_stats = {\n", "    'total_processed': 0,\n", "    'successful_conversions': 0,\n", "    'failed_conversions': 0,\n", "    'filtered_small_elements': 0,\n", "    'total_points_generated': 0\n", "}\n", "\n", "# Define your parameters here\n", "include_element_types = ['IfcWall', 'IfcSlab']  # Add your desired element types\n", "point_density = 0.02\n", "max_points_per_element = 100000  # This maps to max_points parameter\n", "min_element_size = 0.3  # This maps to min_size parameter\n", "\n", "# Process IFC file\n", "point_clouds = process_ifc_file(\n", "    ifc_file_path,\n", "    point_density=point_density,\n", "    max_points=max_points_per_element,  # Fixed parameter name\n", "    min_size=min_element_size,  # Fixed parameter name\n", "    min_volume=0.005,\n", "    sampling_method='poisson',\n", "    num_workers=4  # Note: the function doesn't actually use this parameter\n", ")\n", "\n", "# Filter results by include_element_types\n", "ifc_file = ifcopenshell.open(ifc_file_path)\n", "filtered_point_clouds = []\n", "for points, normals, colors, guid in point_clouds:\n", "    try:\n", "        element = ifc_file.by_guid(guid)\n", "        if element and element.is_a() in include_element_types:\n", "            filtered_point_clouds.append((points, normals, colors, guid))\n", "    except Exception as e:\n", "        print(f\"Warning: Could not process element {guid}: {e}\")\n", "        continue\n", "\n", "point_clouds = filtered_point_clouds\n", "\n", "# Process results\n", "for points, normals, colors, guid in point_clouds:\n", "    conversion_stats['total_processed'] += 1\n", "    \n", "    if points is not None and len(points) > 0:\n", "        all_points.append(points)\n", "        if normals is not None:\n", "            all_normals.append(normals)\n", "        if colors is not None:\n", "            all_colors.append(colors)\n", "        \n", "        # Retrieve element from IFC file\n", "        try:\n", "            element = ifc_file.by_guid(guid)\n", "            \n", "            # Store element information\n", "            element_info.append({\n", "                'GlobalId': guid,\n", "                'Name': getattr(element, 'Name', None),\n", "                'Type': element.is_a(),\n", "                'PointCount': len(points),\n", "                'BoundingBox': {\n", "                    'min': np.min(points, axis=0).tolist(),\n", "                    'max': np.max(points, axis=0).tolist()\n", "                },\n", "                'HasNormals': normals is not None,\n", "                'HasColors': colors is not None\n", "            })\n", "            \n", "            conversion_stats['successful_conversions'] += 1\n", "            conversion_stats['total_points_generated'] += len(points)\n", "        except Exception as e:\n", "            print(f\"Warning: Could not retrieve element info for {guid}: {e}\")\n", "            conversion_stats['failed_conversions'] += 1\n", "    else:\n", "        conversion_stats['failed_conversions'] += 1\n", "\n", "# Update filtered elements count - get total elements of desired types\n", "try:\n", "    elements = [e for e in ifc_file.by_type('IfcProduct') \n", "               if e.Representation and e.is_a() in include_element_types]\n", "    conversion_stats['filtered_small_elements'] = (len(elements) - \n", "                                                 conversion_stats['successful_conversions'] - \n", "                                                 conversion_stats['failed_conversions'])\n", "except Exception as e:\n", "    print(f\"Warning: Could not calculate filtered elements count: {e}\")\n", "    conversion_stats['filtered_small_elements'] = 0\n", "\n", "# <PERSON><PERSON><PERSON> all points\n", "if all_points:\n", "    combined_points = np.vstack(all_points)\n", "    combined_normals = np.vstack(all_normals) if all_normals else None\n", "    combined_colors = np.vstack(all_colors) if all_colors else None\n", "    \n", "    print(f\"\\nConversion completed:\")\n", "    print(f\"  Total points generated: {len(combined_points):,}\")\n", "    print(f\"  Successful conversions: {conversion_stats['successful_conversions']}\")\n", "    print(f\"  Failed conversions: {conversion_stats['failed_conversions']}\")\n", "    print(f\"  Small elements filtered: {conversion_stats['filtered_small_elements']}\")\n", "    print(f\"  Normals included: {combined_normals is not None}\")\n", "    print(f\"  Colors included: {combined_colors is not None}\")\n", "else:\n", "    print(\"\\nNo points generated - conversion failed\")\n", "    combined_points = None\n", "    combined_normals = None\n", "    combined_colors = None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Point Cloud Analysis and Export"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import json\n", "from pathlib import Path\n", "from pyproj import CRS, Transformer\n", "import open3d as o3d\n", "\n", "if combined_points is not None and len(combined_points) > 0:\n", "    print(f\"\\nPoint Cloud: {len(combined_points):,} points\")\n", "    \n", "    bounds_min = combined_points.min(axis=0)\n", "    bounds_max = combined_points.max(axis=0)\n", "    center = combined_points.mean(axis=0)\n", "    extent = bounds_max - bounds_min\n", "\n", "    print(f\"  Bounds X: {bounds_min[0]:.2f} – {bounds_max[0]:.2f}\")\n", "    print(f\"  Bounds Y: {bounds_min[1]:.2f} – {bounds_max[1]:.2f}\")\n", "    print(f\"  Bounds Z: {bounds_min[2]:.2f} – {bounds_max[2]:.2f}\")\n", "    print(f\"  Center: {np.round(center, 2).tolist()}\")\n", "    print(f\"  Extent: {np.round(extent, 2).tolist()}\")\n", "\n", "    # Create Open3D point cloud object\n", "    pcd = o3d.geometry.PointCloud()\n", "    pcd.points = o3d.utility.Vector3dVector(combined_points)\n", "\n", "    # File naming\n", "    ifc_name = Path(ifc_file_path).stem\n", "    paths = {\n", "        \"ply\": output_path / f\"{ifc_name}_pointcloud.ply\",\n", "        \"data_driven\": output_path / f\"{ifc_name}_data_driven.ply\",\n", "        \"crs\": output_path / f\"{ifc_name}_crs.json\",\n", "        \"element_info\": output_path / f\"{ifc_name}_element_info.json\",\n", "        \"summary\": output_path / f\"{ifc_name}_conversion_summary.json\"\n", "    }\n", "\n", "    try:\n", "        o3d.io.write_point_cloud(str(paths[\"ply\"]), pcd)\n", "        o3d.io.write_point_cloud(str(paths[\"data_driven\"]), pcd)\n", "        print(f\"Saved PLY: {paths['ply'].name}, {paths['data_driven'].name}\")\n", "    except Exception as e:\n", "        print(f\"[ERROR] Failed to save PLY: {e}\")\n", "\n", "    # Save CRS metadata\n", "    try:\n", "        #target_crs = get_utm_crs(lat, lon)\n", "        target_crs = CRS.from_epsg(32633)\n", "        print(f\"Target CRS: {target_crs}\")\n", "        transformer = Transformer.from_crs(CRS.from_epsg(4326), target_crs, always_xy=True)\n", "        utm_x, utm_y = transformer.transform(lon, lat)\n", "\n", "        # Convert string to Path object\n", "        ifc_path = Path(ifc_file_path)\n", "        crs_path = ifc_path.with_name(ifc_path.stem + \"_crs.json\")\n", "\n", "        crs_info = {\n", "            'epsg': target_crs.to_epsg(),\n", "            'utm_zone': target_crs.to_string(),\n", "            'origin_utm_x': utm_x,\n", "            'origin_utm_y': utm_y\n", "        }            \n", "\n", "        with open(crs_path, \"w\") as f:\n", "            json.dump(crs_info, f, indent=2)\n", "\n", "        print(f\"Saved CRS metadata: {paths['crs'].name}\")\n", "    except Exception as e:\n", "        print(f\"[WARNING] Failed to write CRS metadata: {e}\")\n", "\n", "    # Save element info\n", "    try:\n", "        with open(paths[\"element_info\"], 'w') as f:\n", "            json.dump(element_info, f, indent=2)\n", "        print(f\"Saved element info: {paths['element_info'].name}\")\n", "    except Exception as e:\n", "        print(f\"[WARNING] Failed to save element info: {e}\")\n", "\n", "    # Save summary\n", "    try:\n", "        summary = {\n", "            'ifc_file': ifc_name,\n", "            'conversion_date': pd.Timestamp.now().isoformat(),\n", "            'parameters': {\n", "                'point_density': point_density,\n", "                'min_element_size': min_element_size,\n", "                'max_points_per_element': max_points_per_element,\n", "                'element_types': include_element_types\n", "            },\n", "            'statistics': conversion_stats,\n", "            'point_cloud_properties': {\n", "                'total_points': len(combined_points),\n", "                'bounds': dict(zip(['x_min', 'y_min', 'z_min'], bounds_min)),\n", "                'bounds_max': dict(zip(['x_max', 'y_max', 'z_max'], bounds_max)),\n", "                'center': center.tolist(),\n", "                'extent': extent.tolist()\n", "            },\n", "            'files_generated': {k: str(v) for k, v in paths.items()}\n", "        }\n", "\n", "        with open(paths[\"summary\"], 'w') as f:\n", "            json.dump(summary, f, indent=2)\n", "\n", "        print(f\"Saved summary: {paths['summary'].name}\")\n", "    except Exception as e:\n", "        print(f\"[WARNING] Failed to write summary: {e}\")\n", "\n", "    print(f\"\\nIFC TO POINT CLOUD CONVERSION COMPLETE — Output in: {output_path}\")\n", "\n", "else:\n", "    print(\"\\nNo point cloud generated — check IFC input or filters.\")\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}