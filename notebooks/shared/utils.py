from pathlib import Path
import logging

logger = logging.getLogger(__name__)

def resolve_point_cloud_path(path_input):
    """
    Resolve and validate the point cloud file path.
    Automatically switches between `.las` and `.ply` if needed.
    """
    if not path_input:
        raise ValueError("point_cloud_path must be provided as a Papermill parameter.")

    point_cloud_file = Path(path_input)

    if not point_cloud_file.exists():
        alternative_ext = ".ply" if point_cloud_file.suffix == ".las" else ".las"
        alt_file = point_cloud_file.with_suffix(alternative_ext)

        if alt_file.exists():
            logger.warning(
                f"{point_cloud_file.suffix} file not found. Using alternative: {alt_file}"
            )
            point_cloud_file = alt_file
        else:
            raise FileNotFoundError(
                f"Neither {point_cloud_file} nor {alt_file} found."
            )

    if not point_cloud_file.is_file():
        raise ValueError(f"Provided path is not a file: {point_cloud_file}")

    logger.info(f"Using point cloud file: {point_cloud_file}")
    return point_cloud_file

def find_project_root(self, marker_dir: str, max_levels: int = 10) -> Path:
        """Finds the project root by looking for a directory containing the given marker (e.g. 'data')"""
        current = Path(__file__).resolve().parent if "__file__" in globals() else Path().resolve()
        print(f"Searching for project root from: {current}")

        expected_subdirs = {"processed", "output_runs"}  # Add more if needed

        for _ in range(max_levels):
            data_dir = current / marker_dir
            if data_dir.is_dir():
                subdirs = {p.name for p in data_dir.iterdir() if p.is_dir()}
                if expected_subdirs.issubset(subdirs):
                    print(f"Found project root at: {current}")
                    return current
            if current == current.parent:
                break
            current = current.parent

        raise FileNotFoundError(f"Could not find project root containing '{marker_dir}' with expected subdirs in any parent.")

