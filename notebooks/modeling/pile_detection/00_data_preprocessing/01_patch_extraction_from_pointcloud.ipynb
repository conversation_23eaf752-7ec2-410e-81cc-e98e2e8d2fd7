{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Data Preparation for Machine Learning\n", "\n", "This notebook prepares training data for ML-based pile detection by extracting point cloud patches around pile locations.\n", "\n", "## What we will do:\n", "1. Load aligned point cloud data\n", "2. Load pile location metadata\n", "3. Extract point cloud patches around each pile\n", "4. Create positive samples (pile locations) and negative samples (non-pile locations)\n", "5. Save the prepared data for ML training\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: July 2025  \n", "**Project**: As-Built Foundation Analysis"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 1: Setup Environment"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Libraries imported successfully\n"]}], "source": ["# Import required libraries\n", "import sys\n", "import numpy as np\n", "import pandas as pd\n", "import open3d as o3d\n", "from pathlib import Path\n", "from scipy.spatial import cKDTree\n", "from datetime import datetime\n", "import json\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "from sklearn.model_selection import train_test_split\n", "\n", "\n", "print(\"Libraries imported successfully\")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Notebooks root: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/notebooks\n"]}], "source": ["# Setup notebook path and import shared utilities\n", "current_path = Path.cwd()\n", "while current_path.name != \"notebooks\":\n", "    if current_path.parent == current_path:\n", "        raise RuntimeError(\"Could not find 'notebooks' directory\")\n", "    current_path = current_path.parent\n", "\n", "notebooks_root = current_path\n", "if str(notebooks_root) not in sys.path:\n", "    sys.path.insert(0, str(notebooks_root))\n", "\n", "from shared.config import get_processed_data_path, find_latest_file\n", "print(f\"Notebooks root: {notebooks_root}\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["UPDATED Configuration:\n", "  Min points per patch: 30 (was 100)\n", "  Max positive samples: 3844 (based on harmonization)\n", "  Max negative samples: 3844\n", "Configuration:\n", "  Site: trino_enel\n", "  Ground method: ransac_pmf\n", "  Patch radius: 5.0 meters\n", "  Min points per patch: 30\n", "  Max positive samples: 3844\n", "  Max negative samples: 3844\n"]}], "source": ["# Configuration - These are the main settings for our analysis\n", "site_name = \"trino_enel\"  # Name of the construction site\n", "ground_method = \"ransac_pmf\"  # Ground segmentation method used\n", "patch_radius = 5.0  # How far around each pile to extract points (meters)\n", "min_points_per_patch = 30  # REDUCED from 100 - matche the  data!, Minimum points needed to consider a patch valid\n", "max_positive_samples = 3844  # Based on your harmonized ready piles , Maximum number of pile samples to extract\n", "max_negative_samples = 3844  # Balance dataset ,Maximum number of non-pile samples to extract\n", "\n", "# Quality filtering settings \n", "MIN_CONFIDENCE_THRESHOLD = 'medium'  # Use medium+ confidence piles\n", "NEGATIVE_SAMPLE_BUFFER = 15.0  # Distance from piles for negative samples\n", "\n", "print(\"UPDATED Configuration:\")\n", "print(f\"  Min points per patch: {min_points_per_patch} (was 100)\")\n", "print(f\"  Max positive samples: {max_positive_samples} (based on harmonization)\")\n", "print(f\"  Max negative samples: {max_negative_samples}\")\n", "\n", "print(\"Configuration:\")\n", "print(f\"  Site: {site_name}\")\n", "print(f\"  Ground method: {ground_method}\")\n", "print(f\"  Patch radius: {patch_radius} meters\")\n", "print(f\"  Min points per patch: {min_points_per_patch}\")\n", "print(f\"  Max positive samples: {max_positive_samples}\")\n", "print(f\"  Max negative samples: {max_negative_samples}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 2: Load Point Cloud Data"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading point cloud data...\n", "Loading from: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/gcp_alignment_z_corrected/ransac_pmf/filtered/trino_enel_gcp_aligned_z_corrected_filtered.ply\n", "Successfully loaded 517,002 points\n"]}], "source": ["# Load the aligned point cloud data\n", "print(\"Loading point cloud data...\")\n", "\n", "# First try to find the filtered version\n", "alignment_path = get_processed_data_path(site_name, \"gcp_alignment_z_corrected\") / ground_method / \"filtered\"\n", "point_cloud_file = find_latest_file(alignment_path, \"*_gcp_aligned_z_corrected_filtered.ply\")\n", "\n", "# If filtered version doesn't exist, use the non-filtered version\n", "if not point_cloud_file.exists():\n", "    print(\"Filtered version not found, using non-filtered version...\")\n", "    alignment_path = get_processed_data_path(site_name, \"gcp_alignment_z_corrected\") / ground_method\n", "    point_cloud_file = find_latest_file(alignment_path, \"*_gcp_aligned_z_corrected.ply\")\n", "\n", "print(f\"Loading from: {point_cloud_file}\")\n", "\n", "# Load the point cloud using Open3D\n", "point_cloud = o3d.io.read_point_cloud(str(point_cloud_file))\n", "points = np.asarray(point_cloud.points)\n", "\n", "print(f\"Successfully loaded {len(points):,} points\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Point cloud information:\n", "  Number of points: 517,002\n", "  Data shape: (517002, 3)\n", "  Data type: float64\n", "\n", "Coordinate ranges:\n", "  X: 435220.30 to 436796.35 (range: 1576.05 meters)\n", "  Y: 5010812.68 to 5012553.07 (range: 1740.39 meters)\n", "  Z: 152.43 to 182.42 (range: 29.98 meters)\n", "\n", "First 5 points:\n", "  Point 1: X=435344.09, Y=5011988.43, Z=157.60\n", "  Point 2: X=436578.57, Y=5012091.24, Z=154.69\n", "  Point 3: X=436464.55, Y=5011306.84, Z=155.10\n", "  Point 4: X=436146.57, Y=5010913.93, Z=154.13\n", "  Point 5: X=435563.17, Y=5011417.41, Z=158.16\n"]}], "source": ["# Examine the point cloud data\n", "print(\"Point cloud information:\")\n", "print(f\"  Number of points: {len(points):,}\")\n", "print(f\"  Data shape: {points.shape}\")\n", "print(f\"  Data type: {points.dtype}\")\n", "\n", "print(\"\\nCoordinate ranges:\")\n", "for i, axis in enumerate(['X', 'Y', 'Z']):\n", "    min_val = points[:, i].min()\n", "    max_val = points[:, i].max()\n", "    range_val = max_val - min_val\n", "    print(f\"  {axis}: {min_val:.2f} to {max_val:.2f} (range: {range_val:.2f} meters)\")\n", "\n", "print(\"\\nFirst 5 points:\")\n", "for i in range(min(5, len(points))):\n", "    print(f\"  Point {i+1}: X={points[i,0]:.2f}, Y={points[i,1]:.2f}, Z={points[i,2]:.2f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 3: Create Spatial Index for Fast Searching"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Creating spatial index for point cloud...\n", "Spatial index created for 517,002 points\n"]}], "source": ["# Create a spatial index (KD-Tree) for fast point searching\n", "print(\"Creating spatial index for point cloud...\")\n", "\n", "# Build KD-Tree for efficient nearest neighbor searches\n", "point_tree = cKDTree(points)\n", "print(f\"Spatial index created for {len(points):,} points\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 4: Load IFC Metadata"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading IFC metadata...\n", "Loading from: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/ifc_metadata/GRE.EEC.S.00.IT.P.14353.00.265_enhanced_metadata.csv\n", "Loaded 14,460 IFC elements\n", "\n", "Available columns: ['GlobalId', 'Name', 'Type', 'Description', 'Tag', 'Site_Latitude', 'Site_Longitude', 'Site_Elevation', 'X', 'Y', 'Z', 'GeometryExtracted', 'Longitude', 'Latitude']\n", "\n", "Element types in data:\n", "Type\n", "IfcColumn    14460\n", "Name: count, dtype: int64\n"]}], "source": ["# Load IFC metadata containing pile information\n", "print(\"Loading IFC metadata...\")\n", "\n", "ifc_metadata_path = get_processed_data_path(site_name, \"ifc_metadata\")\n", "metadata_file = find_latest_file(ifc_metadata_path, \"*enhanced_metadata.csv\")\n", "\n", "print(f\"Loading from: {metadata_file}\")\n", "ifc_data = pd.read_csv(metadata_file)\n", "print(f\"Loaded {len(ifc_data):,} IFC elements\")\n", "\n", "# Show what columns are available\n", "print(f\"\\nAvailable columns: {list(ifc_data.columns)}\")\n", "print(f\"\\nElement types in data:\")\n", "print(ifc_data['Type'].value_counts().head())"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Filtering piles by quality and point density...\n", "Loaded 14,623 harmonized piles\n", "\n", "Data sources:\n", "source\n", "ifc_only    13335\n", "matched      1125\n", "kml_only      163\n", "Name: count, dtype: int64\n", "\n", "Confidence levels:\n", "confidence\n", "medium    13335\n", "high       1125\n", "low         163\n", "Name: count, dtype: int64\n", "After confidence filtering (>= medium): 14460 piles\n", "Calculating point density for piles...\n", "Calculated point density - ready piles: 3835\n", "\n", "Final pile statistics:\n", "  Ready for extraction: 3835\n", "  Point density - Mean: 38.8\n", "  Point density - Median: 37.0\n", "Valid pile coordinates: 3,835\n", "\n", "Pile coordinate ranges:\n", "  x: 435267.20 to 436710.45 (range: 1443.25 meters)\n", "  y: 5010904.97 to 5012462.41 (range: 1557.44 meters)\n", "  z: 155.12 to 159.52 (range: 4.40 meters)\n", "\n", "Sample pile data:\n", "                                        pile_id   source confidence  \\\n", "40  TRPL_Tracker Pile:TRPL_Tracker Pile:1249724  matched       high   \n", "58  TRPL_Tracker Pile:TRPL_Tracker Pile:1249669  matched       high   \n", "64  TRPL_Tracker Pile:TRPL_Tracker Pile:1249597  matched       high   \n", "66  TRPL_Tracker Pile:TRPL_Tracker Pile:1249285  matched       high   \n", "76  TRPL_Tracker Pile:TRPL_Tracker Pile:1249315  matched       high   \n", "\n", "             x            y        z  nearby_points  \n", "40  435875.197  5011838.056  157.524             34  \n", "58  435884.697  5011633.989  156.829             31  \n", "64  435846.697  5011676.043  156.899             36  \n", "66  435913.197  5011688.454  157.224             42  \n", "76  435922.697  5011694.525  157.241             42  \n", "\n", "Test query around first pile location:\n", "  Pile location: X=435875.20, Y=5011838.06, Z=157.52\n", "  Points within 5.0m radius: 34\n"]}], "source": ["print(\"Filtering piles by quality and point density...\")\n", "\n", "# Load the harmonized dataset instead of raw IFC\n", "harmonized_file = Path(\"output/ml_patch_data/trino_enel_harmonized_piles_corrected.csv\")\n", "\n", "if not harmonized_file.exists():\n", "    raise FileNotFoundError(\n", "        f\"Harmonized pile data not found at {harmonized_file}\\n\"\n", "        f\"Please run the harmonization notebook first!\"\n", "    )\n", "\n", "pile_data = pd.read_csv(harmonized_file)\n", "print(f\"Loaded {len(pile_data):,} harmonized piles\")\n", "print(f\"\\nData sources:\")\n", "print(pile_data['source'].value_counts())\n", "print(f\"\\nConfidence levels:\")\n", "print(pile_data['confidence'].value_counts())\n", "\n", "\n", "# Filter by confidence level\n", "confidence_order = {'high': 3, 'medium': 2, 'low': 1}\n", "min_confidence_score = confidence_order[MIN_CONFIDENCE_THRESHOLD]\n", "\n", "quality_piles = pile_data[\n", "    pile_data['confidence'].map(confidence_order) >= min_confidence_score\n", "].copy()\n", "\n", "print(f\"After confidence filtering (>= {MIN_CONFIDENCE_THRESHOLD}): {len(quality_piles)} piles\")\n", "\n", "# Filter by point density (if nearby_points column exists)\n", "if 'nearby_points' in quality_piles.columns:\n", "    ready_piles = quality_piles[quality_piles['nearby_points'] >= min_points_per_patch].copy()\n", "    print(f\"After point density filtering (>= {min_points_per_patch}): {len(ready_piles)} piles\")\n", "else:\n", "    # Calculate point density if not available\n", "    print(\"Calculating point density for piles...\")\n", "    point_counts = []\n", "    \n", "    for _, pile in quality_piles.iterrows():\n", "        pile_coord = [pile['x'], pile['y'], pile['z']]\n", "        indices = point_tree.query_ball_point(pile_coord, patch_radius)\n", "        point_counts.append(len(indices))\n", "    \n", "    quality_piles['nearby_points'] = point_counts\n", "    ready_piles = quality_piles[quality_piles['nearby_points'] >= min_points_per_patch].copy()\n", "    print(f\"Calculated point density - ready piles: {len(ready_piles)}\")\n", "\n", "# Extract final coordinates for patch extraction\n", "pile_metadata = ready_piles[['pile_id', 'source', 'confidence', 'nearby_points']].to_dict('records')\n", "\n", "print(f\"\\nFinal pile statistics:\")\n", "print(f\"  Ready for extraction: {len(ready_piles)}\")\n", "print(f\"  Point density - Mean: {ready_piles['nearby_points'].mean():.1f}\")\n", "print(f\"  Point density - Median: {ready_piles['nearby_points'].median():.1f}\")\n", "\n", "# Extract coordinates and remove any rows with missing coordinates\n", "coordinate_columns = ['x', 'y', 'z']\n", "pile_coordinates = ready_piles[coordinate_columns].dropna().values\n", "print(f\"Valid pile coordinates: {len(pile_coordinates):,}\")\n", "\n", "# Show coordinate ranges\n", "print(\"\\nPile coordinate ranges:\")\n", "for i, axis in enumerate(coordinate_columns):\n", "    min_val = pile_coordinates[:, i].min()\n", "    max_val = pile_coordinates[:, i].max()\n", "    range_val = max_val - min_val\n", "    print(f\"  {axis}: {min_val:.2f} to {max_val:.2f} (range: {range_val:.2f} meters)\")\n", "\n", "# Show sample pile data\n", "print(\"\\nSample pile data:\")\n", "print(ready_piles[['pile_id', 'source', 'confidence', 'x', 'y', 'z', 'nearby_points']].head())\n", "\n", "\n", "# Test the spatial index with a sample query\n", "if len(pile_coordinates) > 0:\n", "    test_pile = pile_coordinates[0]\n", "    test_indices = point_tree.query_ball_point(test_pile, patch_radius)\n", "    print(f\"\\nTest query around first pile location:\")\n", "    print(f\"  Pile location: X={test_pile[0]:.2f}, Y={test_pile[1]:.2f}, Z={test_pile[2]:.2f}\")\n", "    print(f\"  Points within {patch_radius}m radius: {len(test_indices)}\")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 5: Extract Positive Samples (<PERSON><PERSON>)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Patch extraction function defined\n"]}], "source": ["# Function to extract points around a location\n", "def extract_patch_around_location(center_point, radius, tree, all_points, min_points=100):\n", "    \"\"\"\n", "    Extract points within a radius around a center point.\n", "    \"\"\"\n", "    # Find all points within radius\n", "    point_indices = tree.query_ball_point(center_point, radius)\n", "    \n", "    # Check if we have enough points\n", "    if len(point_indices) < min_points:\n", "        return None\n", "    \n", "    # Extract the actual point coordinates\n", "    patch_points = all_points[point_indices]\n", "    \n", "    return patch_points\n", "\n", "\n", "def extract_patch_with_metadata(center_point, pile_info, radius, tree, all_points, min_points=30):\n", "    \"\"\"Enhanced patch extraction with metadata tracking\"\"\"\n", "    \n", "    point_indices = tree.query_ball_point(center_point, radius)\n", "    \n", "    if len(point_indices) < min_points:\n", "        return None, None\n", "    \n", "    patch_points = all_points[point_indices]\n", "    \n", "    # Center the patch (important for ML)\n", "    centered_patch = patch_points - center_point\n", "    \n", "    # Enhanced metadata\n", "    patch_metadata = {\n", "        'pile_id': pile_info.get('pile_id', 'unknown'),\n", "        'source': pile_info.get('source', 'unknown'), \n", "        'confidence': pile_info.get('confidence', 'unknown'),\n", "        'center_location': center_point.tolist(),\n", "        'radius_used': radius,\n", "        'num_points': len(patch_points),\n", "        'patch_type': 'positive',\n", "        'label': 1\n", "    }\n", "    \n", "    return centered_patch, patch_metadata\n", "\n", "\n", "print(\"Patch extraction function defined\")"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Extracting positive samples with quality metadata...\n", "  Extracted 100 patches...\n", "  Extracted 200 patches...\n", "  Extracted 300 patches...\n", "  Extracted 400 patches...\n", "  Extracted 500 patches...\n", "  Extracted 600 patches...\n", "  Extracted 700 patches...\n", "  Extracted 800 patches...\n", "  Extracted 900 patches...\n", "  Extracted 1000 patches...\n", "  Extracted 1100 patches...\n", "  Extracted 1200 patches...\n", "  Extracted 1300 patches...\n", "  Extracted 1400 patches...\n", "  Extracted 1500 patches...\n", "  Extracted 1600 patches...\n", "  Extracted 1700 patches...\n", "  Extracted 1800 patches...\n", "  Extracted 1900 patches...\n", "  Extracted 2000 patches...\n", "  Extracted 2100 patches...\n", "  Extracted 2200 patches...\n", "  Extracted 2300 patches...\n", "  Extracted 2400 patches...\n", "  Extracted 2500 patches...\n", "  Extracted 2600 patches...\n", "  Extracted 2700 patches...\n", "  Extracted 2800 patches...\n", "  Extracted 2900 patches...\n", "  Extracted 3000 patches...\n", "  Extracted 3100 patches...\n", "  Extracted 3200 patches...\n", "  Extracted 3300 patches...\n", "  Extracted 3400 patches...\n", "  Extracted 3500 patches...\n", "  Extracted 3600 patches...\n", "  Extracted 3700 patches...\n", "  Extracted 3800 patches...\n", "\n", "Positive extraction complete:\n", "  Total patches: 3835\n", "  By confidence: {'high': 104, 'medium': 3731, 'low': 0}\n"]}], "source": ["print(\"Extracting positive samples with quality metadata...\")\n", "\n", "positive_patches = []\n", "positive_metadata = []\n", "extraction_stats = {'high': 0, 'medium': 0, 'low': 0}\n", "\n", "# Try different radii if the default doesn't work\n", "radii_to_try = [patch_radius, patch_radius * 1.5, patch_radius * 2.0]\n", "\n", "for i, (pile_coord, pile_info) in enumerate(zip(pile_coordinates, pile_metadata)):\n", "    if len(positive_patches) >= max_positive_samples:\n", "        break\n", "    \n", "    patch_extracted = False\n", "    \n", "    # Try different radii to get enough points\n", "    for radius in radii_to_try:\n", "        patch, metadata = extract_patch_with_metadata(\n", "            pile_coord, pile_info, radius, point_tree, points, min_points_per_patch\n", "        )\n", "        \n", "        if patch is not None:\n", "            positive_patches.append(patch)\n", "            positive_metadata.append(metadata)\n", "            extraction_stats[pile_info['confidence']] += 1\n", "            patch_extracted = True\n", "            \n", "            if len(positive_patches) % 100 == 0:\n", "                print(f\"  Extracted {len(positive_patches)} patches...\")\n", "            break\n", "    \n", "    if not patch_extracted:\n", "        print(f\"Failed to extract patch for pile {pile_info.get('pile_id', i)}\")\n", "\n", "print(f\"\\nPositive extraction complete:\")\n", "print(f\"  Total patches: {len(positive_patches)}\")\n", "print(f\"  By confidence: {extraction_stats}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 6: Extract Negative Samples (Non-<PERSON><PERSON>)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["def generate_negative_samples_enhanced(pile_coords, point_tree, all_points, \n", "                                     target_count, buffer_distance=15.0):\n", "    \"\"\"Enhanced negative sampling with better spatial distribution\"\"\"\n", "    \n", "    # Calculate bounds with margin\n", "    margin = 20.0\n", "    x_min, x_max = all_points[:, 0].min() + margin, all_points[:, 0].max() - margin\n", "    y_min, y_max = all_points[:, 1].min() + margin, all_points[:, 1].max() - margin\n", "    z_min, z_max = all_points[:, 2].min(), all_points[:, 2].max()\n", "    \n", "    negative_patches = []\n", "    negative_metadata = []\n", "    attempts = 0\n", "    max_attempts = target_count * 15\n", "    \n", "    print(f\"Generating {target_count} negative samples...\")\n", "    print(f\"  Buffer distance: {buffer_distance}m\")\n", "    \n", "    while len(negative_patches) < target_count and attempts < max_attempts:\n", "        attempts += 1\n", "        \n", "        # Random location\n", "        candidate = np.array([\n", "            np.random.uniform(x_min, x_max),\n", "            np.random.uniform(y_min, y_max), \n", "            np.random.uniform(z_min, z_max)\n", "        ])\n", "        \n", "        # Check distance from all piles\n", "        distances = np.linalg.norm(pile_coords - candidate, axis=1)\n", "        if distances.min() >= buffer_distance:\n", "            \n", "            # Extract patch\n", "            indices = point_tree.query_ball_point(candidate, patch_radius)\n", "            if len(indices) >= min_points_per_patch:\n", "                \n", "                patch_points = all_points[indices]\n", "                centered_patch = patch_points - candidate\n", "                \n", "                metadata = {\n", "                    'patch_id': f\"neg_{len(negative_patches):04d}\",\n", "                    'center_location': candidate.tolist(),\n", "                    'radius_used': patch_radius,\n", "                    'num_points': len(patch_points),\n", "                    'min_pile_distance': distances.min(),\n", "                    'patch_type': 'negative',\n", "                    'label': 0\n", "                }\n", "                \n", "                negative_patches.append(centered_patch)\n", "                negative_metadata.append(metadata)\n", "                \n", "                if len(negative_patches) % 100 == 0:\n", "                    print(f\"  Generated {len(negative_patches)} negative patches...\")\n", "    \n", "    return negative_patches, negative_metadata, attempts"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Extracting negative samples from non-pile areas...\n", "Point cloud bounds for negative sampling:\n", "  X: 435220.30 to 436796.35\n", "  Y: 5010812.68 to 5012553.07\n", "  Z: 152.43 to 182.42\n", "Minimum distance from piles: 15.0 meters\n"]}], "source": ["# Extract negative samples (patches from areas without piles)\n", "print(\"Extracting negative samples from non-pile areas...\")\n", "\n", "# Calculate point cloud bounds for random sampling\n", "x_min, x_max = points[:, 0].min(), points[:, 0].max()\n", "y_min, y_max = points[:, 1].min(), points[:, 1].max()\n", "z_min, z_max = points[:, 2].min(), points[:, 2].max()\n", "\n", "print(f\"Point cloud bounds for negative sampling:\")\n", "print(f\"  X: {x_min:.2f} to {x_max:.2f}\")\n", "print(f\"  Y: {y_min:.2f} to {y_max:.2f}\")\n", "print(f\"  Z: {z_min:.2f} to {z_max:.2f}\")\n", "\n", "# Minimum distance from pile locations for negative samples\n", "# min_distance_from_piles = patch_radius * 3.0\n", "min_distance_from_piles = NEGATIVE_SAMPLE_BUFFER  # Use configured value (15.0)\n", "print(f\"Minimum distance from piles: {min_distance_from_piles:.1f} meters\")"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Generating negative samples using enhanced method...\n", "Generating 3844 negative samples...\n", "  Buffer distance: 15.0m\n", "  Generated 100 negative patches...\n", "  Generated 200 negative patches...\n", "  Generated 300 negative patches...\n", "  Generated 400 negative patches...\n", "  Generated 500 negative patches...\n", "  Generated 600 negative patches...\n", "  Generated 700 negative patches...\n", "  Generated 800 negative patches...\n", "  Generated 900 negative patches...\n", "\n", "Negative extraction complete:\n", "  Generated: 944 patches\n", "  Attempts: 57660\n", "  Success rate: 1.6%\n"]}], "source": ["# %%\n", "# Generate negative samples using enhanced function\n", "print(\"Generating negative samples using enhanced method...\")\n", "\n", "negative_patches, negative_metadata, neg_attempts = generate_negative_samples_enhanced(\n", "    pile_coordinates, point_tree, points, max_negative_samples, NEGATIVE_SAMPLE_BUFFER\n", ")\n", "\n", "print(f\"\\nNegative extraction complete:\")\n", "print(f\"  Generated: {len(negative_patches)} patches\")\n", "print(f\"  Attempts: {neg_attempts}\")\n", "print(f\"  Success rate: {len(negative_patches)/neg_attempts*100:.1f}%\")\n", "\n", "# Use negative_metadata (not negative_info) for consistency\n", "negative_info = negative_metadata"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 7: Save Prepared Data"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Preparing enhanced dataset for ML training...\n", "Dataset summary:\n", "  Total patches: 4779\n", "  Positive: 3835 (80.2%)\n", "  Negative: 944 (19.8%)\n", "\n", "Dataset splits:\n", "  Training: 3345 patches\n", "  Validation: 717 patches\n", "  Testing: 717 patches\n"]}], "source": ["from sklearn.model_selection import train_test_split\n", "\n", "print(\"Preparing enhanced dataset for ML training...\")\n", "\n", "# Combine all patches and metadata  \n", "all_patches = positive_patches + negative_patches\n", "all_metadata = positive_metadata + negative_info\n", "\n", "# Create labels array\n", "labels = np.array([meta.get('label', meta.get('patch_type') == 'positive') for meta in all_metadata])\n", "\n", "print(f\"Dataset summary:\")\n", "print(f\"  Total patches: {len(all_patches)}\")\n", "print(f\"  Positive: {len(positive_patches)} ({len(positive_patches)/len(all_patches)*100:.1f}%)\")\n", "print(f\"  Negative: {len(negative_patches)} ({len(negative_patches)/len(all_patches)*100:.1f}%)\")\n", "\n", "# Train/validation/test split\n", "train_patches, temp_patches, train_meta, temp_meta = train_test_split(\n", "    all_patches, all_metadata, test_size=0.3, random_state=42, stratify=labels\n", ")\n", "\n", "val_patches, test_patches, val_meta, test_meta = train_test_split(\n", "    temp_patches, temp_meta, test_size=0.5, random_state=42, \n", "    stratify=[m.get('label', m.get('patch_type') == 'positive') for m in temp_meta]\n", ")\n", "\n", "print(f\"\\nDataset splits:\")\n", "print(f\"  Training: {len(train_patches)} patches\")\n", "print(f\"  Validation: {len(val_patches)} patches\")\n", "print(f\"  Testing: {len(test_patches)} patches\")\n"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Saved train: 3345 patches\n", "Saved val: 717 patches\n", "Saved test: 717 patches\n", "\n", "Enhanced dataset saved to: output/ml_patch_data/patches_20250722_160244\n", "Ready for ML training with PointNet++, DGCNN, etc.!\n", "Output directory saved to output_dir.txt\n"]}], "source": ["import pickle\n", "\n", "timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "output_dir = Path(f\"output/ml_patch_data/patches_{timestamp}\")\n", "output_dir.mkdir(parents=True, exist_ok=True)\n", "\n", "# Save splits separately\n", "datasets = {\n", "    'train': (train_patches, train_meta),\n", "    'val': (val_patches, val_meta), \n", "    'test': (test_patches, test_meta)\n", "}\n", "\n", "for split_name, (patches, metadata) in datasets.items():\n", "    # Save patches as pickle (efficient for numpy arrays)\n", "    patch_file = output_dir / f\"{split_name}_patches.pkl\"\n", "    with open(patch_file, 'wb') as f:\n", "        pickle.dump(patches, f)\n", "    \n", "    # Save metadata as JSON\n", "    meta_file = output_dir / f\"{split_name}_metadata.json\"\n", "    with open(meta_file, 'w') as f:\n", "        json.dump(metadata, f, indent=2)\n", "    \n", "    print(f\"Saved {split_name}: {len(patches)} patches\")\n", "\n", "# Save configuration\n", "config = {\n", "    'site_name': site_name,\n", "    'parameters': {\n", "        'patch_radius': patch_radius,\n", "        'min_points_per_patch': min_points_per_patch,\n", "        'min_confidence_threshold': MIN_CONFIDENCE_THRESHOLD,\n", "        'negative_buffer': NEGATIVE_SAMPLE_BUFFER\n", "    },\n", "    'dataset_stats': {\n", "        'total_patches': len(all_patches),\n", "        'positive_patches': len(positive_patches),\n", "        'negative_patches': len(negative_patches),\n", "        'extraction_stats': extraction_stats if 'extraction_stats' in locals() else {}\n", "    },\n", "    'timestamp': timestamp\n", "}\n", "\n", "config_file = output_dir / \"config.json\"\n", "with open(config_file, 'w') as f:\n", "    json.dump(config, f, indent=2)\n", "\n", "print(f\"\\nEnhanced dataset saved to: {output_dir}\")\n", "print(f\"Ready for ML training with PointNet++, DGCNN, etc.!\")\n", "\n", "# Save the output directory path for the next notebook\n", "with open(\"output_dir.txt\", \"w\") as f:\n", "    f.write(str(output_dir))\n", "print(f\"Output directory saved to output_dir.txt\")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Summary\n", "\n", "Data preparation is now complete! Here's what we accomplished:\n", "\n", "1. **Loaded point cloud data** with spatial coordinates\n", "2. **Extracted pile locations** from IFC metadata\n", "3. **Created positive samples** by extracting point patches around pile locations\n", "4. **Created negative samples** by extracting patches from non-pile areas\n", "5. **Saved all data** for analysis in the next notebook\n", "\n", "The prepared data is now ready for feature analysis and rule-based classification in the next notebook: `02_pile_detection_analysis_validation.ipynb`"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}