!python -m pip install geopandas

# Import required libraries
import sys
import numpy as np
import pandas as pd
import open3d as o3d
from pathlib import Path
from datetime import datetime
import json
import warnings
warnings.filterwarnings('ignore')

from scipy.spatial import cKDTree
import geopandas as gpd

import matplotlib.pyplot as plt


print("Libraries imported successfully")

# Setup notebook path and import shared utilities
current_path = Path.cwd()
while current_path.name != "notebooks":
    if current_path.parent == current_path:
        raise RuntimeError("Could not find 'notebooks' directory")
    current_path = current_path.parent

notebooks_root = current_path
if str(notebooks_root) not in sys.path:
    sys.path.insert(0, str(notebooks_root))

from shared.config import get_data_path, get_processed_data_path, find_latest_file
print(f"Notebooks root: {notebooks_root}")

# Configuration - These are the main settings for our analysis
site_name = "trino_enel"  # Name of the construction site
ground_method = "ransac_pmf"  # Ground segmentation method used

BASE_DIR = Path.cwd()
DATA_DIR = BASE_DIR / "data"
OUTPUT_DIR = BASE_DIR / "output" / "ml_patch_data"
OUTPUT_DIR.mkdir(parents=True, exist_ok=True)

# Patch Extraction Settings
PATCH_RADIUS = 5.0  # Keep same radius
min_points_per_patch = 30  # REDUCED from 100 - matches your data! 
max_positive_samples = 1125  # Match your available matched piles 
max_negative_samples = 1125  # Balance positive/negative samples 

# Quality filtering settings - RELAXED for better coverage 
MIN_CONFIDENCE_THRESHOLD = 'medium'  # Keep medium+
MAX_MATCH_DISTANCE = 15.0  # Keep same
MAX_Z_VARIATION = 1.5  # meters
NEGATIVE_SAMPLE_BUFFER = 15.0  # Slightly reduced 


print("Configuration:")
print(f"  Site: {site_name}")
print(f"  Ground method: {ground_method}")
print(f"  Patch radius: {PATCH_RADIUS} meters")
print(f"  Min points per patch: {min_points_per_patch}")
print(f"  Max positive samples: {max_positive_samples}")
print(f"  Max negative samples: {max_negative_samples}")

# Load aligned point cloud (.las or .laz)
alignment_path = get_processed_data_path(site_name, "gcp_alignment_z_corrected") / ground_method / "filtered"
pointcloud_path = find_latest_file(alignment_path, "*_gcp_aligned_z_corrected_filtered.ply")

# If filtered version doesn't exist, use the non-filtered version
if not pointcloud_path.exists():
    print("Filtered version not found, using non-filtered version...")
    alignment_path = get_processed_data_path(site_name, "gcp_alignment_z_corrected") / ground_method
    point_cloud_file = find_latest_file(alignment_path, "*_gcp_aligned_z_corrected.ply")

print(f"Loading from: {pointcloud_path}")

# Load the point cloud using Open3D
point_cloud = o3d.io.read_point_cloud(str(pointcloud_path))
points = np.asarray(point_cloud.points)

print(f"Successfully loaded {len(points):,} points")


# Examine the point cloud data
print("Point cloud information:")
print(f"  Number of points: {len(points):,}")
print(f"  Data shape: {points.shape}")
print(f"  Data type: {points.dtype}")

print("\nCoordinate ranges:")
for i, axis in enumerate(['X', 'Y', 'Z']):
    min_val = points[:, i].min()
    max_val = points[:, i].max()
    range_val = max_val - min_val
    print(f"  {axis}: {min_val:.2f} to {max_val:.2f} (range: {range_val:.2f} meters)")

print("\nFirst 5 points:")
for i in range(min(5, len(points))):
    print(f"  Point {i+1}: X={points[i,0]:.2f}, Y={points[i,1]:.2f}, Z={points[i,2]:.2f}")


# Optional: quick scatter plot (subsampled)
sample_idx = np.random.choice(points.shape[0], size=50000, replace=False) if points.shape[0] > 50000 else np.arange(points.shape[0])
sampled = points[sample_idx]

fig = plt.figure(figsize=(8,6))
ax = fig.add_subplot(111, projection='3d')
ax.scatter(sampled[:,0], sampled[:,1], sampled[:,2], s=0.1, alpha=0.5)
ax.set_title("Aligned Point Cloud Preview")
plt.show()


# Load IFC metadata containing pile information
print("Loading IFC metadata...")

ifc_metadata_path = get_processed_data_path(site_name, "ifc_metadata")
metadata_file = find_latest_file(ifc_metadata_path, "*enhanced_metadata.csv")

print(f"Loading from: {metadata_file}")
ifc_data = pd.read_csv(metadata_file)
print(f"Loaded IFC pile metadata: {ifc_data.shape[0]} piles")
display(ifc_data.head())

# Convert IFC DataFrame to NumPy arrays for downstream processing
pile_xyz_ifc = ifc_data[['X', 'Y', 'Z']].values
pile_ids = ifc_data['Name'].tolist()  # or 'GlobalId' if preferred

print(f"Prepared {len(pile_xyz_ifc)} pile centers for patch extraction.")

# Create a spatial index (KD-Tree) for fast point searching
print("Creating spatial index for point cloud...")

# Build KD-Tree for fast spatial queries
point_tree = cKDTree(points)
print(f"Spatial index created for {len(points):,} points")

# Test it on the first pile to confirm functionality
if len(pile_xyz_ifc) > 0:
    test_pile = pile_xyz_ifc[0]
    test_indices = point_tree.query_ball_point(test_pile, PATCH_RADIUS)
    print(f"\nTest query around first pile location:")
    print(f"  Pile location: X={test_pile[0]:.2f}, Y={test_pile[1]:.2f}, Z={test_pile[2]:.2f}")
    print(f"  Points within {PATCH_RADIUS}m radius: {len(test_indices)}")


import geopandas as gpd
import numpy as np

# === Step 1: Load KML ===
kml_path = get_data_path(site_name, "raw") / "kml" / "pile.kml"
gdf_kml = gpd.read_file(kml_path, driver='KML')

# Check data loaded
print(f"Loaded {len(gdf_kml)} features from KML.")
print("Columns:", gdf_kml.columns)
display(gdf_kml.head())

# === Step 2: Handle Geometry ===
# Ensure geometries are valid and convert to 2D
gdf_kml = gdf_kml[gdf_kml.geometry.notnull()].copy()
gdf_kml['geometry'] = gdf_kml.geometry.apply(lambda geom: geom.centroid if not geom.geom_type == 'Point' else geom)

# === Step 3: Convert to UTM Zone ===
# Optional: convert to projected UTM CRS for accurate distance (based on lat/lon)
gdf_kml = gdf_kml.set_crs(epsg=4326)  # assuming WGS84
gdf_kml_utm = gdf_kml.to_crs(epsg=32644)  # ← adjust UTM zone as per your site

# === Step 4: Assign pile IDs ===
gdf_kml_utm['pile_id'] = gdf_kml_utm['Name'].fillna("Pile_Unknown")

# === Step 5: Extract Coordinates ===
pile_ids_kml = gdf_kml_utm['pile_id'].tolist()
pile_xyz_kml = np.stack([
    gdf_kml_utm.geometry.x.values,
    gdf_kml_utm.geometry.y.values,
    np.zeros(len(gdf_kml_utm))  # Dummy Z; replace later if needed
], axis=1)

print(f"Extracted {len(pile_ids_kml)} pile locations.")
print("Sample pile XYZ:\n", pile_xyz_kml[:3])

# === Step 6: Extract Regions (e.g., circular buffers) ===
BUFFER_RADIUS_METERS = 2.0  # radius in meters

# Create circular regions (buffer polygons)
gdf_kml_utm['geometry_region'] = gdf_kml_utm.geometry.buffer(BUFFER_RADIUS_METERS)

# Optional: visualize or export to verify
print("Sample buffered region geometry:")
pile_regions = gdf_kml_utm[['pile_id', 'geometry_region']].copy()
pile_regions = pile_regions.set_geometry('geometry_region')
display(pile_regions.head())


# Step: Coordinate System Harmonization
print("=== COORDINATE SYSTEM HARMONIZATION ===")

# Ensure IFC coordinates are in same CRS as point cloud
# Assuming point cloud is in UTM (adjust EPSG code as needed)
TARGET_EPSG = 32632  # Adjust for your UTM zone

# IFC data might already be in correct CRS - verify this
print("IFC pile coordinate ranges:")
print(f"  X: {pile_xyz_ifc[:, 0].min():.2f} to {pile_xyz_ifc[:, 0].max():.2f}")
print(f"  Y: {pile_xyz_ifc[:, 1].min():.2f} to {pile_xyz_ifc[:, 1].max():.2f}")
print(f"  Z: {pile_xyz_ifc[:, 2].min():.2f} to {pile_xyz_ifc[:, 2].max():.2f}")

print("\nKML pile coordinate ranges (UTM):")
print(f"  X: {pile_xyz_kml[:, 0].min():.2f} to {pile_xyz_kml[:, 0].max():.2f}")
print(f"  Y: {pile_xyz_kml[:, 1].min():.2f} to {pile_xyz_kml[:, 1].max():.2f}")

print("\nPoint cloud coordinate ranges:")
print(f"  X: {points[:, 0].min():.2f} to {points[:, 0].max():.2f}")
print(f"  Y: {points[:, 1].min():.2f} to {points[:, 1].max():.2f}")
print(f"  Z: {points[:, 2].min():.2f} to {points[:, 2].max():.2f}")


# Addresses coordinate system issues and misalignment problems

print("=== DIAGNOSING KML COORDINATE ISSUES ===")

# First, let's examine the original KML coordinates before any transformation
kml_path = get_data_path(site_name, "raw") / "kml" / "pile.kml"
gdf_kml_original = gpd.read_file(kml_path, driver='KML')

print("Original KML data (first 5 records):")
for i in range(min(5, len(gdf_kml_original))):
    geom = gdf_kml_original.iloc[i].geometry
    if hasattr(geom, 'centroid'):
        centroid = geom.centroid
        print(f"  Record {i}: Lon={centroid.x:.6f}, Lat={centroid.y:.6f}")
    else:
        print(f"  Record {i}: Lon={geom.x:.6f}, Lat={geom.y:.6f}")

# Check if coordinates are in lat/lon (should be around 8.17°E, 45.25°N for Italy)
sample_geom = gdf_kml_original.iloc[0].geometry
if hasattr(sample_geom, 'centroid'):
    sample_x, sample_y = sample_geom.centroid.x, sample_geom.centroid.y
else:
    sample_x, sample_y = sample_geom.x, sample_geom.y

print(f"\nSample coordinates: {sample_x:.6f}, {sample_y:.6f}")

if abs(sample_x) < 180 and abs(sample_y) < 90:
    print("✓ KML coordinates appear to be in WGS84 lat/lon")
    coords_type = "latlon"
else:
    print("✗ KML coordinates are NOT in standard lat/lon format")
    coords_type = "unknown"

print(f"KML coordinate type: {coords_type}")


# COORDINATE SYSTEM DETECTIVE WORK
print("\n=== COORDINATE SYSTEM ANALYSIS ===")

# Check what CRS the point cloud is actually in
print("Point cloud coordinate analysis:")
print(f"X range: {points[:, 0].min():.2f} to {points[:, 0].max():.2f}")
print(f"Y range: {points[:, 1].min():.2f} to {points[:, 1].max():.2f}")

# These coordinates (435k, 5010k) suggest UTM Zone 32N for Italy
if 400000 <= points[:, 0].min() <= 800000 and 5000000 <= points[:, 1].min() <= 6000000:
    print("✓ Point cloud appears to be in UTM Zone 32N (EPSG:32632)")
    target_epsg = 32632
else:
    print("? Point cloud CRS unclear - manual verification needed")
    target_epsg = 32632  # Best guess for Italy

print(f"Using target CRS: EPSG:{target_epsg}")


print("\n=== CORRECTED KML COORDINATE TRANSFORMATION ===")

if coords_type == "latlon":
    # Process KML correctly
    gdf_kml_clean = gdf_kml_original.copy()
    
    # Ensure we have valid geometries and convert to centroids
    gdf_kml_clean = gdf_kml_clean[gdf_kml_clean.geometry.notnull()].copy()
    gdf_kml_clean['geometry'] = gdf_kml_clean.geometry.apply(
        lambda geom: geom.centroid if geom.geom_type != 'Point' else geom
    )
    
    # Set correct CRS and transform
    gdf_kml_clean = gdf_kml_clean.set_crs(epsg=4326)  # WGS84
    gdf_kml_utm = gdf_kml_clean.to_crs(epsg=target_epsg)
    
    # Extract coordinates
    pile_ids_kml_corrected = gdf_kml_utm['Name'].fillna("Unknown").tolist()
    pile_xyz_kml_corrected = np.stack([
        gdf_kml_utm.geometry.x.values,
        gdf_kml_utm.geometry.y.values,
        np.zeros(len(gdf_kml_utm))  # Z will be extracted from point cloud
    ], axis=1)
    
    print(f"Corrected KML coordinates (first 5):")
    for i in range(min(5, len(pile_xyz_kml_corrected))):
        x, y = pile_xyz_kml_corrected[i, 0], pile_xyz_kml_corrected[i, 1]
        print(f"  Pile {i}: X={x:.2f}, Y={y:.2f}")
    
    print(f"\nCorrected KML coordinate ranges:")
    print(f"  X: {pile_xyz_kml_corrected[:, 0].min():.2f} to {pile_xyz_kml_corrected[:, 0].max():.2f}")
    print(f"  Y: {pile_xyz_kml_corrected[:, 1].min():.2f} to {pile_xyz_kml_corrected[:, 1].max():.2f}")
    
    # Check if corrected coordinates are within point cloud bounds
    pc_bounds = {
        'x_min': points[:, 0].min(), 'x_max': points[:, 0].max(),
        'y_min': points[:, 1].min(), 'y_max': points[:, 1].max()
    }
    
    within_bounds = (
        (pile_xyz_kml_corrected[:, 0] >= pc_bounds['x_min']) & 
        (pile_xyz_kml_corrected[:, 0] <= pc_bounds['x_max']) &
        (pile_xyz_kml_corrected[:, 1] >= pc_bounds['y_min']) & 
        (pile_xyz_kml_corrected[:, 1] <= pc_bounds['y_max'])
    )
    
    print(f"\nKML piles within point cloud bounds: {within_bounds.sum()}/{len(pile_xyz_kml_corrected)}")
    
else:
    print("Cannot process KML - coordinates are not in recognizable format")
    pile_xyz_kml_corrected = None
    pile_ids_kml_corrected = None


# Step: Extract Z-coordinates for KML piles from point cloud
print("=== Z-COORDINATE EXTRACTION ===")

if pile_xyz_kml_corrected is not None:
    print("\n=== EXTRACTING Z-COORDINATES FOR CORRECTED KML ===")
    
    def extract_z_from_pointcloud_robust(xy_coords, points, search_radius=5.0):
        """Robust Z extraction with multiple fallback strategies"""
        point_tree_2d = cKDTree(points[:, :2])
        z_coords = []
        extraction_stats = {'direct': 0, 'expanded': 0, 'fallback': 0}
        
        for i, (x, y) in enumerate(xy_coords):
            # Try normal radius first
            indices = point_tree_2d.query_ball_point([x, y], search_radius)
            
            if len(indices) >= 5:  # Good coverage
                z_coord = np.median(points[indices, 2])
                extraction_stats['direct'] += 1
            elif len(indices) > 0:  # Some points found
                z_coord = np.mean(points[indices, 2])
                extraction_stats['direct'] += 1
            else:
                # Expand search radius
                indices = point_tree_2d.query_ball_point([x, y], search_radius * 3)
                if len(indices) > 0:
                    z_coord = np.median(points[indices, 2])
                    extraction_stats['expanded'] += 1
                else:
                    # Use ground level estimate
                    z_coord = np.percentile(points[:, 2], 10)  # 10th percentile as ground
                    extraction_stats['fallback'] += 1
            
            z_coords.append(z_coord)
            
            if (i + 1) % 100 == 0:
                print(f"  Processed {i + 1}/{len(xy_coords)} KML piles...")
        
        print(f"Z-extraction stats: {extraction_stats}")
        return np.array(z_coords)
    
    # Only process piles within bounds
    within_bounds_mask = within_bounds
    if within_bounds.sum() > 0:
        kml_coords_in_bounds = pile_xyz_kml_corrected[within_bounds_mask]
        kml_z_coords = extract_z_from_pointcloud_robust(kml_coords_in_bounds[:, :2], points)
        pile_xyz_kml_corrected[within_bounds_mask, 2] = kml_z_coords
        
        print(f"Extracted Z for {len(kml_z_coords)} KML piles within bounds")
        print(f"Z range: {kml_z_coords.min():.2f} to {kml_z_coords.max():.2f}")
    else:
        print("No KML piles within point cloud bounds - cannot extract Z coordinates")


def match_pile_locations_improved(ifc_coords, kml_coords, ifc_ids, kml_ids, max_distance=10.0):
        """Improved pile matching with better distance tolerance"""
        ifc_tree = cKDTree(ifc_coords[:, :2])
        
        matches = []
        unmatched_ifc = set(range(len(ifc_coords)))
        unmatched_kml = set(range(len(kml_coords)))
        
        # Sort KML piles by distance to closest IFC pile for better matching
        kml_distances = []
        for kml_idx, kml_coord in enumerate(kml_coords):
            distance, _ = ifc_tree.query(kml_coord[:2])
            kml_distances.append((distance, kml_idx))
        
        kml_distances.sort()  # Process closest matches first
        
        for distance, kml_idx in kml_distances:
            if kml_idx in unmatched_kml and distance <= max_distance:
                kml_coord = kml_coords[kml_idx]
                _, ifc_idx = ifc_tree.query(kml_coord[:2])
                
                if ifc_idx in unmatched_ifc:
                    matches.append({
                        'ifc_idx': ifc_idx,
                        'kml_idx': kml_idx,
                        'ifc_id': ifc_ids[ifc_idx],
                        'kml_id': kml_ids[kml_idx],
                        'distance': distance,
                        'ifc_coords': ifc_coords[ifc_idx],
                        'kml_coords': kml_coord
                    })
                    unmatched_ifc.remove(ifc_idx)
                    unmatched_kml.remove(kml_idx)
        
        return matches, list(unmatched_ifc), list(unmatched_kml)
    
 

#  PILE MATCHING AND HARMONIZATION ⭐
def create_harmonized_dataset(ifc_coords, ifc_ids, kml_coords=None, kml_ids=None, 
                            within_bounds_mask=None, max_distance=15.0):
    """
    Create harmonized pile dataset with automatic matching and quality assignment
    """
    harmonized_piles = []
    
    # Handle case where KML data is available and valid
    if kml_coords is not None and within_bounds_mask is not None and within_bounds_mask.sum() > 0:
        print(f"=== PILE MATCHING: {len(ifc_coords)} IFC vs {within_bounds_mask.sum()} valid KML piles ===")
        
        # Filter valid KML data
        kml_coords_valid = kml_coords[within_bounds_mask]
        kml_ids_valid = [kml_ids[i] for i in range(len(kml_ids)) if within_bounds_mask[i]]
        
        # Perform matching
        matches, unmatched_ifc, unmatched_kml = match_pile_locations_improved(
            ifc_coords, kml_coords_valid, ifc_ids, kml_ids_valid, max_distance
        )
        
        print(f"Matched pairs: {len(matches)}")
        print(f"  -> Unmatched IFC: {len(unmatched_ifc)}")  
        print(f"  -> Unmatched KML: {len(unmatched_kml)}")
        
        if matches:
            avg_distance = np.mean([m['distance'] for m in matches])
            print(f"  -> Average match distance: {avg_distance:.3f}m")
        
        # Build harmonized dataset
        harmonized_piles.extend([
            create_pile_record(m, 'matched', 'high') for m in matches
        ])
        harmonized_piles.extend([
            create_pile_record({'ifc_idx': idx, 'ifc_id': ifc_ids[idx], 
                              'ifc_coords': ifc_coords[idx]}, 'ifc_only', 'medium') 
            for idx in unmatched_ifc
        ])
        harmonized_piles.extend([
            create_pile_record({'kml_idx': idx, 'kml_id': kml_ids_valid[idx],
                              'kml_coords': kml_coords_valid[idx]}, 'kml_only', 'low')
            for idx in unmatched_kml  
        ])
        
    else:
        # IFC-only mode
        print(f"=== IFC-ONLY MODE: {len(ifc_coords)} piles ===")
        harmonized_piles.extend([
            create_pile_record({'ifc_idx': i, 'ifc_id': ifc_ids[i], 
                              'ifc_coords': ifc_coords[i]}, 'ifc_only', 'medium')
            for i in range(len(ifc_coords))
        ])
    
    df = pd.DataFrame(harmonized_piles)
    print(f"\n=== Harmonized dataset: {len(df)} piles ===")
    print(f"  Sources: {dict(df['source'].value_counts())}")
    
    return df

def create_pile_record(match_data, source_type, confidence):
    """Create standardized pile record for matched, IFC-only, or KML-only piles."""
    
    if source_type not in ['matched', 'ifc_only', 'kml_only']:
        raise ValueError(f"Invalid source_type: {source_type}")
    
    record = {
        'pile_id': match_data.get('ifc_id') if source_type != 'kml_only' else f"KML_{match_data['kml_id']}",
        'source': source_type,
        'confidence': confidence,
        'match_distance': match_data.get('distance') if source_type == 'matched' else None,
        'kml_match': match_data.get('kml_id') if source_type != 'ifc_only' else None,
    }
    
    # Extract coordinates with fallback handling
    try:
        coords = match_data['ifc_coords'] if source_type != 'kml_only' else match_data['kml_coords']
        record.update({'x': coords[0], 'y': coords[1], 'z': coords[2]})
    except (KeyError, IndexError, TypeError) as e:
        raise ValueError(f"Invalid coordinate data for {source_type}: {e}")
    
    return record

if pile_xyz_kml_corrected is not None and within_bounds.sum() > 0:
    harmonized_df_corrected = create_harmonized_dataset(
        ifc_coords=pile_xyz_ifc,
        ifc_ids=pile_ids,
        kml_coords=pile_xyz_kml_corrected, 
        kml_ids=pile_ids_kml_corrected,
        within_bounds_mask=within_bounds,
        max_distance=15.0
    )
else:
    harmonized_df_corrected = create_harmonized_dataset(
        ifc_coords=pile_xyz_ifc,
        ifc_ids=pile_ids
    )

# Save results
corrected_output_path = OUTPUT_DIR / f"{site_name}_harmonized_piles_corrected.csv"
harmonized_df_corrected.to_csv(corrected_output_path, index=False)
print(f"\n💾 Saved to: {corrected_output_path}")


# Step: Quality Control and Validation
if 'harmonized_df_corrected' in locals():
    print("\n=== FINAL QUALITY CHECK ON CORRECTED DATA ===")
    
    # Check point density for corrected harmonized piles
    harmonized_coords_corrected = harmonized_df_corrected[['x', 'y', 'z']].values
    point_counts_corrected = []
    
    for i, pile_coord in enumerate(harmonized_coords_corrected):
        indices = point_tree.query_ball_point(pile_coord, PATCH_RADIUS)
        point_counts_corrected.append(len(indices))
    
    harmonized_df_corrected['nearby_points'] = point_counts_corrected
    
    print(f"Point density stats (corrected data):")
    print(f"  Mean: {np.mean(point_counts_corrected):.1f}")
    print(f"  Median: {np.median(point_counts_corrected):.1f}")
    print(f"  Min: {np.min(point_counts_corrected)}")
    print(f"  Max: {np.max(point_counts_corrected)}")
    
    ready_for_extraction = harmonized_df_corrected[harmonized_df_corrected['nearby_points'] >= min_points_per_patch]
    print(f"\nPiles ready for patch extraction: {len(ready_for_extraction)}")
    
    if len(ready_for_extraction) > 0:
        print("SUCCESS: Harmonization corrected - piles ready for ML patch extraction!")
        display(ready_for_extraction.head())
    else:
        print("Still no piles ready for extraction - may need to reduce min_points_per_patch")
        
        # Show piles with most points
        top_piles = harmonized_df_corrected.nlargest(10, 'nearby_points')
        print("Top 10 piles by point count:")
        display(top_piles[['pile_id', 'source', 'nearby_points', 'confidence']])

print("\n=== HARMONIZATION CORRECTION COMPLETE ===")