{"cells": [{"cell_type": "markdown", "id": "851440e6", "metadata": {}, "source": ["# Data Preparation for Machine Learning: Pile Patch Extraction\n", "\n", "This notebook prepares structured training data for ML-based pile detection by extracting point cloud patches centered at known pile locations (positive samples) and random non-pile regions (negative samples). These patches are later used to train models like PointNet++, DGCNN, and others.\n", "\n", "---\n", "\n", "## Objectives\n", "1. Load aligned point cloud data (PLY format)\n", "2. Load PRE-HARMONIZED pile location metadata\n", "3. Extract local point cloud patches using spatial radius search\n", "4. Label patches as positive (pile) or negative (non-pile)\n", "5. Save patch-level data and metadata for training and validation\n", "6. Include coordinate system validation and quality checks\n", "\n", "---\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: July 2025  \n", "**Project**: As-Built Solar Foundation QA & Deviation Detection  \n"]}, {"cell_type": "markdown", "id": "7a969096", "metadata": {}, "source": ["## Step 1: Setup Environment\n"]}, {"cell_type": "code", "execution_count": 20, "id": "40ba6546", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: geopandas in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (1.1.1)\n", "Requirement already satisfied: numpy>=1.24 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from geopandas) (1.26.4)\n", "Requirement already satisfied: pyogrio>=0.7.2 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from geopandas) (0.11.0)\n", "Requirement already satisfied: packaging in /Users/<USER>/.local/lib/python3.11/site-packages (from geopandas) (25.0)\n", "Requirement already satisfied: pandas>=2.0.0 in /Users/<USER>/.local/lib/python3.11/site-packages (from geopandas) (2.3.0)\n", "Requirement already satisfied: pyproj>=3.5.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from geopandas) (3.7.1)\n", "Requirement already satisfied: shapely>=2.0.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from geopandas) (2.1.1)\n", "Requirement already satisfied: python-dateutil>=2.8.2 in /Users/<USER>/.local/lib/python3.11/site-packages (from pandas>=2.0.0->geopandas) (2.9.0.post0)\n", "Requirement already satisfied: pytz>=2020.1 in /Users/<USER>/.local/lib/python3.11/site-packages (from pandas>=2.0.0->geopandas) (2025.2)\n", "Requirement already satisfied: tzdata>=2022.7 in /Users/<USER>/.local/lib/python3.11/site-packages (from pandas>=2.0.0->geopandas) (2025.2)\n", "Requirement already satisfied: certifi in /Users/<USER>/.local/lib/python3.11/site-packages (from pyogrio>=0.7.2->geopandas) (2025.6.15)\n", "Requirement already satisfied: six>=1.5 in /Users/<USER>/.local/lib/python3.11/site-packages (from python-dateutil>=2.8.2->pandas>=2.0.0->geopandas) (1.17.0)\n"]}], "source": ["!python -m pip install geopandas"]}, {"cell_type": "code", "execution_count": 21, "id": "247f3ded", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Libraries imported successfully\n"]}], "source": ["# Import required libraries\n", "import sys\n", "import numpy as np\n", "import pandas as pd\n", "import open3d as o3d\n", "from pathlib import Path\n", "from datetime import datetime\n", "import json\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "from scipy.spatial import cKDTree\n", "import geopandas as gpd\n", "\n", "import matplotlib.pyplot as plt\n", "\n", "\n", "print(\"Libraries imported successfully\")"]}, {"cell_type": "code", "execution_count": 22, "id": "6d1e83f8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Notebooks root: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/notebooks\n"]}], "source": ["# Setup notebook path and import shared utilities\n", "current_path = Path.cwd()\n", "while current_path.name != \"notebooks\":\n", "    if current_path.parent == current_path:\n", "        raise RuntimeError(\"Could not find 'notebooks' directory\")\n", "    current_path = current_path.parent\n", "\n", "notebooks_root = current_path\n", "if str(notebooks_root) not in sys.path:\n", "    sys.path.insert(0, str(notebooks_root))\n", "\n", "from shared.config import get_data_path, get_processed_data_path, find_latest_file\n", "print(f\"Notebooks root: {notebooks_root}\")"]}, {"cell_type": "code", "execution_count": 23, "id": "a2dc8c94", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Configuration:\n", "  Site: trino_enel\n", "  Ground method: ransac_pmf\n", "  Patch radius: 5.0 meters\n", "  Min points per patch: 30\n", "  Max positive samples: 1125\n", "  Max negative samples: 1125\n"]}], "source": ["# Configuration - These are the main settings for our analysis\n", "site_name = \"trino_enel\"  # Name of the construction site\n", "ground_method = \"ransac_pmf\"  # Ground segmentation method used\n", "\n", "BASE_DIR = Path.cwd()\n", "DATA_DIR = BASE_DIR / \"data\"\n", "OUTPUT_DIR = BASE_DIR / \"output\" / \"ml_patch_data\"\n", "OUTPUT_DIR.mkdir(parents=True, exist_ok=True)\n", "\n", "# Patch Extraction Settings\n", "PATCH_RADIUS = 5.0  # Keep same radius\n", "min_points_per_patch = 30  # REDUCED from 100 - matches your data! \n", "max_positive_samples = 1125  # Match your available matched piles \n", "max_negative_samples = 1125  # Balance positive/negative samples \n", "\n", "# Quality filtering settings - RELAXED for better coverage \n", "MIN_CONFIDENCE_THRESHOLD = 'medium'  # Keep medium+\n", "MAX_MATCH_DISTANCE = 15.0  # Keep same\n", "MAX_Z_VARIATION = 1.5  # meters\n", "NEGATIVE_SAMPLE_BUFFER = 15.0  # Slightly reduced \n", "\n", "\n", "print(\"Configuration:\")\n", "print(f\"  Site: {site_name}\")\n", "print(f\"  Ground method: {ground_method}\")\n", "print(f\"  Patch radius: {PATCH_RADIUS} meters\")\n", "print(f\"  Min points per patch: {min_points_per_patch}\")\n", "print(f\"  Max positive samples: {max_positive_samples}\")\n", "print(f\"  Max negative samples: {max_negative_samples}\")"]}, {"cell_type": "code", "execution_count": 24, "id": "c74281ff", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading from: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/gcp_alignment_z_corrected/ransac_pmf/filtered/trino_enel_gcp_aligned_z_corrected_filtered.ply\n", "Successfully loaded 517,002 points\n"]}], "source": ["# Load aligned point cloud (.las or .laz)\n", "alignment_path = get_processed_data_path(site_name, \"gcp_alignment_z_corrected\") / ground_method / \"filtered\"\n", "pointcloud_path = find_latest_file(alignment_path, \"*_gcp_aligned_z_corrected_filtered.ply\")\n", "\n", "# If filtered version doesn't exist, use the non-filtered version\n", "if not pointcloud_path.exists():\n", "    print(\"Filtered version not found, using non-filtered version...\")\n", "    alignment_path = get_processed_data_path(site_name, \"gcp_alignment_z_corrected\") / ground_method\n", "    point_cloud_file = find_latest_file(alignment_path, \"*_gcp_aligned_z_corrected.ply\")\n", "\n", "print(f\"Loading from: {pointcloud_path}\")\n", "\n", "# Load the point cloud using Open3D\n", "point_cloud = o3d.io.read_point_cloud(str(pointcloud_path))\n", "points = np.asarray(point_cloud.points)\n", "\n", "print(f\"Successfully loaded {len(points):,} points\")\n"]}, {"cell_type": "code", "execution_count": 25, "id": "5d7eae21", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Point cloud information:\n", "  Number of points: 517,002\n", "  Data shape: (517002, 3)\n", "  Data type: float64\n", "\n", "Coordinate ranges:\n", "  X: 435220.30 to 436796.35 (range: 1576.05 meters)\n", "  Y: 5010812.68 to 5012553.07 (range: 1740.39 meters)\n", "  Z: 152.43 to 182.42 (range: 29.98 meters)\n", "\n", "First 5 points:\n", "  Point 1: X=435344.09, Y=5011988.43, Z=157.60\n", "  Point 2: X=436578.57, Y=5012091.24, Z=154.69\n", "  Point 3: X=436464.55, Y=5011306.84, Z=155.10\n", "  Point 4: X=436146.57, Y=5010913.93, Z=154.13\n", "  Point 5: X=435563.17, Y=5011417.41, Z=158.16\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Examine the point cloud data\n", "print(\"Point cloud information:\")\n", "print(f\"  Number of points: {len(points):,}\")\n", "print(f\"  Data shape: {points.shape}\")\n", "print(f\"  Data type: {points.dtype}\")\n", "\n", "print(\"\\nCoordinate ranges:\")\n", "for i, axis in enumerate(['X', 'Y', 'Z']):\n", "    min_val = points[:, i].min()\n", "    max_val = points[:, i].max()\n", "    range_val = max_val - min_val\n", "    print(f\"  {axis}: {min_val:.2f} to {max_val:.2f} (range: {range_val:.2f} meters)\")\n", "\n", "print(\"\\nFirst 5 points:\")\n", "for i in range(min(5, len(points))):\n", "    print(f\"  Point {i+1}: X={points[i,0]:.2f}, Y={points[i,1]:.2f}, Z={points[i,2]:.2f}\")\n", "\n", "\n", "# Optional: quick scatter plot (subsampled)\n", "sample_idx = np.random.choice(points.shape[0], size=50000, replace=False) if points.shape[0] > 50000 else np.arange(points.shape[0])\n", "sampled = points[sample_idx]\n", "\n", "fig = plt.figure(figsize=(8,6))\n", "ax = fig.add_subplot(111, projection='3d')\n", "ax.scatter(sampled[:,0], sampled[:,1], sampled[:,2], s=0.1, alpha=0.5)\n", "ax.set_title(\"Aligned Point Cloud Preview\")\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 26, "id": "bec2dfff", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading IFC metadata...\n", "Loading from: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/ifc_metadata/GRE.EEC.S.00.IT.P.14353.00.265_enhanced_metadata.csv\n", "Loaded IFC pile metadata: 14460 piles\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>GlobalId</th>\n", "      <th>Name</th>\n", "      <th>Type</th>\n", "      <th>Description</th>\n", "      <th>Tag</th>\n", "      <th>Site_Latitude</th>\n", "      <th>Site_Longitude</th>\n", "      <th>Site_Elevation</th>\n", "      <th>X</th>\n", "      <th>Y</th>\n", "      <th>Z</th>\n", "      <th>GeometryExtracted</th>\n", "      <th>Longitude</th>\n", "      <th>Latitude</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1u7AZf3On2lwsljDdawZWm</td>\n", "      <td>TRPL_Tracker Pile:TRPL_Tracker Pile:952577</td>\n", "      <td>IfcColumn</td>\n", "      <td>NaN</td>\n", "      <td>952577</td>\n", "      <td>45.257</td>\n", "      <td>8.184</td>\n", "      <td>NaN</td>\n", "      <td>435751.684</td>\n", "      <td>5012179.151</td>\n", "      <td>158.688</td>\n", "      <td>True</td>\n", "      <td>8.181</td>\n", "      <td>45.26</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1u7AZf3On2lwsljDdawZWp</td>\n", "      <td>TRPL_Tracker Pile:TRPL_Tracker Pile:952578</td>\n", "      <td>IfcColumn</td>\n", "      <td>NaN</td>\n", "      <td>952578</td>\n", "      <td>45.257</td>\n", "      <td>8.184</td>\n", "      <td>NaN</td>\n", "      <td>435751.684</td>\n", "      <td>5012187.444</td>\n", "      <td>158.688</td>\n", "      <td>True</td>\n", "      <td>8.181</td>\n", "      <td>45.26</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1u7AZf3On2lwsljDdawZWo</td>\n", "      <td>TRPL_Tracker Pile:TRPL_Tracker Pile:952579</td>\n", "      <td>IfcColumn</td>\n", "      <td>NaN</td>\n", "      <td>952579</td>\n", "      <td>45.257</td>\n", "      <td>8.184</td>\n", "      <td>NaN</td>\n", "      <td>435751.684</td>\n", "      <td>5012195.736</td>\n", "      <td>158.688</td>\n", "      <td>True</td>\n", "      <td>8.181</td>\n", "      <td>45.26</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1u7AZf3On2lwsljDdawZWr</td>\n", "      <td>TRPL_Tracker Pile:TRPL_Tracker Pile:952580</td>\n", "      <td>IfcColumn</td>\n", "      <td>NaN</td>\n", "      <td>952580</td>\n", "      <td>45.257</td>\n", "      <td>8.184</td>\n", "      <td>NaN</td>\n", "      <td>435751.684</td>\n", "      <td>5012170.859</td>\n", "      <td>158.688</td>\n", "      <td>True</td>\n", "      <td>8.181</td>\n", "      <td>45.26</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1u7AZf3On2lwsljDdawZWq</td>\n", "      <td>TRPL_Tracker Pile:TRPL_Tracker Pile:952581</td>\n", "      <td>IfcColumn</td>\n", "      <td>NaN</td>\n", "      <td>952581</td>\n", "      <td>45.257</td>\n", "      <td>8.184</td>\n", "      <td>NaN</td>\n", "      <td>435751.684</td>\n", "      <td>5012204.029</td>\n", "      <td>158.688</td>\n", "      <td>True</td>\n", "      <td>8.181</td>\n", "      <td>45.26</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                 GlobalId                                        Name  \\\n", "0  1u7AZf3On2lwsljDdawZWm  TRPL_Tracker Pile:TRPL_Tracker Pile:952577   \n", "1  1u7AZf3On2lwsljDdawZWp  TRPL_Tracker Pile:TRPL_Tracker Pile:952578   \n", "2  1u7AZf3On2lwsljDdawZWo  TRPL_Tracker Pile:TRPL_Tracker Pile:952579   \n", "3  1u7AZf3On2lwsljDdawZWr  TRPL_Tracker Pile:TRPL_Tracker Pile:952580   \n", "4  1u7AZf3On2lwsljDdawZWq  TRPL_Tracker Pile:TRPL_Tracker Pile:952581   \n", "\n", "        Type  Description     Tag  Site_Latitude  Site_Longitude  \\\n", "0  IfcColumn          NaN  952577         45.257           8.184   \n", "1  IfcColumn          NaN  952578         45.257           8.184   \n", "2  IfcColumn          NaN  952579         45.257           8.184   \n", "3  IfcColumn          NaN  952580         45.257           8.184   \n", "4  IfcColumn          NaN  952581         45.257           8.184   \n", "\n", "   Site_Elevation           X            Y        Z  GeometryExtracted  \\\n", "0             NaN  435751.684  5012179.151  158.688               True   \n", "1             NaN  435751.684  5012187.444  158.688               True   \n", "2             NaN  435751.684  5012195.736  158.688               True   \n", "3             NaN  435751.684  5012170.859  158.688               True   \n", "4             NaN  435751.684  5012204.029  158.688               True   \n", "\n", "   Longitude  Latitude  \n", "0      8.181     45.26  \n", "1      8.181     45.26  \n", "2      8.181     45.26  \n", "3      8.181     45.26  \n", "4      8.181     45.26  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Prepared 14460 pile centers for patch extraction.\n", "Creating spatial index for point cloud...\n"]}], "source": ["# Load IFC metadata containing pile information\n", "print(\"Loading IFC metadata...\")\n", "\n", "ifc_metadata_path = get_processed_data_path(site_name, \"ifc_metadata\")\n", "metadata_file = find_latest_file(ifc_metadata_path, \"*enhanced_metadata.csv\")\n", "\n", "print(f\"Loading from: {metadata_file}\")\n", "ifc_data = pd.read_csv(metadata_file)\n", "print(f\"Loaded IFC pile metadata: {ifc_data.shape[0]} piles\")\n", "display(ifc_data.head())\n", "\n", "# Convert IFC DataFrame to NumPy arrays for downstream processing\n", "pile_xyz_ifc = ifc_data[['X', 'Y', 'Z']].values\n", "pile_ids = ifc_data['Name'].tolist()  # or 'GlobalId' if preferred\n", "\n", "print(f\"Prepared {len(pile_xyz_ifc)} pile centers for patch extraction.\")\n", "\n", "# Create a spatial index (KD-Tree) for fast point searching\n", "print(\"Creating spatial index for point cloud...\")"]}, {"cell_type": "code", "execution_count": 27, "id": "b727e21a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Spatial index created for 517,002 points\n", "\n", "Test query around first pile location:\n", "  Pile location: X=435751.68, Y=5012179.15, Z=158.69\n", "  Points within 5.0m radius: 0\n"]}], "source": ["# Build KD-Tree for fast spatial queries\n", "point_tree = cKDTree(points)\n", "print(f\"Spatial index created for {len(points):,} points\")\n", "\n", "# Test it on the first pile to confirm functionality\n", "if len(pile_xyz_ifc) > 0:\n", "    test_pile = pile_xyz_ifc[0]\n", "    test_indices = point_tree.query_ball_point(test_pile, PATCH_RADIUS)\n", "    print(f\"\\nTest query around first pile location:\")\n", "    print(f\"  Pile location: X={test_pile[0]:.2f}, Y={test_pile[1]:.2f}, Z={test_pile[2]:.2f}\")\n", "    print(f\"  Points within {PATCH_RADIUS}m radius: {len(test_indices)}\")\n"]}, {"cell_type": "code", "execution_count": 28, "id": "2cf795da", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 1288 features from KML.\n", "Columns: Index(['Name', 'Description', 'geometry'], dtype='object')\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Name</th>\n", "      <th>Description</th>\n", "      <th>geometry</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td></td>\n", "      <td>Unknown Area Type</td>\n", "      <td>POLYGON Z ((8.17954 45.25947 0, 8.17956 45.259...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td></td>\n", "      <td>Unknown Area Type</td>\n", "      <td>POLYGON Z ((8.17954 45.25954 0, 8.17956 45.259...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td></td>\n", "      <td>Unknown Area Type</td>\n", "      <td>POLYGON Z ((8.17955 45.25917 0, 8.17956 45.259...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td></td>\n", "      <td>Unknown Area Type</td>\n", "      <td>POLYGON Z ((8.17955 45.25924 0, 8.17956 45.259...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td></td>\n", "      <td>Unknown Area Type</td>\n", "      <td>POLYGON Z ((8.17954 45.2593 0, 8.17956 45.2593...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  Name        Description                                           geometry\n", "0       Unknown Area Type  POLYGON Z ((8.17954 45.25947 0, 8.17956 45.259...\n", "1       Unknown Area Type  POLYGON Z ((8.17954 45.25954 0, 8.17956 45.259...\n", "2       Unknown Area Type  POLYGON Z ((8.17955 45.25917 0, 8.17956 45.259...\n", "3       Unknown Area Type  POLYGON Z ((8.17955 45.25924 0, 8.17956 45.259...\n", "4       Unknown Area Type  POLYGON Z ((8.17954 45.2593 0, 8.17956 45.2593..."]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Extracted 1288 pile locations.\n", "Sample pile XYZ:\n", " [[-4704594.16314179  8181675.48237289        0.        ]\n", " [-4704584.63201188  8181679.79138014        0.        ]\n", " [-4704635.77846063  8181657.24160812        0.        ]]\n", "Sample buffered region geometry:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>pile_id</th>\n", "      <th>geometry_region</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td></td>\n", "      <td>POLYGON ((-4704592.163 8181675.482, -4704592.1...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td></td>\n", "      <td>POLYGON ((-4704582.632 8181679.791, -4704582.6...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td></td>\n", "      <td>POLYGON ((-4704633.778 8181657.242, -4704633.7...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td></td>\n", "      <td>POLYGON ((-4704624.536 8181661.281, -4704624.5...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td></td>\n", "      <td>POLYGON ((-4704615.454 8181665.387, -4704615.4...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  pile_id                                    geometry_region\n", "0          POLYGON ((-4704592.163 8181675.482, -4704592.1...\n", "1          POLYGON ((-4704582.632 8181679.791, -4704582.6...\n", "2          POLYGON ((-4704633.778 8181657.242, -4704633.7...\n", "3          POLYGON ((-4704624.536 8181661.281, -4704624.5...\n", "4          POLYGON ((-4704615.454 8181665.387, -4704615.4..."]}, "metadata": {}, "output_type": "display_data"}], "source": ["import geopandas as gpd\n", "import numpy as np\n", "\n", "# === Step 1: Load KML ===\n", "kml_path = get_data_path(site_name, \"raw\") / \"kml\" / \"pile.kml\"\n", "gdf_kml = gpd.read_file(kml_path, driver='KML')\n", "\n", "# Check data loaded\n", "print(f\"Loaded {len(gdf_kml)} features from KML.\")\n", "print(\"Columns:\", gdf_kml.columns)\n", "display(gdf_kml.head())\n", "\n", "# === Step 2: Handle Geometry ===\n", "# Ensure geometries are valid and convert to 2D\n", "gdf_kml = gdf_kml[gdf_kml.geometry.notnull()].copy()\n", "gdf_kml['geometry'] = gdf_kml.geometry.apply(lambda geom: geom.centroid if not geom.geom_type == 'Point' else geom)\n", "\n", "# === Step 3: Convert to UTM Zone ===\n", "# Optional: convert to projected UTM CRS for accurate distance (based on lat/lon)\n", "gdf_kml = gdf_kml.set_crs(epsg=4326)  # assuming WGS84\n", "gdf_kml_utm = gdf_kml.to_crs(epsg=32644)  # ← adjust UTM zone as per your site\n", "\n", "# === Step 4: Assign pile IDs ===\n", "gdf_kml_utm['pile_id'] = gdf_kml_utm['Name'].fillna(\"Pile_Unknown\")\n", "\n", "# === Step 5: Extract Coordinates ===\n", "pile_ids_kml = gdf_kml_utm['pile_id'].tolist()\n", "pile_xyz_kml = np.stack([\n", "    gdf_kml_utm.geometry.x.values,\n", "    gdf_kml_utm.geometry.y.values,\n", "    np.zeros(len(gdf_kml_utm))  # Dummy Z; replace later if needed\n", "], axis=1)\n", "\n", "print(f\"Extracted {len(pile_ids_kml)} pile locations.\")\n", "print(\"Sample pile XYZ:\\n\", pile_xyz_kml[:3])\n", "\n", "# === Step 6: Extract Regions (e.g., circular buffers) ===\n", "BUFFER_RADIUS_METERS = 2.0  # radius in meters\n", "\n", "# Create circular regions (buffer polygons)\n", "gdf_kml_utm['geometry_region'] = gdf_kml_utm.geometry.buffer(BUFFER_RADIUS_METERS)\n", "\n", "# Optional: visualize or export to verify\n", "print(\"Sample buffered region geometry:\")\n", "pile_regions = gdf_kml_utm[['pile_id', 'geometry_region']].copy()\n", "pile_regions = pile_regions.set_geometry('geometry_region')\n", "display(pile_regions.head())\n"]}, {"cell_type": "code", "execution_count": 29, "id": "ae0552bd", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== COORDINATE SYSTEM HARMONIZATION ===\n", "IFC pile coordinate ranges:\n", "  X: 435267.20 to 436719.95\n", "  Y: 5010900.71 to 5012462.41\n", "  Z: 154.99 to 159.52\n", "\n", "KML pile coordinate ranges (UTM):\n", "  X: -4705538.26 to -4704393.89\n", "  Y: 8180509.51 to 8181679.79\n", "\n", "Point cloud coordinate ranges:\n", "  X: 435220.30 to 436796.35\n", "  Y: 5010812.68 to 5012553.07\n", "  Z: 152.43 to 182.42\n"]}], "source": ["# Step: Coordinate System Harmonization\n", "print(\"=== COORDINATE SYSTEM HARMONIZATION ===\")\n", "\n", "# Ensure IFC coordinates are in same CRS as point cloud\n", "# Assuming point cloud is in UTM (adjust EPSG code as needed)\n", "TARGET_EPSG = 32632  # Adjust for your UTM zone\n", "\n", "# IFC data might already be in correct CRS - verify this\n", "print(\"IFC pile coordinate ranges:\")\n", "print(f\"  X: {pile_xyz_ifc[:, 0].min():.2f} to {pile_xyz_ifc[:, 0].max():.2f}\")\n", "print(f\"  Y: {pile_xyz_ifc[:, 1].min():.2f} to {pile_xyz_ifc[:, 1].max():.2f}\")\n", "print(f\"  Z: {pile_xyz_ifc[:, 2].min():.2f} to {pile_xyz_ifc[:, 2].max():.2f}\")\n", "\n", "print(\"\\nKML pile coordinate ranges (UTM):\")\n", "print(f\"  X: {pile_xyz_kml[:, 0].min():.2f} to {pile_xyz_kml[:, 0].max():.2f}\")\n", "print(f\"  Y: {pile_xyz_kml[:, 1].min():.2f} to {pile_xyz_kml[:, 1].max():.2f}\")\n", "\n", "print(\"\\nPoint cloud coordinate ranges:\")\n", "print(f\"  X: {points[:, 0].min():.2f} to {points[:, 0].max():.2f}\")\n", "print(f\"  Y: {points[:, 1].min():.2f} to {points[:, 1].max():.2f}\")\n", "print(f\"  Z: {points[:, 2].min():.2f} to {points[:, 2].max():.2f}\")\n"]}, {"cell_type": "code", "execution_count": 30, "id": "33b0f51a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== DIAGNOSING KML COORDINATE ISSUES ===\n", "Original KML data (first 5 records):\n", "  Record 0: Lon=8.179553, Lat=45.259478\n", "  Record 1: Lon=8.179552, Lat=45.259548\n", "  Record 2: Lon=8.179555, Lat=45.259175\n", "  Record 3: Lon=8.179555, Lat=45.259242\n", "  Record 4: Lon=8.179553, Lat=45.259309\n", "\n", "Sample coordinates: 8.179553, 45.259478\n", "✓ KML coordinates appear to be in WGS84 lat/lon\n", "KML coordinate type: latlon\n"]}], "source": ["# Addresses coordinate system issues and misalignment problems\n", "\n", "print(\"=== DIAGNOSING KML COORDINATE ISSUES ===\")\n", "\n", "# First, let's examine the original KML coordinates before any transformation\n", "kml_path = get_data_path(site_name, \"raw\") / \"kml\" / \"pile.kml\"\n", "gdf_kml_original = gpd.read_file(kml_path, driver='KML')\n", "\n", "print(\"Original KML data (first 5 records):\")\n", "for i in range(min(5, len(gdf_kml_original))):\n", "    geom = gdf_kml_original.iloc[i].geometry\n", "    if hasattr(geom, 'centroid'):\n", "        centroid = geom.centroid\n", "        print(f\"  Record {i}: Lon={centroid.x:.6f}, Lat={centroid.y:.6f}\")\n", "    else:\n", "        print(f\"  Record {i}: Lon={geom.x:.6f}, Lat={geom.y:.6f}\")\n", "\n", "# Check if coordinates are in lat/lon (should be around 8.17°E, 45.25°N for Italy)\n", "sample_geom = gdf_kml_original.iloc[0].geometry\n", "if hasattr(sample_geom, 'centroid'):\n", "    sample_x, sample_y = sample_geom.centroid.x, sample_geom.centroid.y\n", "else:\n", "    sample_x, sample_y = sample_geom.x, sample_geom.y\n", "\n", "print(f\"\\nSample coordinates: {sample_x:.6f}, {sample_y:.6f}\")\n", "\n", "if abs(sample_x) < 180 and abs(sample_y) < 90:\n", "    print(\"✓ KML coordinates appear to be in WGS84 lat/lon\")\n", "    coords_type = \"latlon\"\n", "else:\n", "    print(\"✗ KML coordinates are NOT in standard lat/lon format\")\n", "    coords_type = \"unknown\"\n", "\n", "print(f\"KML coordinate type: {coords_type}\")\n"]}, {"cell_type": "code", "execution_count": 31, "id": "ac3eb905", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== COORDINATE SYSTEM ANALYSIS ===\n", "Point cloud coordinate analysis:\n", "X range: 435220.30 to 436796.35\n", "Y range: 5010812.68 to 5012553.07\n", "✓ Point cloud appears to be in UTM Zone 32N (EPSG:32632)\n", "Using target CRS: EPSG:32632\n"]}], "source": ["# COORDINATE SYSTEM DETECTIVE WORK\n", "print(\"\\n=== COORDINATE SYSTEM ANALYSIS ===\")\n", "\n", "# Check what CRS the point cloud is actually in\n", "print(\"Point cloud coordinate analysis:\")\n", "print(f\"X range: {points[:, 0].min():.2f} to {points[:, 0].max():.2f}\")\n", "print(f\"Y range: {points[:, 1].min():.2f} to {points[:, 1].max():.2f}\")\n", "\n", "# These coordinates (435k, 5010k) suggest UTM Zone 32N for Italy\n", "if 400000 <= points[:, 0].min() <= 800000 and 5000000 <= points[:, 1].min() <= 6000000:\n", "    print(\"✓ Point cloud appears to be in UTM Zone 32N (EPSG:32632)\")\n", "    target_epsg = 32632\n", "else:\n", "    print(\"? Point cloud CRS unclear - manual verification needed\")\n", "    target_epsg = 32632  # Best guess for Italy\n", "\n", "print(f\"Using target CRS: EPSG:{target_epsg}\")\n"]}, {"cell_type": "code", "execution_count": 32, "id": "2c22b755", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== CORRECTED KML COORDINATE TRANSFORMATION ===\n", "Corrected KML coordinates (first 5):\n", "  Pile 0: X=435628.79, Y=5012103.14\n", "  Pile 1: X=435628.75, Y=5012110.89\n", "  Pile 2: X=435628.57, Y=5012069.48\n", "  Pile 3: X=435628.62, Y=5012076.95\n", "  Pile 4: X=435628.59, Y=5012084.33\n", "\n", "Corrected KML coordinate ranges:\n", "  X: 435628.57 to 436255.19\n", "  Y: 5011261.36 to 5012200.75\n", "\n", "KML piles within point cloud bounds: 1288/1288\n"]}], "source": ["print(\"\\n=== CORRECTED KML COORDINATE TRANSFORMATION ===\")\n", "\n", "if coords_type == \"latlon\":\n", "    # Process KML correctly\n", "    gdf_kml_clean = gdf_kml_original.copy()\n", "    \n", "    # Ensure we have valid geometries and convert to centroids\n", "    gdf_kml_clean = gdf_kml_clean[gdf_kml_clean.geometry.notnull()].copy()\n", "    gdf_kml_clean['geometry'] = gdf_kml_clean.geometry.apply(\n", "        lambda geom: geom.centroid if geom.geom_type != 'Point' else geom\n", "    )\n", "    \n", "    # Set correct CRS and transform\n", "    gdf_kml_clean = gdf_kml_clean.set_crs(epsg=4326)  # WGS84\n", "    gdf_kml_utm = gdf_kml_clean.to_crs(epsg=target_epsg)\n", "    \n", "    # Extract coordinates\n", "    pile_ids_kml_corrected = gdf_kml_utm['Name'].fillna(\"Unknown\").tolist()\n", "    pile_xyz_kml_corrected = np.stack([\n", "        gdf_kml_utm.geometry.x.values,\n", "        gdf_kml_utm.geometry.y.values,\n", "        np.zeros(len(gdf_kml_utm))  # Z will be extracted from point cloud\n", "    ], axis=1)\n", "    \n", "    print(f\"Corrected KML coordinates (first 5):\")\n", "    for i in range(min(5, len(pile_xyz_kml_corrected))):\n", "        x, y = pile_xyz_kml_corrected[i, 0], pile_xyz_kml_corrected[i, 1]\n", "        print(f\"  Pile {i}: X={x:.2f}, Y={y:.2f}\")\n", "    \n", "    print(f\"\\nCorrected KML coordinate ranges:\")\n", "    print(f\"  X: {pile_xyz_kml_corrected[:, 0].min():.2f} to {pile_xyz_kml_corrected[:, 0].max():.2f}\")\n", "    print(f\"  Y: {pile_xyz_kml_corrected[:, 1].min():.2f} to {pile_xyz_kml_corrected[:, 1].max():.2f}\")\n", "    \n", "    # Check if corrected coordinates are within point cloud bounds\n", "    pc_bounds = {\n", "        'x_min': points[:, 0].min(), 'x_max': points[:, 0].max(),\n", "        'y_min': points[:, 1].min(), 'y_max': points[:, 1].max()\n", "    }\n", "    \n", "    within_bounds = (\n", "        (pile_xyz_kml_corrected[:, 0] >= pc_bounds['x_min']) & \n", "        (pile_xyz_kml_corrected[:, 0] <= pc_bounds['x_max']) &\n", "        (pile_xyz_kml_corrected[:, 1] >= pc_bounds['y_min']) & \n", "        (pile_xyz_kml_corrected[:, 1] <= pc_bounds['y_max'])\n", "    )\n", "    \n", "    print(f\"\\nKML piles within point cloud bounds: {within_bounds.sum()}/{len(pile_xyz_kml_corrected)}\")\n", "    \n", "else:\n", "    print(\"Cannot process KML - coordinates are not in recognizable format\")\n", "    pile_xyz_kml_corrected = None\n", "    pile_ids_kml_corrected = None\n"]}, {"cell_type": "code", "execution_count": 33, "id": "b2e74afe", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== Z-COORDINATE EXTRACTION ===\n", "\n", "=== EXTRACTING Z-COORDINATES FOR CORRECTED KML ===\n", "  Processed 100/1288 KML piles...\n", "  Processed 200/1288 KML piles...\n", "  Processed 300/1288 KML piles...\n", "  Processed 400/1288 KML piles...\n", "  Processed 500/1288 KML piles...\n", "  Processed 600/1288 KML piles...\n", "  Processed 700/1288 KML piles...\n", "  Processed 800/1288 KML piles...\n", "  Processed 900/1288 KML piles...\n", "  Processed 1000/1288 KML piles...\n", "  Processed 1100/1288 KML piles...\n", "  Processed 1200/1288 KML piles...\n", "Z-extraction stats: {'direct': 1031, 'expanded': 228, 'fallback': 29}\n", "Extracted Z for 1288 KML piles within bounds\n", "Z range: 154.01 to 159.42\n"]}], "source": ["# Step: Extract Z-coordinates for KML piles from point cloud\n", "print(\"=== Z-COORDINATE EXTRACTION ===\")\n", "\n", "if pile_xyz_kml_corrected is not None:\n", "    print(\"\\n=== EXTRACTING Z-COORDINATES FOR CORRECTED KML ===\")\n", "    \n", "    def extract_z_from_pointcloud_robust(xy_coords, points, search_radius=5.0):\n", "        \"\"\"Robust Z extraction with multiple fallback strategies\"\"\"\n", "        point_tree_2d = cKDTree(points[:, :2])\n", "        z_coords = []\n", "        extraction_stats = {'direct': 0, 'expanded': 0, 'fallback': 0}\n", "        \n", "        for i, (x, y) in enumerate(xy_coords):\n", "            # Try normal radius first\n", "            indices = point_tree_2d.query_ball_point([x, y], search_radius)\n", "            \n", "            if len(indices) >= 5:  # Good coverage\n", "                z_coord = np.median(points[indices, 2])\n", "                extraction_stats['direct'] += 1\n", "            elif len(indices) > 0:  # Some points found\n", "                z_coord = np.mean(points[indices, 2])\n", "                extraction_stats['direct'] += 1\n", "            else:\n", "                # Expand search radius\n", "                indices = point_tree_2d.query_ball_point([x, y], search_radius * 3)\n", "                if len(indices) > 0:\n", "                    z_coord = np.median(points[indices, 2])\n", "                    extraction_stats['expanded'] += 1\n", "                else:\n", "                    # Use ground level estimate\n", "                    z_coord = np.percentile(points[:, 2], 10)  # 10th percentile as ground\n", "                    extraction_stats['fallback'] += 1\n", "            \n", "            z_coords.append(z_coord)\n", "            \n", "            if (i + 1) % 100 == 0:\n", "                print(f\"  Processed {i + 1}/{len(xy_coords)} KML piles...\")\n", "        \n", "        print(f\"Z-extraction stats: {extraction_stats}\")\n", "        return np.array(z_coords)\n", "    \n", "    # Only process piles within bounds\n", "    within_bounds_mask = within_bounds\n", "    if within_bounds.sum() > 0:\n", "        kml_coords_in_bounds = pile_xyz_kml_corrected[within_bounds_mask]\n", "        kml_z_coords = extract_z_from_pointcloud_robust(kml_coords_in_bounds[:, :2], points)\n", "        pile_xyz_kml_corrected[within_bounds_mask, 2] = kml_z_coords\n", "        \n", "        print(f\"Extracted Z for {len(kml_z_coords)} KML piles within bounds\")\n", "        print(f\"Z range: {kml_z_coords.min():.2f} to {kml_z_coords.max():.2f}\")\n", "    else:\n", "        print(\"No KML piles within point cloud bounds - cannot extract Z coordinates\")\n"]}, {"cell_type": "code", "execution_count": 34, "id": "73a401fa", "metadata": {}, "outputs": [], "source": ["def match_pile_locations_improved(ifc_coords, kml_coords, ifc_ids, kml_ids, max_distance=10.0):\n", "        \"\"\"Improved pile matching with better distance tolerance\"\"\"\n", "        ifc_tree = cKDTree(ifc_coords[:, :2])\n", "        \n", "        matches = []\n", "        unmatched_ifc = set(range(len(ifc_coords)))\n", "        unmatched_kml = set(range(len(kml_coords)))\n", "        \n", "        # Sort KML piles by distance to closest IFC pile for better matching\n", "        kml_distances = []\n", "        for kml_idx, kml_coord in enumerate(kml_coords):\n", "            distance, _ = ifc_tree.query(kml_coord[:2])\n", "            kml_distances.append((distance, kml_idx))\n", "        \n", "        kml_distances.sort()  # Process closest matches first\n", "        \n", "        for distance, kml_idx in kml_distances:\n", "            if kml_idx in unmatched_kml and distance <= max_distance:\n", "                kml_coord = kml_coords[kml_idx]\n", "                _, ifc_idx = ifc_tree.query(kml_coord[:2])\n", "                \n", "                if ifc_idx in unmatched_ifc:\n", "                    matches.append({\n", "                        'ifc_idx': ifc_idx,\n", "                        'kml_idx': kml_idx,\n", "                        'ifc_id': ifc_ids[ifc_idx],\n", "                        'kml_id': kml_ids[kml_idx],\n", "                        'distance': distance,\n", "                        'ifc_coords': ifc_coords[ifc_idx],\n", "                        'kml_coords': kml_coord\n", "                    })\n", "                    unmatched_ifc.remove(ifc_idx)\n", "                    unmatched_kml.remove(kml_idx)\n", "        \n", "        return matches, list(unmatched_ifc), list(unmatched_kml)\n", "    \n", " "]}, {"cell_type": "code", "execution_count": null, "id": "64db422e", "metadata": {}, "outputs": [], "source": ["#  PILE MATCHING AND HARMO<PERSON>ZATION ⭐\n", "def create_harmonized_dataset(ifc_coords, ifc_ids, kml_coords=None, kml_ids=None, \n", "                            within_bounds_mask=None, max_distance=15.0):\n", "    \"\"\"\n", "    Create harmonized pile dataset with automatic matching and quality assignment\n", "    \"\"\"\n", "    harmonized_piles = []\n", "    \n", "    # Handle case where KML data is available and valid\n", "    if kml_coords is not None and within_bounds_mask is not None and within_bounds_mask.sum() > 0:\n", "        print(f\"=== PILE MATCHING: {len(ifc_coords)} IFC vs {within_bounds_mask.sum()} valid KML piles ===\")\n", "        \n", "        # Filter valid KML data\n", "        kml_coords_valid = kml_coords[within_bounds_mask]\n", "        kml_ids_valid = [kml_ids[i] for i in range(len(kml_ids)) if within_bounds_mask[i]]\n", "        \n", "        # Perform matching\n", "        matches, unmatched_ifc, unmatched_kml = match_pile_locations_improved(\n", "            ifc_coords, kml_coords_valid, ifc_ids, kml_ids_valid, max_distance\n", "        )\n", "        \n", "        print(f\"Matched pairs: {len(matches)}\")\n", "        print(f\"  -> Unmatched IFC: {len(unmatched_ifc)}\")  \n", "        print(f\"  -> Unmatched KML: {len(unmatched_kml)}\")\n", "        \n", "        if matches:\n", "            avg_distance = np.mean([m['distance'] for m in matches])\n", "            print(f\"  -> Average match distance: {avg_distance:.3f}m\")\n", "        \n", "        # Build harmonized dataset\n", "        harmonized_piles.extend([\n", "            create_pile_record(m, 'matched', 'high') for m in matches\n", "        ])\n", "        harmonized_piles.extend([\n", "            create_pile_record({'ifc_idx': idx, 'ifc_id': ifc_ids[idx], \n", "                              'ifc_coords': ifc_coords[idx]}, 'ifc_only', 'medium') \n", "            for idx in unmatched_ifc\n", "        ])\n", "        harmonized_piles.extend([\n", "            create_pile_record({'kml_idx': idx, 'kml_id': kml_ids_valid[idx],\n", "                              'kml_coords': kml_coords_valid[idx]}, 'kml_only', 'low')\n", "            for idx in unmatched_kml  \n", "        ])\n", "        \n", "    else:\n", "        # IFC-only mode\n", "        print(f\"=== IFC-ONLY MODE: {len(ifc_coords)} piles ===\")\n", "        harmonized_piles.extend([\n", "            create_pile_record({'ifc_idx': i, 'ifc_id': ifc_ids[i], \n", "                              'ifc_coords': ifc_coords[i]}, 'ifc_only', 'medium')\n", "            for i in range(len(ifc_coords))\n", "        ])\n", "    \n", "    df = pd.DataFrame(harmonized_piles)\n", "    print(f\"\\n=== Harmonized dataset: {len(df)} piles ===\")\n", "    print(f\"  Sources: {dict(df['source'].value_counts())}\")\n", "    \n", "    return df\n", "\n", "def create_pile_record(match_data, source_type, confidence):\n", "    \"\"\"Create standardized pile record for matched, IFC-only, or KML-only piles.\"\"\"\n", "    \n", "    if source_type not in ['matched', 'ifc_only', 'kml_only']:\n", "        raise ValueError(f\"Invalid source_type: {source_type}\")\n", "    \n", "    record = {\n", "        'pile_id': match_data.get('ifc_id') if source_type != 'kml_only' else f\"KML_{match_data['kml_id']}\",\n", "        'source': source_type,\n", "        'confidence': confidence,\n", "        'match_distance': match_data.get('distance') if source_type == 'matched' else None,\n", "        'kml_match': match_data.get('kml_id') if source_type != 'ifc_only' else None,\n", "    }\n", "    \n", "    # Extract coordinates with fallback handling\n", "    try:\n", "        coords = match_data['ifc_coords'] if source_type != 'kml_only' else match_data['kml_coords']\n", "        record.update({'x': coords[0], 'y': coords[1], 'z': coords[2]})\n", "    except (<PERSON><PERSON><PERSON><PERSON>, IndexError, TypeError) as e:\n", "        raise ValueError(f\"Invalid coordinate data for {source_type}: {e}\")\n", "    \n", "    return record"]}, {"cell_type": "code", "execution_count": null, "id": "90d3f36b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== PILE MATCHING: 14460 IFC vs 1288 valid KML piles ===\n", "Matched pairs: 1125\n", "  -> Unmatched IFC: 13335\n", "  -> Unmatched KML: 163\n", "  -> Average match distance: 1.237m\n", "\n", "=== Harmonized dataset: 14623 piles ===\n", "  Sources: {'ifc_only': 13335, 'matched': 1125, 'kml_only': 163}\n", "\n", "💾 Saved to: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/notebooks/modeling/pile_detection/00_data_preparation/output/ml_patch_data/trino_enel_harmonized_piles_corrected.csv\n"]}], "source": ["if pile_xyz_kml_corrected is not None and within_bounds.sum() > 0:\n", "    harmonized_df_corrected = create_harmonized_dataset(\n", "        ifc_coords=pile_xyz_ifc,\n", "        ifc_ids=pile_ids,\n", "        kml_coords=pile_xyz_kml_corrected, \n", "        kml_ids=pile_ids_kml_corrected,\n", "        within_bounds_mask=within_bounds,\n", "        max_distance=15.0\n", "    )\n", "else:\n", "    harmonized_df_corrected = create_harmonized_dataset(\n", "        ifc_coords=pile_xyz_ifc,\n", "        ifc_ids=pile_ids\n", "    )\n", "\n", "# Save results\n", "corrected_output_path = OUTPUT_DIR / f\"{site_name}_harmonized_piles_corrected.csv\"\n", "harmonized_df_corrected.to_csv(corrected_output_path, index=False)\n", "print(f\"\\n💾 Saved to: {corrected_output_path}\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "c91a001b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== FINAL QUALITY CHECK ON CORRECTED DATA ===\n", "Point density stats (corrected data):\n", "  Mean: 22.4\n", "  Median: 23.0\n", "  Min: 0\n", "  Max: 85\n", "\n", "Piles ready for patch extraction: 3844\n", "SUCCESS: Harmonization corrected - piles ready for ML patch extraction!\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>pile_id</th>\n", "      <th>source</th>\n", "      <th>confidence</th>\n", "      <th>match_distance</th>\n", "      <th>kml_match</th>\n", "      <th>x</th>\n", "      <th>y</th>\n", "      <th>z</th>\n", "      <th>nearby_points</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>40</th>\n", "      <td>TRPL_Tracker Pile:TRPL_Tracker Pile:1249724</td>\n", "      <td>matched</td>\n", "      <td>high</td>\n", "      <td>0.179221</td>\n", "      <td></td>\n", "      <td>435875.197</td>\n", "      <td>5011838.056</td>\n", "      <td>157.524</td>\n", "      <td>34</td>\n", "    </tr>\n", "    <tr>\n", "      <th>58</th>\n", "      <td>TRPL_Tracker Pile:TRPL_Tracker Pile:1249669</td>\n", "      <td>matched</td>\n", "      <td>high</td>\n", "      <td>0.221646</td>\n", "      <td></td>\n", "      <td>435884.697</td>\n", "      <td>5011633.989</td>\n", "      <td>156.829</td>\n", "      <td>31</td>\n", "    </tr>\n", "    <tr>\n", "      <th>64</th>\n", "      <td>TRPL_Tracker Pile:TRPL_Tracker Pile:1249597</td>\n", "      <td>matched</td>\n", "      <td>high</td>\n", "      <td>0.230959</td>\n", "      <td></td>\n", "      <td>435846.697</td>\n", "      <td>5011676.043</td>\n", "      <td>156.899</td>\n", "      <td>36</td>\n", "    </tr>\n", "    <tr>\n", "      <th>66</th>\n", "      <td>TRPL_Tracker Pile:TRPL_Tracker Pile:1249285</td>\n", "      <td>matched</td>\n", "      <td>high</td>\n", "      <td>0.233150</td>\n", "      <td></td>\n", "      <td>435913.197</td>\n", "      <td>5011688.454</td>\n", "      <td>157.224</td>\n", "      <td>42</td>\n", "    </tr>\n", "    <tr>\n", "      <th>76</th>\n", "      <td>TRPL_Tracker Pile:TRPL_Tracker Pile:1249315</td>\n", "      <td>matched</td>\n", "      <td>high</td>\n", "      <td>0.248430</td>\n", "      <td></td>\n", "      <td>435922.697</td>\n", "      <td>5011694.525</td>\n", "      <td>157.241</td>\n", "      <td>42</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                        pile_id   source confidence  \\\n", "40  TRPL_Tracker Pile:TRPL_Tracker Pile:1249724  matched       high   \n", "58  TRPL_Tracker Pile:TRPL_Tracker Pile:1249669  matched       high   \n", "64  TRPL_Tracker Pile:TRPL_Tracker Pile:1249597  matched       high   \n", "66  TRPL_Tracker Pile:TRPL_Tracker Pile:1249285  matched       high   \n", "76  TRPL_Tracker Pile:TRPL_Tracker Pile:1249315  matched       high   \n", "\n", "    match_distance kml_match           x            y        z  nearby_points  \n", "40        0.179221            435875.197  5011838.056  157.524             34  \n", "58        0.221646            435884.697  5011633.989  156.829             31  \n", "64        0.230959            435846.697  5011676.043  156.899             36  \n", "66        0.233150            435913.197  5011688.454  157.224             42  \n", "76        0.248430            435922.697  5011694.525  157.241             42  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "=== HARMONIZATION CORRECTION COMPLETE ===\n"]}], "source": ["# Step: Quality Control and Validation\n", "if 'harmonized_df_corrected' in locals():\n", "    print(\"\\n=== FINAL QUALITY CHECK ON CORRECTED DATA ===\")\n", "    \n", "    # Check point density for corrected harmonized piles\n", "    harmonized_coords_corrected = harmonized_df_corrected[['x', 'y', 'z']].values\n", "    point_counts_corrected = []\n", "    \n", "    for i, pile_coord in enumerate(harmonized_coords_corrected):\n", "        indices = point_tree.query_ball_point(pile_coord, PATCH_RADIUS)\n", "        point_counts_corrected.append(len(indices))\n", "    \n", "    harmonized_df_corrected['nearby_points'] = point_counts_corrected\n", "    \n", "    print(f\"Point density stats (corrected data):\")\n", "    print(f\"  Mean: {np.mean(point_counts_corrected):.1f}\")\n", "    print(f\"  Median: {np.median(point_counts_corrected):.1f}\")\n", "    print(f\"  Min: {np.min(point_counts_corrected)}\")\n", "    print(f\"  Max: {np.max(point_counts_corrected)}\")\n", "    \n", "    ready_for_extraction = harmonized_df_corrected[harmonized_df_corrected['nearby_points'] >= min_points_per_patch]\n", "    print(f\"\\nPiles ready for patch extraction: {len(ready_for_extraction)}\")\n", "    \n", "    if len(ready_for_extraction) > 0:\n", "        print(\"SUCCESS: Harmonization corrected - piles ready for ML patch extraction!\")\n", "        display(ready_for_extraction.head())\n", "    else:\n", "        print(\"Still no piles ready for extraction - may need to reduce min_points_per_patch\")\n", "        \n", "        # Show piles with most points\n", "        top_piles = harmonized_df_corrected.nlargest(10, 'nearby_points')\n", "        print(\"Top 10 piles by point count:\")\n", "        display(top_piles[['pile_id', 'source', 'nearby_points', 'confidence']])\n", "\n", "print(\"\\n=== HARMONIZATION CORRECTION COMPLETE ===\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}