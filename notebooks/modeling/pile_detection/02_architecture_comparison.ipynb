{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Pile Detection Architecture Comparison\n", "\n", "This notebook compares the performance of three pile detection approaches:\n", "1. Rule-based IFC detection\n", "2. PointNet++ deep learning\n", "3. DGCNN deep learning\n", "\n", "**Analysis includes:**\n", "- Performance metrics comparison\n", "- Computational efficiency analysis\n", "- Strengths and limitations assessment\n", "- Recommendations for deployment\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: July 2025  \n", "**Project**: As-Built Foundation Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "from pathlib import Path\n", "\n", "# Add notebooks to path\n", "notebooks_root = Path.cwd().parents[2]\n", "if str(notebooks_root) not in sys.path:\n", "    sys.path.insert(0, str(notebooks_root))\n", "\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import json\n", "from datetime import datetime\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "print(\"=== PILE DETECTION ARCHITECTURE COMPARISON ===\")\n", "print(f\"Analysis date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Load Results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def load_results():\n", "    \"\"\"Load results from all three approaches\"\"\"\n", "    results = {}\n", "    \n", "    # Rule-based results\n", "    rule_based_path = Path(\"rule_based/data/output_runs\")\n", "    training_files = list(rule_based_path.glob(\"*/training_data.json\"))\n", "    if training_files:\n", "        latest_file = max(training_files, key=lambda x: x.parent.name)\n", "        with open(latest_file, 'r') as f:\n", "            rule_data = json.load(f)\n", "        results['rule_based'] = rule_data['performance']\n", "        print(f\"Loaded rule-based results: {latest_file}\")\n", "    \n", "    # PointNet++ results\n", "    pointnet_file = Path(\"ml_based/pointnet_plus_plus/pointnet_plus_plus_results.json\")\n", "    if pointnet_file.exists():\n", "        with open(pointnet_file, 'r') as f:\n", "            pointnet_data = json.load(f)\n", "        results['pointnet_plus_plus'] = pointnet_data['test_metrics']\n", "        print(f\"Loaded PointNet++ results: {pointnet_file}\")\n", "    \n", "    # DGCNN results\n", "    dgcnn_file = Path(\"ml_based/dgcnn/dgcnn_results.json\")\n", "    if dgcnn_file.exists():\n", "        with open(dgcnn_file, 'r') as f:\n", "            dgcnn_data = json.load(f)\n", "        results['dgcnn'] = dgcnn_data['test_metrics']\n", "        print(f\"Loaded DGCNN results: {dgcnn_file}\")\n", "    \n", "    return results\n", "\n", "# Load all results\n", "all_results = load_results()\n", "print(f\"\\nLoaded results for {len(all_results)} methods\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Performance Comparison"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create comparison dataframe\n", "metrics = ['accuracy', 'precision', 'recall', 'f1_score']\n", "comparison_data = []\n", "\n", "for method, results in all_results.items():\n", "    row = {'Method': method.replace('_', ' ').title()}\n", "    for metric in metrics:\n", "        row[metric.replace('_', ' ').title()] = results.get(metric, 0)\n", "    comparison_data.append(row)\n", "\n", "comparison_df = pd.DataFrame(comparison_data)\n", "print(\"=== PERFORMANCE COMPARISON ===\")\n", "print(comparison_df.round(4))\n", "\n", "# Find best performing method for each metric\n", "print(\"\\n=== BEST PERFORMANCE BY METRIC ===\")\n", "for metric in ['Accuracy', 'Precision', 'Recall', 'F1 Score']:\n", "    if metric in comparison_df.columns:\n", "        best_idx = comparison_df[metric].idxmax()\n", "        best_method = comparison_df.loc[best_idx, 'Method']\n", "        best_value = comparison_df.loc[best_idx, metric]\n", "        print(f\"{metric}: {best_method} ({best_value:.4f})\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Visualization\n", "if len(comparison_df) > 0:\n", "    fig, axes = plt.subplots(2, 2, figsize=(12, 10))\n", "    fig.suptitle('Pile Detection Performance Comparison', fontsize=16)\n", "    \n", "    metrics_to_plot = ['Accuracy', 'Precision', 'Recall', 'F1 Score']\n", "    \n", "    for i, metric in enumerate(metrics_to_plot):\n", "        if metric in comparison_df.columns:\n", "            ax = axes[i//2, i%2]\n", "            bars = ax.bar(comparison_df['Method'], comparison_df[metric], \n", "                         color=['skyblue', 'lightcoral', 'lightgreen'][:len(comparison_df)])\n", "            ax.set_title(f'{metric} Comparison')\n", "            ax.set_ylabel(metric)\n", "            ax.set_ylim(0, 1)\n", "            \n", "            # Add value labels on bars\n", "            for bar, value in zip(bars, comparison_df[metric]):\n", "                ax.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,\n", "                       f'{value:.3f}', ha='center', va='bottom')\n", "            \n", "            # Rotate x-axis labels if needed\n", "            ax.tick_params(axis='x', rotation=45)\n", "    \n", "    plt.tight_layout()\n", "    plt.savefig('pile_detection_comparison.png', dpi=300, bbox_inches='tight')\n", "    plt.show()\n", "    print(\"Comparison plot saved as 'pile_detection_comparison.png'\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Detailed Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Calculate improvements over rule-based baseline\n", "if 'rule_based' in all_results:\n", "    baseline_f1 = all_results['rule_based']['f1_score']\n", "    \n", "    print(\"=== IMPROVEMENT OVER RULE-BASED BASELINE ===\")\n", "    print(f\"Rule-based F1-Score: {baseline_f1:.4f}\")\n", "    \n", "    for method, results in all_results.items():\n", "        if method != 'rule_based':\n", "            method_f1 = results['f1_score']\n", "            improvement = ((method_f1 - baseline_f1) / baseline_f1) * 100\n", "            print(f\"{method.replace('_', ' ').title()} F1-Score: {method_f1:.4f} ({improvement:+.1f}%)\")\n", "\n", "# Method characteristics analysis\n", "print(\"\\n=== METHOD CHARACTERISTICS ===\")\n", "\n", "characteristics = {\n", "    'Rule Based': {\n", "        'Training Time': '<PERSON><PERSON> (feature engineering)',\n", "        'Inference Speed': 'Very Fast',\n", "        'Memory Usage': 'Low',\n", "        'Interpretability': 'High',\n", "        'Generalization': 'Limited',\n", "        'Data Requirements': 'Low'\n", "    },\n", "    'Pointnet Plus Plus': {\n", "        'Training Time': 'Moderate (hours)',\n", "        'Inference Speed': 'Fast',\n", "        'Memory Usage': 'Moderate',\n", "        'Interpretability': 'Low',\n", "        'Generalization': 'Good',\n", "        'Data Requirements': 'Moderate'\n", "    },\n", "    'Dgcnn': {\n", "        'Training Time': 'High (hours)',\n", "        'Inference Speed': 'Moderate',\n", "        'Memory Usage': 'High',\n", "        'Interpretability': 'Low',\n", "        'Generalization': 'Very Good',\n", "        'Data Requirements': 'High'\n", "    }\n", "}\n", "\n", "char_df = pd.DataFrame(characteristics).T\n", "print(char_df)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Recommendations"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Generate recommendations based on results\n", "print(\"=== DEPLOYMENT RECOMMENDATIONS ===\")\n", "\n", "if len(all_results) >= 2:\n", "    # Find best overall method\n", "    f1_scores = {method: results['f1_score'] for method, results in all_results.items()}\n", "    best_method = max(f1_scores, key=f1_scores.get)\n", "    best_f1 = f1_scores[best_method]\n", "    \n", "    print(f\"\\n1. BEST OVERALL PERFORMANCE:\")\n", "    print(f\"   {best_method.replace('_', ' ').title()} with F1-Score: {best_f1:.4f}\")\n", "    \n", "    print(f\"\\n2. USE CASE RECOMMENDATIONS:\")\n", "    \n", "    if best_f1 >= 0.9:\n", "        print(f\"   - Production deployment: {best_method.replace('_', ' ').title()}\")\n", "        print(f\"   - Excellent performance suitable for automated systems\")\n", "    elif best_f1 >= 0.8:\n", "        print(f\"   - Semi-automated deployment: {best_method.replace('_', ' ').title()}\")\n", "        print(f\"   - Good performance with human oversight recommended\")\n", "    else:\n", "        print(f\"   - Research/development use: {best_method.replace('_', ' ').title()}\")\n", "        print(f\"   - Further optimization needed for production\")\n", "    \n", "    print(f\"\\n3. COMPUTATIONAL CONSIDERATIONS:\")\n", "    if 'rule_based' in all_results and f1_scores['rule_based'] >= 0.7:\n", "        print(f\"   - Resource-constrained environments: Rule-based approach\")\n", "        print(f\"   - Real-time applications: Rule-based approach\")\n", "    \n", "    if any('pointnet' in method or 'dgcnn' in method for method in all_results.keys()):\n", "        print(f\"   - High-accuracy requirements: Deep learning approaches\")\n", "        print(f\"   - Batch processing: Deep learning approaches\")\n", "    \n", "    print(f\"\\n4. FUTURE IMPROVEMENTS:\")\n", "    print(f\"   - Ensemble methods combining multiple approaches\")\n", "    print(f\"   - Transfer learning from larger datasets\")\n", "    print(f\"   - Active learning for continuous improvement\")\n", "    print(f\"   - Multi-modal fusion (point cloud + imagery)\")\n", "\n", "else:\n", "    print(\"Insufficient results for comprehensive recommendations.\")\n", "    print(\"Please run all three approaches first.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Summary Report"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Generate comprehensive summary\n", "summary_report = {\n", "    'analysis_date': datetime.now().isoformat(),\n", "    'methods_compared': list(all_results.keys()),\n", "    'performance_metrics': comparison_df.to_dict('records') if len(comparison_df) > 0 else [],\n", "    'best_performers': {},\n", "    'recommendations': {\n", "        'production_ready': [],\n", "        'research_stage': [],\n", "        'computational_efficient': []\n", "    }\n", "}\n", "\n", "# Identify best performers\n", "if len(comparison_df) > 0:\n", "    for metric in ['Accuracy', 'Precision', 'Recall', 'F1 Score']:\n", "        if metric in comparison_df.columns:\n", "            best_idx = comparison_df[metric].idxmax()\n", "            summary_report['best_performers'][metric.lower().replace(' ', '_')] = {\n", "                'method': comparison_df.loc[best_idx, 'Method'],\n", "                'value': float(comparison_df.loc[best_idx, metric])\n", "            }\n", "\n", "# Categorize methods\n", "for method, results in all_results.items():\n", "    f1_score = results['f1_score']\n", "    method_name = method.replace('_', ' ').title()\n", "    \n", "    if f1_score >= 0.8:\n", "        summary_report['recommendations']['production_ready'].append(method_name)\n", "    else:\n", "        summary_report['recommendations']['research_stage'].append(method_name)\n", "    \n", "    if method == 'rule_based':\n", "        summary_report['recommendations']['computational_efficient'].append(method_name)\n", "\n", "# Save summary report\n", "with open('pile_detection_comparison_report.json', 'w') as f:\n", "    json.dump(summary_report, f, indent=2)\n", "\n", "print(\"=== ANALYSIS COMPLETE ===\")\n", "print(f\"Summary report saved to: pile_detection_comparison_report.json\")\n", "print(f\"Visualization saved to: pile_detection_comparison.png\")\n", "print(\"\\nAll pile detection approaches have been implemented and compared.\")\n", "print(\"Ready for dissertation documentation and deployment decisions.\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}